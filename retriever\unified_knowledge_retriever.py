#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一知识库检索器 - 整合所有检索类型的统一接口
解决知识库检索系统的所有问题，提供一致的API和完善的错误处理机制
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入各个检索组件
try:
    from retriever.enhanced_knowledge_base import EnhancedKnowledgeBase
    ENHANCED_KB_AVAILABLE = True
except ImportError as e:
    ENHANCED_KB_AVAILABLE = False
    logging.warning(f"增强知识库不可用: {e}")

try:
    from retriever.knowledge_base import KnowledgeBase
    BASIC_KB_AVAILABLE = True
except ImportError as e:
    BASIC_KB_AVAILABLE = False
    logging.warning(f"基础知识库不可用: {e}")

try:
    from core.config_manager import get_config
    CONFIG_AVAILABLE = True
except ImportError as e:
    CONFIG_AVAILABLE = False
    logging.warning(f"配置管理器不可用: {e}")

try:
    from retriever.optimized_retrieval_engine import get_optimized_engine
    OPTIMIZED_ENGINE_AVAILABLE = True
except ImportError as e:
    OPTIMIZED_ENGINE_AVAILABLE = False
    logging.warning(f"优化检索引擎不可用: {e}")

try:
    from retriever.enhanced_multimodal_retriever import get_enhanced_multimodal_retriever
    ENHANCED_MULTIMODAL_AVAILABLE = True
except ImportError as e:
    ENHANCED_MULTIMODAL_AVAILABLE = False
    logging.warning(f"增强多模态检索器不可用: {e}")


class SearchType(Enum):
    """搜索类型枚举"""
    TEXT = "text"
    IMAGE = "image"
    MULTIMODAL = "multimodal"
    ADVANCED = "advanced"
    ENHANCED = "enhanced"
    SEMANTIC = "semantic"
    HYBRID = "hybrid"


class RetrievalStatus(Enum):
    """检索状态枚举"""
    SUCCESS = "success"
    PARTIAL_SUCCESS = "partial_success"
    FAILED = "failed"
    FALLBACK = "fallback"


@dataclass
class SearchResult:
    """标准化搜索结果"""
    id: str
    title: str
    content: str
    score: float
    metadata: Dict[str, Any]
    source: str
    type: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "score": self.score,
            "metadata": self.metadata,
            "source": self.source,
            "type": self.type
        }


@dataclass
class RetrievalResponse:
    """标准化检索响应"""
    success: bool
    status: RetrievalStatus
    results: List[SearchResult]
    total: int
    query: str
    search_type: str
    response_time: float
    message: str = ""
    error: str = ""
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "success": self.success,
            "status": self.status.value,
            "results": [r.to_dict() for r in self.results],
            "total": self.total,
            "query": self.query,
            "search_type": self.search_type,
            "response_time": self.response_time,
            "message": self.message,
            "error": self.error,
            "metadata": self.metadata or {}
        }


class UnifiedKnowledgeRetriever:
    """统一知识库检索器"""
    
    def __init__(self):
        """初始化统一检索器"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个检索组件
        self.enhanced_kb = None
        self.basic_kb = None
        self.config = None
        self.optimized_engine = None
        self.enhanced_multimodal = None

        # 检索器可用性状态
        self.enhanced_available = False
        self.basic_available = False
        self.optimized_engine_available = False
        self.enhanced_multimodal_available = False
        
        # 初始化组件
        self._initialize_components()
        
        # 检索策略配置
        self.retrieval_strategies = {
            SearchType.ENHANCED: self._enhanced_search,
            SearchType.ADVANCED: self._advanced_search,
            SearchType.SEMANTIC: self._semantic_search,
            SearchType.TEXT: self._text_search,
            SearchType.IMAGE: self._image_search,
            SearchType.MULTIMODAL: self._multimodal_search,
            SearchType.HYBRID: self._hybrid_search
        }
        
        self.logger.info("统一知识库检索器初始化完成")
    
    def _initialize_components(self):
        """初始化各个检索组件"""
        try:
            # 初始化配置
            if CONFIG_AVAILABLE:
                self.config = get_config()
                self.logger.info("配置管理器初始化成功")
            
            # 初始化增强知识库
            if ENHANCED_KB_AVAILABLE:
                try:
                    self.enhanced_kb = EnhancedKnowledgeBase()
                    self.enhanced_available = self.enhanced_kb.is_available
                    if self.enhanced_available:
                        self.logger.info("✅ 增强知识库初始化成功")
                    else:
                        self.logger.warning("⚠️ 增强知识库初始化失败")
                except Exception as e:
                    self.logger.error(f"增强知识库初始化异常: {e}")
            
            # 初始化基础知识库
            if BASIC_KB_AVAILABLE and self.config:
                try:
                    self.basic_kb = KnowledgeBase(self.config._config)
                    self.basic_available = True
                    self.logger.info("✅ 基础知识库初始化成功")
                except Exception as e:
                    self.logger.error(f"基础知识库初始化失败: {e}")
                    self.basic_available = False

            # 初始化优化检索引擎
            if OPTIMIZED_ENGINE_AVAILABLE:
                try:
                    self.optimized_engine = get_optimized_engine()
                    self.optimized_engine_available = True
                    self.logger.info("✅ 优化检索引擎初始化成功")
                except Exception as e:
                    self.logger.error(f"优化检索引擎初始化失败: {e}")
                    self.optimized_engine_available = False

            # 初始化增强多模态检索器
            if ENHANCED_MULTIMODAL_AVAILABLE:
                try:
                    self.enhanced_multimodal = get_enhanced_multimodal_retriever()
                    self.enhanced_multimodal_available = True
                    self.logger.info("✅ 增强多模态检索器初始化成功")
                except Exception as e:
                    self.logger.error(f"增强多模态检索器初始化失败: {e}")
                    self.enhanced_multimodal_available = False

        except Exception as e:
            self.logger.error(f"组件初始化失败: {e}")
    
    def search(self, 
               query: str, 
               search_type: Union[str, SearchType] = SearchType.HYBRID,
               limit: int = 10,
               **kwargs) -> RetrievalResponse:
        """
        统一搜索接口
        
        Args:
            query: 搜索查询
            search_type: 搜索类型
            limit: 返回结果数量限制
            **kwargs: 其他搜索参数
            
        Returns:
            标准化检索响应
        """
        start_time = time.time()
        
        # 参数验证
        if not query or not query.strip():
            return RetrievalResponse(
                success=False,
                status=RetrievalStatus.FAILED,
                results=[],
                total=0,
                query=query,
                search_type=str(search_type),
                response_time=time.time() - start_time,
                error="查询文本不能为空"
            )
        
        # 转换搜索类型
        if isinstance(search_type, str):
            try:
                search_type = SearchType(search_type.lower())
            except ValueError:
                search_type = SearchType.HYBRID
        
        self.logger.info(f"🔍 开始搜索: '{query[:50]}...', 类型: {search_type.value}")
        
        try:
            # 选择检索策略
            strategy = self.retrieval_strategies.get(search_type, self._hybrid_search)
            
            # 执行搜索
            response = strategy(query, limit, **kwargs)
            response.response_time = time.time() - start_time
            
            self.logger.info(f"✅ 搜索完成: 返回 {response.total} 个结果, 耗时 {response.response_time:.2f}s")
            return response
            
        except Exception as e:
            self.logger.error(f"❌ 搜索失败: {e}")
            return RetrievalResponse(
                success=False,
                status=RetrievalStatus.FAILED,
                results=[],
                total=0,
                query=query,
                search_type=search_type.value,
                response_time=time.time() - start_time,
                error=f"搜索执行失败: {str(e)}"
            )
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        status = {
            "enhanced_kb_available": self.enhanced_available,
            "basic_kb_available": self.basic_available,
            "optimized_engine_available": self.optimized_engine_available,
            "enhanced_multimodal_available": self.enhanced_multimodal_available,
            "config_available": CONFIG_AVAILABLE,
            "supported_search_types": [t.value for t in SearchType],
            "total_retrievers": sum([
                self.enhanced_available,
                self.basic_available
            ]),
            "optimization_enabled": self.optimized_engine_available,
            "multimodal_enhanced": self.enhanced_multimodal_available
        }

        # 添加多模态检索器状态
        if self.enhanced_multimodal_available:
            multimodal_status = self.enhanced_multimodal.get_system_status()
            status["multimodal_stats"] = multimodal_status

        return status

    def _normalize_results(self, raw_results: Any, source: str, search_type: str, query: str = "") -> List[SearchResult]:
        """标准化检索结果格式（使用优化检索引擎）"""
        normalized_results = []

        try:
            # 处理不同格式的原始结果
            if isinstance(raw_results, dict):
                if "results" in raw_results:
                    results_data = raw_results["results"]
                else:
                    results_data = [raw_results]
            elif isinstance(raw_results, list):
                results_data = raw_results
            else:
                return []

            # 使用优化检索引擎进行评分优化
            if self.optimized_engine_available and query:
                try:
                    optimized_results = self.optimized_engine.optimize_search_results(query, results_data)

                    for opt_result in optimized_results:
                        normalized_result = SearchResult(
                            id=opt_result.id or f"{source}_{len(normalized_results)}",
                            title=opt_result.title,
                            content=opt_result.content,
                            score=opt_result.optimized_score,
                            metadata={
                                **opt_result.metadata,
                                "raw_score": opt_result.raw_score,
                                "relevance_factors": opt_result.relevance_factors,
                                "optimization_applied": True
                            },
                            source=opt_result.source or source,
                            type=opt_result.search_type or search_type
                        )
                        normalized_results.append(normalized_result)

                    self.logger.debug(f"使用优化引擎处理了 {len(optimized_results)} 个结果")
                    return normalized_results

                except Exception as e:
                    self.logger.warning(f"优化引擎处理失败，使用基础标准化: {e}")

            # 基础标准化（回退方案）
            for i, result in enumerate(results_data):
                if isinstance(result, dict):
                    normalized_result = SearchResult(
                        id=result.get("id", f"{source}_{i}"),
                        title=result.get("title", result.get("content", "")[:100]),
                        content=result.get("content", ""),
                        score=float(result.get("score", result.get("final_score", 0))),
                        metadata={
                            **result.get("metadata", {}),
                            "optimization_applied": False
                        },
                        source=result.get("source", source),
                        type=result.get("type", search_type)
                    )
                    normalized_results.append(normalized_result)

        except Exception as e:
            self.logger.error(f"结果标准化失败: {e}")

        return normalized_results

    def _enhanced_search(self, query: str, limit: int, **kwargs) -> RetrievalResponse:
        """增强知识库搜索"""
        if not self.enhanced_available:
            return self._fallback_search(query, limit, "增强知识库不可用")

        try:
            raw_results = self.enhanced_kb.search(query, "advanced", limit)

            if raw_results.get("success"):
                results = self._normalize_results(raw_results, "增强知识库", "enhanced", query)
                return RetrievalResponse(
                    success=True,
                    status=RetrievalStatus.SUCCESS,
                    results=results,
                    total=len(results),
                    query=query,
                    search_type="enhanced",
                    response_time=0,
                    message=f"增强搜索成功，返回 {len(results)} 个结果"
                )
            else:
                return self._fallback_search(query, limit, raw_results.get("error", "增强搜索失败"))

        except Exception as e:
            return self._fallback_search(query, limit, f"增强搜索异常: {str(e)}")

    def _advanced_search(self, query: str, limit: int, **kwargs) -> RetrievalResponse:
        """高级搜索（优先使用增强知识库）"""
        return self._enhanced_search(query, limit, **kwargs)

    def _semantic_search(self, query: str, limit: int, **kwargs) -> RetrievalResponse:
        """语义搜索"""
        if self.enhanced_available:
            return self._enhanced_search(query, limit, **kwargs)
        else:
            return self._text_search(query, limit, **kwargs)

    def _text_search(self, query: str, limit: int, **kwargs) -> RetrievalResponse:
        """文本搜索"""
        if not self.basic_available:
            return self._create_mock_response(query, limit, "基础知识库不可用")

        try:
            raw_results = self.basic_kb.search(query, "text", limit)

            if raw_results.get("type") != "error":
                results = self._normalize_results(raw_results.get("results", []), "基础知识库", "text", query)
                return RetrievalResponse(
                    success=True,
                    status=RetrievalStatus.SUCCESS if results else RetrievalStatus.PARTIAL_SUCCESS,
                    results=results,
                    total=len(results),
                    query=query,
                    search_type="text",
                    response_time=0,
                    message=f"文本搜索完成，返回 {len(results)} 个结果"
                )
            else:
                return self._create_mock_response(query, limit, "文本搜索失败")

        except Exception as e:
            return self._create_mock_response(query, limit, f"文本搜索异常: {str(e)}")

    def _image_search(self, query: str, limit: int, **kwargs) -> RetrievalResponse:
        """图像搜索（使用增强多模态检索器）"""
        # 优先使用增强多模态检索器
        if self.enhanced_multimodal_available:
            try:
                image_results = self.enhanced_multimodal.search_images(query, limit)

                if image_results:
                    # 转换为标准格式
                    results = []
                    for img_result in image_results:
                        result = SearchResult(
                            id=img_result.id,
                            title=img_result.filename,
                            content=img_result.matched_content,
                            score=img_result.score,
                            metadata={
                                **img_result.metadata,
                                "match_type": img_result.match_type,
                                "confidence": img_result.confidence,
                                "image_path": img_result.path
                            },
                            source="增强多模态检索器",
                            type="image"
                        )
                        results.append(result)

                    return RetrievalResponse(
                        success=True,
                        status=RetrievalStatus.SUCCESS,
                        results=results,
                        total=len(results),
                        query=query,
                        search_type="image",
                        response_time=0,
                        message=f"增强图像搜索完成，返回 {len(results)} 个结果"
                    )
                else:
                    return RetrievalResponse(
                        success=True,
                        status=RetrievalStatus.PARTIAL_SUCCESS,
                        results=[],
                        total=0,
                        query=query,
                        search_type="image",
                        response_time=0,
                        message="未找到相关图像"
                    )

            except Exception as e:
                self.logger.warning(f"增强图像搜索失败，尝试基础搜索: {e}")

        # 回退到基础知识库
        if self.basic_available:
            try:
                raw_results = self.basic_kb.search(query, "image", limit)

                if raw_results.get("type") != "error":
                    results = self._normalize_results(raw_results.get("results", []), "基础知识库", "image", query)
                    return RetrievalResponse(
                        success=True,
                        status=RetrievalStatus.FALLBACK,
                        results=results,
                        total=len(results),
                        query=query,
                        search_type="image",
                        response_time=0,
                        message=f"基础图像搜索完成，返回 {len(results)} 个结果"
                    )
                else:
                    return self._create_mock_response(query, limit, "图像搜索失败")

            except Exception as e:
                return self._create_mock_response(query, limit, f"图像搜索异常: {str(e)}")

        return self._create_mock_response(query, limit, "图像搜索功能不可用")

    def _multimodal_search(self, query: str, limit: int, **kwargs) -> RetrievalResponse:
        """多模态搜索"""
        if not self.basic_available:
            return self._create_mock_response(query, limit, "基础知识库不可用")

        try:
            raw_results = self.basic_kb.search(query, "multimodal", limit)

            if raw_results.get("type") != "error":
                results = self._normalize_results(raw_results.get("results", []), "基础知识库", "multimodal")
                return RetrievalResponse(
                    success=True,
                    status=RetrievalStatus.SUCCESS if results else RetrievalStatus.PARTIAL_SUCCESS,
                    results=results,
                    total=len(results),
                    query=query,
                    search_type="multimodal",
                    response_time=0,
                    message=f"多模态搜索完成，返回 {len(results)} 个结果"
                )
            else:
                return self._create_mock_response(query, limit, "多模态搜索失败")

        except Exception as e:
            return self._create_mock_response(query, limit, f"多模态搜索异常: {str(e)}")

    def _hybrid_search(self, query: str, limit: int, **kwargs) -> RetrievalResponse:
        """混合搜索（智能选择最佳策略）"""
        all_results = []
        search_attempts = []

        # 策略1: 优先尝试增强搜索
        if self.enhanced_available:
            try:
                enhanced_response = self._enhanced_search(query, limit // 2, **kwargs)
                if enhanced_response.success and enhanced_response.results:
                    all_results.extend(enhanced_response.results)
                    search_attempts.append(("enhanced", len(enhanced_response.results)))
            except Exception as e:
                self.logger.warning(f"增强搜索失败: {e}")

        # 策略2: 文本搜索补充
        if self.basic_available and len(all_results) < limit:
            try:
                text_response = self._text_search(query, limit - len(all_results), **kwargs)
                if text_response.success and text_response.results:
                    all_results.extend(text_response.results)
                    search_attempts.append(("text", len(text_response.results)))
            except Exception as e:
                self.logger.warning(f"文本搜索失败: {e}")

        # 策略3: 多模态搜索补充
        if self.basic_available and len(all_results) < limit:
            try:
                multimodal_response = self._multimodal_search(query, limit - len(all_results), **kwargs)
                if multimodal_response.success and multimodal_response.results:
                    all_results.extend(multimodal_response.results)
                    search_attempts.append(("multimodal", len(multimodal_response.results)))
            except Exception as e:
                self.logger.warning(f"多模态搜索失败: {e}")

        # 结果去重和排序
        unique_results = self._deduplicate_results(all_results)
        sorted_results = sorted(unique_results, key=lambda x: x.score, reverse=True)[:limit]

        # 确定状态
        if sorted_results:
            status = RetrievalStatus.SUCCESS if len(search_attempts) > 0 else RetrievalStatus.PARTIAL_SUCCESS
            message = f"混合搜索完成，使用策略: {', '.join([f'{s}({c})' for s, c in search_attempts])}"
        else:
            return self._create_mock_response(query, limit, "所有搜索策略都失败")

        return RetrievalResponse(
            success=True,
            status=status,
            results=sorted_results,
            total=len(sorted_results),
            query=query,
            search_type="hybrid",
            response_time=0,
            message=message,
            metadata={"search_attempts": search_attempts}
        )

    def _deduplicate_results(self, results: List[SearchResult]) -> List[SearchResult]:
        """结果去重"""
        seen_ids = set()
        unique_results = []

        for result in results:
            # 使用内容的前100个字符作为去重标识
            content_key = result.content[:100] if result.content else result.title[:100]
            if content_key not in seen_ids:
                seen_ids.add(content_key)
                unique_results.append(result)

        return unique_results

    def _fallback_search(self, query: str, limit: int, error_msg: str) -> RetrievalResponse:
        """回退搜索机制"""
        self.logger.warning(f"执行回退搜索: {error_msg}")

        # 尝试基础知识库
        if self.basic_available:
            try:
                return self._text_search(query, limit)
            except Exception as e:
                self.logger.error(f"回退搜索也失败: {e}")

        # 最后的回退：创建模拟结果
        return self._create_mock_response(query, limit, f"回退搜索失败: {error_msg}")

    def _create_mock_response(self, query: str, limit: int, error_msg: str) -> RetrievalResponse:
        """创建模拟响应（当所有检索都失败时）"""
        mock_result = SearchResult(
            id="mock_result_1",
            title=f"关于'{query}'的相关信息",
            content=f"抱歉，当前搜索系统遇到问题：{error_msg}。请稍后重试或联系技术支持。",
            score=0.1,
            metadata={"type": "mock", "error": error_msg},
            source="系统提示",
            type="mock"
        )

        return RetrievalResponse(
            success=True,
            status=RetrievalStatus.FALLBACK,
            results=[mock_result],
            total=1,
            query=query,
            search_type="fallback",
            response_time=0,
            message="使用模拟结果",
            error=error_msg
        )

    def health_check(self) -> Dict[str, Any]:
        """系统健康检查"""
        health_status = {
            "overall_status": "healthy",
            "components": {},
            "issues": [],
            "recommendations": []
        }

        # 检查增强知识库
        if self.enhanced_available:
            health_status["components"]["enhanced_kb"] = "healthy"
        else:
            health_status["components"]["enhanced_kb"] = "unavailable"
            health_status["issues"].append("增强知识库不可用")
            health_status["recommendations"].append("检查增强知识库配置和依赖")

        # 检查基础知识库
        if self.basic_available:
            health_status["components"]["basic_kb"] = "healthy"
        else:
            health_status["components"]["basic_kb"] = "unavailable"
            health_status["issues"].append("基础知识库不可用")
            health_status["recommendations"].append("检查基础知识库配置")

        # 检查配置
        if CONFIG_AVAILABLE:
            health_status["components"]["config"] = "healthy"
        else:
            health_status["components"]["config"] = "unavailable"
            health_status["issues"].append("配置管理器不可用")
            health_status["recommendations"].append("检查配置文件")

        # 确定整体状态
        if not self.enhanced_available and not self.basic_available:
            health_status["overall_status"] = "critical"
        elif len(health_status["issues"]) > 0:
            health_status["overall_status"] = "degraded"

        return health_status


# 全局实例
_unified_retriever = None

def get_unified_retriever() -> UnifiedKnowledgeRetriever:
    """获取统一检索器实例（单例模式）"""
    global _unified_retriever
    if _unified_retriever is None:
        _unified_retriever = UnifiedKnowledgeRetriever()
    return _unified_retriever
