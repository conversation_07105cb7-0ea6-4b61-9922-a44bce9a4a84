#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
白银电力系统数据准备脚本
实现数据收集、清洗、标准化和结构化处理
不依赖复杂的ML库，专注于数据准备阶段
"""

import os
import sys
import json
import re
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class BaiyinDataPreparator:
    """白银电力系统数据准备器"""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        
        # 电力专业术语标准化映射
        self.power_terms_mapping = {
            # 设备类型标准化
            '变压器': ['变压器', '主变', '配变', '所变', 'transformer'],
            '断路器': ['断路器', '开关', 'breaker', 'CB'],
            '隔离开关': ['隔离开关', '刀闸', 'isolator', 'DS'],
            '电流互感器': ['电流互感器', 'CT', 'current transformer'],
            '电压互感器': ['电压互感器', 'PT', 'VT', 'voltage transformer'],
            '避雷器': ['避雷器', '氧化锌避雷器', 'arrester'],
            
            # 故障类型标准化
            '短路故障': ['短路', '接地短路', '相间短路', 'short circuit'],
            '接地故障': ['接地', '单相接地', 'ground fault'],
            '过载故障': ['过载', '过负荷', 'overload'],
            '绝缘故障': ['绝缘击穿', '绝缘老化', 'insulation fault'],
            '机械故障': ['机械卡涩', '操作机构故障', 'mechanical fault'],
        }
        
        # 敏感信息脱敏规则
        self.sensitive_patterns = {
            "equipment_id": r'[A-Z]{2,4}\d{6,12}',
            "person_name": r'[\u4e00-\u9fa5]{2,4}(?=\s|$|，|。)',
            "phone_number": r'1[3-9]\d{9}',
            "detailed_address": r'[\u4e00-\u9fa5]+(?:市|县|区|镇|村|路|街|号)\d*号?',
            "ip_address": r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
        }
        
        # 处理统计
        self.stats = {
            "total_files": 0,
            "processed_files": 0,
            "failed_files": 0,
            "total_documents": 0,
            "standardized_terms": 0,
            "anonymized_items": 0,
            "start_time": None,
            "end_time": None
        }

    def standardize_text(self, text: str) -> str:
        """标准化文本内容"""
        if not text:
            return ""
        
        result = text
        
        # 标准化电力术语
        for standard_term, variants in self.power_terms_mapping.items():
            for variant in variants:
                if variant != standard_term:
                    pattern = r'\b' + re.escape(variant) + r'\b'
                    if re.search(pattern, result, re.IGNORECASE):
                        result = re.sub(pattern, standard_term, result, flags=re.IGNORECASE)
                        self.stats["standardized_terms"] += 1
        
        # 标准化技术参数格式
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[kK][vV]', r'\1kV', result)
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[mM][vV]', r'\1MV', result)
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[kK][wW]', r'\1kW', result)
        result = re.sub(r'(\d+(?:\.\d+)?)\s*[mM][wW]', r'\1MW', result)
        
        # 清理格式
        result = re.sub(r'\s+', ' ', result)
        result = result.strip()
        
        return result

    def anonymize_text(self, text: str) -> str:
        """脱敏处理文本"""
        if not text:
            return ""
        
        result = text
        
        # 设备ID脱敏
        def anonymize_equipment_id(match):
            original = match.group(0)
            return f"EQ_{hashlib.md5(original.encode()).hexdigest()[:8].upper()}"
        
        # 人员姓名脱敏
        def anonymize_person_name(match):
            original = match.group(0)
            # 保留技术术语，不脱敏
            technical_terms = {'变压器', '断路器', '隔离开关', '电流互感器', '电压互感器'}
            if original in technical_terms:
                return original
            return f"用户{hashlib.md5(original.encode()).hexdigest()[:4].upper()}"
        
        # 地址脱敏
        def anonymize_address(match):
            original = match.group(0)
            if "白银" in original:
                return "白银市***区域"
            return "***地区"
        
        # 应用脱敏规则
        anonymization_rules = {
            "equipment_id": anonymize_equipment_id,
            "person_name": anonymize_person_name,
            "detailed_address": anonymize_address,
            "phone_number": lambda m: f"138****{m.group(0)[-4:]}",
            "ip_address": lambda m: f"{'.'.join(m.group(0).split('.')[:2])}.***.***.***",
        }
        
        for pattern_name, pattern in self.sensitive_patterns.items():
            if pattern_name in anonymization_rules:
                anonymizer = anonymization_rules[pattern_name]
                matches = re.findall(pattern, result)
                if matches:
                    result = re.sub(pattern, anonymizer, result)
                    self.stats["anonymized_items"] += len(matches)
        
        return result

    def extract_structured_info(self, text: str, source: str) -> Dict[str, Any]:
        """提取结构化信息"""
        structured_info = {
            "equipment_types": [],
            "fault_types": [],
            "voltage_levels": [],
            "locations": [],
            "technical_parameters": {}
        }
        
        # 提取设备类型
        for equipment_type in self.power_terms_mapping.keys():
            if equipment_type in text:
                structured_info["equipment_types"].append(equipment_type)
        
        # 提取电压等级
        voltage_matches = re.findall(r'(\d+(?:\.\d+)?)\s*[kKmM]?[vV]', text)
        for voltage in voltage_matches:
            try:
                voltage_val = float(voltage)
                if voltage_val >= 1000:
                    structured_info["voltage_levels"].append(f"{voltage_val/1000:.0f}kV")
                else:
                    structured_info["voltage_levels"].append(f"{voltage_val:.0f}V")
            except:
                pass
        
        # 提取故障类型关键词
        fault_keywords = {
            "短路故障": ["短路", "接地短路", "相间短路"],
            "接地故障": ["接地", "单相接地"],
            "过载故障": ["过载", "过负荷"],
            "绝缘故障": ["绝缘击穿", "绝缘老化"],
            "机械故障": ["机械卡涩", "操作机构故障"]
        }
        
        for fault_type, keywords in fault_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    structured_info["fault_types"].append(fault_type)
                    break
        
        # 提取地点信息
        if "白银" in text:
            structured_info["locations"].append("白银")
        
        # 去重
        for key in ["equipment_types", "fault_types", "voltage_levels", "locations"]:
            structured_info[key] = list(set(structured_info[key]))
        
        return structured_info

    def chunk_text(self, text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
        """文本分块处理"""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 尝试在句号处分割
            if end < len(text):
                # 寻找最近的句号
                sentence_end = text.rfind('。', start, end)
                if sentence_end > start:
                    end = sentence_end + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= len(text):
                break
        
        return chunks

    def process_file(self, file_path: Path) -> List[Dict[str, Any]]:
        """处理单个文件"""
        try:
            documents = []
            
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 提取JSON中的文本内容
                text_content = self._extract_text_from_json(data)
                
            elif file_path.suffix.lower() in ['.md', '.txt']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    text_content = f.read()
            
            else:
                logger.warning(f"不支持的文件类型: {file_path.suffix}")
                return []
            
            if not text_content.strip():
                return []
            
            # 数据处理流程
            # 1. 标准化
            standardized_text = self.standardize_text(text_content)
            
            # 2. 脱敏
            anonymized_text = self.anonymize_text(standardized_text)
            
            # 3. 提取结构化信息
            structured_info = self.extract_structured_info(anonymized_text, str(file_path))
            
            # 4. 文本分块
            chunks = self.chunk_text(anonymized_text)
            
            # 5. 构建文档
            for i, chunk in enumerate(chunks):
                doc = {
                    "id": f"{file_path.stem}_{i}",
                    "source": str(file_path),
                    "content": chunk,
                    "chunk_id": i,
                    "total_chunks": len(chunks),
                    "metadata": {
                        "file_type": file_path.suffix.lower(),
                        "location": "白银",
                        "processed_at": datetime.now().isoformat(),
                        **structured_info
                    }
                }
                documents.append(doc)
            
            self.stats["processed_files"] += 1
            self.stats["total_documents"] += len(documents)
            logger.info(f"处理文件成功: {file_path}, 生成 {len(documents)} 个文档块")
            
            return documents
            
        except Exception as e:
            logger.error(f"处理文件失败 {file_path}: {e}")
            self.stats["failed_files"] += 1
            return []

    def _extract_text_from_json(self, data: Any) -> str:
        """从JSON数据中提取文本内容"""
        if isinstance(data, str):
            return data
        elif isinstance(data, dict):
            text_parts = []
            for key, value in data.items():
                if isinstance(value, str) and len(value) > 10:
                    text_parts.append(f"{key}: {value}")
                elif isinstance(value, (dict, list)):
                    sub_text = self._extract_text_from_json(value)
                    if sub_text:
                        text_parts.append(sub_text)
            return "\n".join(text_parts)
        elif isinstance(data, list):
            text_parts = []
            for item in data:
                sub_text = self._extract_text_from_json(item)
                if sub_text:
                    text_parts.append(sub_text)
            return "\n".join(text_parts)
        else:
            return str(data)

    def process_baiyin_data(self) -> bool:
        """处理白银电力系统数据"""
        try:
            self.stats["start_time"] = datetime.now()
            logger.info("开始处理白银电力系统数据...")
            
            # 收集所有文档
            all_documents = []
            
            # 处理数据目录
            data_dirs = ["data/structured", "data/integrated", "data/raw"]
            supported_extensions = ['.json', '.md', '.txt']
            
            for data_dir in data_dirs:
                data_path = self.base_path / data_dir
                if data_path.exists():
                    logger.info(f"处理目录: {data_path}")
                    
                    for file_path in data_path.rglob("*"):
                        if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                            self.stats["total_files"] += 1
                            documents = self.process_file(file_path)
                            all_documents.extend(documents)
            
            # 保存处理结果
            output_dir = self.base_path / "data/processed"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存处理后的文档
            output_file = output_dir / "baiyin_prepared_documents.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_documents, f, ensure_ascii=False, indent=2)
            
            # 生成统计报告
            self._generate_report(output_dir)
            
            self.stats["end_time"] = datetime.now()
            logger.info("白银电力系统数据处理完成！")
            
            return True
            
        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            return False

    def _generate_report(self, output_dir: Path):
        """生成处理报告"""
        try:
            duration = (datetime.now() - self.stats["start_time"]).total_seconds()
            
            report = f"""
# 白银电力系统数据准备报告

## 处理统计
- 开始时间: {self.stats["start_time"].strftime('%Y-%m-%d %H:%M:%S')}
- 处理耗时: {duration:.2f} 秒
- 总文件数: {self.stats["total_files"]}
- 成功处理: {self.stats["processed_files"]}
- 失败文件: {self.stats["failed_files"]}
- 生成文档块: {self.stats["total_documents"]}
- 标准化术语: {self.stats["standardized_terms"]} 个
- 脱敏项目: {self.stats["anonymized_items"]} 个

## 处理流程
1. ✅ 数据收集和预处理
2. ✅ 电力术语标准化
3. ✅ 敏感信息脱敏
4. ✅ 结构化信息提取
5. ✅ 文本分块处理
6. ✅ 元数据构建

## 数据质量
- 处理成功率: {(self.stats['processed_files'] / max(self.stats['total_files'], 1) * 100):.1f}%
- 数据完整性: {'良好' if self.stats['failed_files'] == 0 else '一般'}
- 标准化覆盖: {'高' if self.stats['standardized_terms'] > 100 else '中等'}

## 下一步建议
1. 安装向量化依赖: pip install sentence-transformers chromadb
2. 运行向量化处理: python scripts/vector_processing.py
3. 构建检索索引: python scripts/build_index.py
4. 测试检索效果: python scripts/test_retrieval.py

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            report_file = output_dir / "data_preparation_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"处理报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"生成报告失败: {e}")


def main():
    """主函数"""
    preparator = BaiyinDataPreparator()
    
    logger.info("开始白银电力系统数据准备...")
    success = preparator.process_baiyin_data()
    
    if success:
        print("✅ 数据准备成功完成！")
        print(f"📊 处理了 {preparator.stats['total_files']} 个文件")
        print(f"📝 生成了 {preparator.stats['total_documents']} 个文档块")
        print(f"🔧 标准化了 {preparator.stats['standardized_terms']} 个术语")
        print(f"🔒 脱敏了 {preparator.stats['anonymized_items']} 个敏感项")
        print("📁 处理结果已保存到 data/processed/ 目录")
        print("\n下一步: 安装向量化依赖并运行向量处理")
    else:
        print("❌ 数据准备失败，请检查日志")
        sys.exit(1)


if __name__ == "__main__":
    main()
