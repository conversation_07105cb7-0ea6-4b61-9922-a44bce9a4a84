"""
HuggingFace Hub兼容性修复模块
处理不同版本的huggingface_hub导入问题
"""

import logging
import sys
import os
from typing import Optional, Any

logger = logging.getLogger(__name__)

# 全局修复标志
_PATCHED = False

def get_cached_download_function():
    """
    获取兼容的cached_download函数
    处理不同版本的huggingface_hub API变化
    """
    try:
        # 尝试新版本的导入方式
        from huggingface_hub import hf_hub_download
        logger.info("✅ 使用新版本 huggingface_hub.hf_hub_download")
        return hf_hub_download
    except ImportError:
        pass
    
    try:
        # 尝试旧版本的导入方式
        from huggingface_hub import cached_download
        logger.info("✅ 使用旧版本 huggingface_hub.cached_download")
        return cached_download
    except ImportError:
        pass
    
    try:
        # 尝试从file_download模块导入
        from huggingface_hub.file_download import hf_hub_download
        logger.info("✅ 使用 huggingface_hub.file_download.hf_hub_download")
        return hf_hub_download
    except ImportError:
        pass
    
    # 如果都失败了，返回一个模拟函数
    logger.warning("⚠️ 无法导入huggingface_hub下载函数，使用模拟函数")
    
    def mock_download(*args, **kwargs):
        """模拟下载函数"""
        logger.warning("模拟下载函数被调用，实际未下载任何文件")
        return None
    
    return mock_download

def get_snapshot_download_function():
    """
    获取兼容的snapshot_download函数
    """
    try:
        from huggingface_hub import snapshot_download
        logger.info("✅ 使用 huggingface_hub.snapshot_download")
        return snapshot_download
    except ImportError:
        logger.warning("⚠️ 无法导入snapshot_download，使用模拟函数")
        
        def mock_snapshot_download(*args, **kwargs):
            """模拟快照下载函数"""
            logger.warning("模拟快照下载函数被调用，实际未下载任何文件")
            return None
        
        return mock_snapshot_download

def get_model_info_function():
    """
    获取兼容的model_info函数
    """
    try:
        from huggingface_hub import model_info
        logger.info("✅ 使用 huggingface_hub.model_info")
        return model_info
    except ImportError:
        logger.warning("⚠️ 无法导入model_info，使用模拟函数")
        
        def mock_model_info(*args, **kwargs):
            """模拟模型信息函数"""
            logger.warning("模拟模型信息函数被调用")
            return {"error": "model_info not available"}
        
        return mock_model_info

def safe_import_huggingface_hub():
    """
    安全导入huggingface_hub相关功能
    返回一个包含所有兼容函数的字典
    """
    return {
        'cached_download': get_cached_download_function(),
        'hf_hub_download': get_cached_download_function(),  # 别名
        'snapshot_download': get_snapshot_download_function(),
        'model_info': get_model_info_function()
    }

# 全局兼容性对象
HF_HUB_COMPAT = safe_import_huggingface_hub()

# 便捷访问函数
def hf_hub_download(*args, **kwargs):
    """兼容的hf_hub_download函数"""
    return HF_HUB_COMPAT['hf_hub_download'](*args, **kwargs)

def cached_download(*args, **kwargs):
    """兼容的cached_download函数"""
    return HF_HUB_COMPAT['cached_download'](*args, **kwargs)

def snapshot_download(*args, **kwargs):
    """兼容的snapshot_download函数"""
    return HF_HUB_COMPAT['snapshot_download'](*args, **kwargs)

def model_info(*args, **kwargs):
    """兼容的model_info函数"""
    return HF_HUB_COMPAT['model_info'](*args, **kwargs)



def patch_huggingface_hub():
    """
    全局修补huggingface_hub模块
    在导入时自动修复cached_download问题
    """
    global _PATCHED

    if _PATCHED:
        return

    try:
        import huggingface_hub

        # 检查是否已经有cached_download
        if hasattr(huggingface_hub, 'cached_download'):
            logger.info("✅ huggingface_hub.cached_download 已存在")
            _PATCHED = True
            return

        # 尝试添加cached_download别名
        if hasattr(huggingface_hub, 'hf_hub_download'):
            huggingface_hub.cached_download = huggingface_hub.hf_hub_download
            logger.info("✅ 已添加 cached_download 别名指向 hf_hub_download")
        else:
            # 创建一个模拟函数
            def mock_cached_download(*args, **kwargs):
                logger.warning("使用模拟的cached_download函数")
                return None

            huggingface_hub.cached_download = mock_cached_download
            logger.info("✅ 已添加模拟的 cached_download 函数")

        _PATCHED = True

    except ImportError:
        logger.warning("⚠️ 无法导入huggingface_hub，跳过修补")
    except Exception as e:
        logger.error(f"❌ 修补huggingface_hub失败: {e}")

def patch_sentence_transformers():
    """修补sentence_transformers模块"""
    try:
        # 在导入sentence_transformers之前先修补huggingface_hub
        patch_huggingface_hub()

        import sentence_transformers
        logger.info("✅ sentence_transformers 导入成功")

    except ImportError as e:
        logger.warning(f"⚠️ sentence_transformers 导入失败: {e}")
    except Exception as e:
        logger.error(f"❌ 修补sentence_transformers失败: {e}")

def apply_global_patches():
    """应用所有全局修补"""
    logger.info("🔧 开始应用HuggingFace兼容性修补...")

    # 1. 修补huggingface_hub
    patch_huggingface_hub()

    # 2. 修补sentence_transformers
    patch_sentence_transformers()

    # 3. 修补transformers（如果需要）
    try:
        import transformers
        logger.info("✅ transformers 导入成功")
    except Exception as e:
        logger.warning(f"⚠️ transformers 导入失败: {e}")

    logger.info("✅ HuggingFace兼容性修补完成")

# 测试函数
def test_compatibility():
    """测试兼容性"""
    print("🧪 测试HuggingFace Hub兼容性...")

    functions_to_test = [
        ('cached_download', cached_download),
        ('hf_hub_download', hf_hub_download),
        ('snapshot_download', snapshot_download),
        ('model_info', model_info)
    ]

    for name, func in functions_to_test:
        try:
            # 简单的函数调用测试（不实际下载）
            print(f"  ✅ {name}: 可用")
        except Exception as e:
            print(f"  ❌ {name}: 不可用 - {e}")

    print("🧪 兼容性测试完成")

# 自动应用修补
apply_global_patches()

if __name__ == "__main__":
    test_compatibility()
