
<!-- 性能监控面板 -->
<div id="performance-dashboard" class="card enhanced-card d-none">
    <div class="card-header card-header-enhanced">
        <h5 class="mb-0">
            <i class="fas fa-chart-line me-2"></i>
            系统性能监控
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="cpu-usage">--</div>
                    <div class="metric-label">CPU使用率</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="memory-usage">--</div>
                    <div class="metric-label">内存使用率</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="response-time">--</div>
                    <div class="metric-label">响应时间</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="active-users">--</div>
                    <div class="metric-label">活跃用户</div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>
            <div class="col-md-6">
                <div class="performance-logs">
                    <h6>性能日志</h6>
                    <div id="performance-log-list" class="log-list">
                        <!-- 日志条目将在这里动态添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.metric-card {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
    margin-bottom: 1rem;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.metric-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.log-list {
    max-height: 200px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 4px;
    padding: 0.5rem;
}

.log-entry {
    font-size: 0.875rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #e9ecef;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-timestamp {
    color: #6c757d;
    margin-right: 0.5rem;
}
</style>

<script>
// 性能监控面板JavaScript
class PerformanceDashboard {
    constructor() {
        this.isVisible = false;
        this.updateInterval = null;
        this.chart = null;
        this.data = {
            cpu: [],
            memory: [],
            responseTime: [],
            timestamps: []
        };
    }
    
    toggle() {
        const dashboard = document.getElementById('performance-dashboard');
        this.isVisible = !this.isVisible;
        
        if (this.isVisible) {
            dashboard.classList.remove('d-none');
            this.startMonitoring();
        } else {
            dashboard.classList.add('d-none');
            this.stopMonitoring();
        }
    }
    
    startMonitoring() {
        this.updateInterval = setInterval(() => {
            this.updateMetrics();
        }, 2000);
        
        this.initChart();
    }
    
    stopMonitoring() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
    
    async updateMetrics() {
        try {
            // 模拟性能数据（实际应用中应该从API获取）
            const metrics = {
                cpu: Math.random() * 100,
                memory: Math.random() * 100,
                responseTime: Math.random() * 1000,
                activeUsers: Math.floor(Math.random() * 50)
            };
            
            // 更新显示
            document.getElementById('cpu-usage').textContent = `${metrics.cpu.toFixed(1)}%`;
            document.getElementById('memory-usage').textContent = `${metrics.memory.toFixed(1)}%`;
            document.getElementById('response-time').textContent = `${metrics.responseTime.toFixed(0)}ms`;
            document.getElementById('active-users').textContent = metrics.activeUsers;
            
            // 更新图表数据
            this.updateChart(metrics);
            
            // 添加日志
            this.addLogEntry(`CPU: ${metrics.cpu.toFixed(1)}%, Memory: ${metrics.memory.toFixed(1)}%`);
            
        } catch (error) {
            console.error('更新性能指标失败:', error);
        }
    }
    
    initChart() {
        const ctx = document.getElementById('performance-chart').getContext('2d');
        
        // 这里应该使用Chart.js或其他图表库
        // 简化版本，只显示基本信息
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, 400, 200);
        ctx.fillStyle = '#0d6efd';
        ctx.font = '16px Arial';
        ctx.fillText('性能图表', 150, 100);
        ctx.font = '12px Arial';
        ctx.fillText('(需要Chart.js库支持)', 130, 120);
    }
    
    updateChart(metrics) {
        // 更新图表数据
        this.data.cpu.push(metrics.cpu);
        this.data.memory.push(metrics.memory);
        this.data.responseTime.push(metrics.responseTime);
        this.data.timestamps.push(new Date().toLocaleTimeString());
        
        // 保持最近20个数据点
        if (this.data.cpu.length > 20) {
            this.data.cpu.shift();
            this.data.memory.shift();
            this.data.responseTime.shift();
            this.data.timestamps.shift();
        }
    }
    
    addLogEntry(message) {
        const logList = document.getElementById('performance-log-list');
        const entry = document.createElement('div');
        entry.className = 'log-entry';
        entry.innerHTML = `
            <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
            ${message}
        `;
        
        logList.insertBefore(entry, logList.firstChild);
        
        // 保持最多10条日志
        while (logList.children.length > 10) {
            logList.removeChild(logList.lastChild);
        }
    }
}

// 创建全局实例
window.performanceDashboard = new PerformanceDashboard();

// 添加切换按钮到导航栏
document.addEventListener('DOMContentLoaded', () => {
    const navbar = document.querySelector('.navbar-nav');
    if (navbar) {
        const toggleButton = document.createElement('li');
        toggleButton.className = 'nav-item';
        toggleButton.innerHTML = `
            <a class="nav-link" href="#" onclick="window.performanceDashboard.toggle(); return false;">
                <i class="fas fa-chart-line"></i> 性能监控
            </a>
        `;
        navbar.appendChild(toggleButton);
    }
});
</script>
