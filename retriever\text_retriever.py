"""
文本检索模块

基于向量相似度的文本检索功能
"""

import os
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import faiss
from loguru import logger

from data_processing.vector_processor import VectorProcessor


class TextRetriever:
    """文本检索器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.index_path = config.get("index_path", "./embeddings/faiss_store")
        self.similarity_threshold = config.get("similarity_threshold", 0.7)
        self.top_k = config.get("top_k", 10)
        
        # 初始化向量处理器
        self.vector_processor = VectorProcessor(config.get("embedding", {}))
        
        # 索引和元数据
        self.index = None
        self.metadata = []
        
        # 加载现有索引
        self._load_index()
    
    def _load_index(self):
        """加载现有的索引和元数据"""
        try:
            index_file = os.path.join(self.index_path, "text_index.faiss")
            metadata_file = os.path.join(self.index_path, "text_metadata.pkl")
            
            if os.path.exists(index_file) and os.path.exists(metadata_file):
                self.index = self.vector_processor.load_index(index_file)
                _, self.metadata = self.vector_processor.load_vectors_metadata(metadata_file)
                logger.info(f"成功加载文本索引，包含 {len(self.metadata)} 个文档")
            else:
                logger.info("未找到现有索引，将创建新索引")
                
        except Exception as e:
            logger.error(f"索引加载失败: {str(e)}")
    
    def build_index(self, documents: List[Dict[str, Any]]) -> bool:
        """
        构建文本索引
        
        Args:
            documents: 文档列表
            
        Returns:
            是否成功
        """
        try:
            if not documents:
                logger.warning("文档列表为空，无法构建索引")
                return False
            
            logger.info(f"开始构建文本索引，文档数量: {len(documents)}")
            
            # 处理文档，生成向量
            vectors, metadata = self.vector_processor.process_documents(documents)
            
            if len(vectors) == 0:
                logger.error("向量生成失败")
                return False
            
            # 创建FAISS索引
            index_type = self.config.get("index_type", "IndexFlatIP")
            self.index = self.vector_processor.create_faiss_index(vectors, index_type)
            self.metadata = metadata
            
            # 保存索引和元数据
            self._save_index(vectors)
            
            logger.info(f"文本索引构建完成，包含 {len(metadata)} 个文档")
            return True
            
        except Exception as e:
            logger.error(f"索引构建失败: {str(e)}")
            return False
    
    def _save_index(self, vectors: np.ndarray):
        """保存索引和元数据"""
        try:
            os.makedirs(self.index_path, exist_ok=True)
            
            index_file = os.path.join(self.index_path, "text_index.faiss")
            metadata_file = os.path.join(self.index_path, "text_metadata.pkl")
            
            # 保存FAISS索引
            self.vector_processor.save_index(self.index, index_file)
            
            # 保存元数据
            self.vector_processor.save_vectors_metadata(vectors, self.metadata, metadata_file)
            
            logger.info("索引和元数据保存成功")
            
        except Exception as e:
            logger.error(f"索引保存失败: {str(e)}")
    
    def search(self, query: str, top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        增强的文档搜索 - 支持多策略检索和智能排序

        Args:
            query: 查询文本
            top_k: 返回结果数量

        Returns:
            搜索结果列表
        """
        try:
            if not self.index or not self.metadata:
                logger.warning("索引未初始化或为空")
                return []

            if not query.strip():
                logger.warning("查询文本为空")
                return []

            top_k = top_k or self.top_k

            # 1. 查询预处理和扩展
            processed_query = self._preprocess_query(query)
            expanded_queries = self._expand_query(processed_query)

            # 2. 多策略检索
            all_results = []

            # 主查询检索
            main_results = self._vector_search(processed_query, top_k * 2)
            all_results.extend(main_results)

            # 扩展查询检索
            for expanded_query in expanded_queries:
                expanded_results = self._vector_search(expanded_query, top_k)
                all_results.extend(expanded_results)

            # 关键词匹配检索
            keyword_results = self._keyword_search(query, top_k)
            all_results.extend(keyword_results)

            # 3. 结果去重和融合
            unique_results = self._deduplicate_results(all_results)

            # 4. 智能重排序
            reranked_results = self._rerank_results(unique_results, query, top_k)

            logger.info(f"增强搜索完成，查询: '{query[:50]}...', 返回 {len(reranked_results)} 个结果")
            return reranked_results

        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            return []

    def _preprocess_query(self, query: str) -> str:
        """查询预处理"""
        # 标准化电力术语
        query = self._normalize_power_terms(query)

        # 移除停用词但保留重要技术词汇
        query = self._remove_stopwords(query)

        return query.strip()

    def _normalize_power_terms(self, text: str) -> str:
        """标准化电力术语"""
        import re

        # 标准化电压等级
        text = re.sub(r'(\d+)\s*[kK][vV]', r'\1kV', text)
        text = re.sub(r'(\d+)\s*[mM][vV]', r'\1MV', text)

        # 标准化设备名称
        equipment_mapping = {
            '主变': '变压器',
            '主变压器': '变压器',
            '开关': '断路器',
            'GIS': 'GIS设备',
            'CT': '电流互感器',
            'PT': '电压互感器'
        }

        for old_term, new_term in equipment_mapping.items():
            text = text.replace(old_term, new_term)

        return text

    def _remove_stopwords(self, text: str) -> str:
        """移除停用词但保留技术词汇"""
        stop_words = {'的', '了', '在', '是', '有', '和', '与', '及', '或', '但'}
        power_terms = {'变压器', '断路器', '故障', '保护', '动作', '跳闸', '运行', '检修'}

        words = text.split()
        filtered_words = []

        for word in words:
            if word not in stop_words or word in power_terms:
                filtered_words.append(word)

        return ' '.join(filtered_words)

    def _expand_query(self, query: str) -> List[str]:
        """查询扩展 - 基于电力领域知识"""
        expanded_queries = []

        # 设备类型扩展
        if '变压器' in query:
            expanded_queries.append(query.replace('变压器', '主变'))
            expanded_queries.append(query + ' 油温 绕组')

        if '断路器' in query:
            expanded_queries.append(query.replace('断路器', '开关'))
            expanded_queries.append(query + ' SF6 操作机构')

        # 故障类型扩展
        if '故障' in query:
            expanded_queries.append(query + ' 跳闸 保护动作')
            expanded_queries.append(query + ' 异常 缺陷')

        return expanded_queries[:3]  # 限制扩展查询数量

    def _vector_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """向量搜索"""
        try:
            # 对查询进行向量化
            query_vector = self.vector_processor.encode_single_text(query)

            if len(query_vector) == 0:
                return []

            # 在索引中搜索
            scores, indices = self.vector_processor.search_similar(self.index, query_vector, top_k)

            # 构建结果
            results = []
            for i, (score, idx) in enumerate(zip(scores, indices)):
                if idx < len(self.metadata) and score >= self.similarity_threshold:
                    result = {
                        "rank": i + 1,
                        "score": float(score),
                        "content": self.metadata[idx]["content"],
                        "source": self.metadata[idx]["source"],
                        "chunk_id": self.metadata[idx]["chunk_id"],
                        "metadata": self.metadata[idx]["metadata"],
                        "search_type": "vector"
                    }
                    results.append(result)

            return results

        except Exception as e:
            logger.error(f"向量搜索失败: {str(e)}")
            return []

    def _keyword_search(self, query: str, top_k: int) -> List[Dict[str, Any]]:
        """关键词搜索 - 补充向量搜索"""
        results = []
        query_lower = query.lower()

        try:
            for i, doc in enumerate(self.metadata):
                content_lower = doc["content"].lower()

                # 计算关键词匹配分数
                score = self._calculate_keyword_score(query_lower, content_lower)

                if score > 0.1:  # 关键词匹配阈值
                    result = {
                        "rank": len(results) + 1,
                        "score": score,
                        "content": doc["content"],
                        "source": doc["source"],
                        "chunk_id": doc["chunk_id"],
                        "metadata": doc["metadata"],
                        "search_type": "keyword"
                    }
                    results.append(result)

            # 按分数排序
            results.sort(key=lambda x: x["score"], reverse=True)
            return results[:top_k]

        except Exception as e:
            logger.error(f"关键词搜索失败: {str(e)}")
            return []

    def _calculate_keyword_score(self, query: str, content: str) -> float:
        """计算关键词匹配分数"""
        query_words = set(query.split())
        content_words = set(content.split())

        # 计算交集
        intersection = query_words.intersection(content_words)

        if not query_words:
            return 0.0

        # 基础匹配分数
        base_score = len(intersection) / len(query_words)

        # 技术词汇加权
        power_terms = {'变压器', '断路器', '故障', '保护', '动作', '跳闸', '运行'}
        tech_bonus = sum(1 for word in intersection if word in power_terms) * 0.2

        return min(base_score + tech_bonus, 1.0)

    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """结果去重"""
        seen_chunks = set()
        unique_results = []

        for result in results:
            chunk_id = result.get("chunk_id")
            if chunk_id not in seen_chunks:
                seen_chunks.add(chunk_id)
                unique_results.append(result)

        return unique_results

    def _rerank_results(self, results: List[Dict[str, Any]], query: str, top_k: int) -> List[Dict[str, Any]]:
        """智能重排序 - 综合多种因素"""
        if not results:
            return []

        # 计算综合分数
        for result in results:
            vector_score = result["score"] if result.get("search_type") == "vector" else 0
            keyword_score = result["score"] if result.get("search_type") == "keyword" else 0

            # 长度惩罚（过短或过长的文档降权）
            content_length = len(result["content"])
            length_penalty = self._calculate_length_penalty(content_length)

            # 技术词汇加权
            tech_bonus = self._calculate_tech_bonus(result["content"], query)

            # 综合分数
            final_score = (vector_score * 0.6 + keyword_score * 0.3) * length_penalty + tech_bonus
            result["final_score"] = final_score

        # 按综合分数排序
        results.sort(key=lambda x: x.get("final_score", 0), reverse=True)

        # 重新分配排名
        for i, result in enumerate(results[:top_k]):
            result["rank"] = i + 1

        return results[:top_k]

    def _calculate_length_penalty(self, length: int) -> float:
        """计算长度惩罚因子"""
        if length < 50:
            return 0.7  # 太短
        elif length > 2000:
            return 0.8  # 太长
        else:
            return 1.0  # 合适长度

    def _calculate_tech_bonus(self, content: str, query: str) -> float:
        """计算技术词汇加权分数"""
        power_terms = {'变压器', '断路器', '故障', '保护', '动作', '跳闸', '运行', 'kV', 'MW'}

        content_lower = content.lower()
        query_lower = query.lower()

        # 统计技术词汇出现次数
        tech_count = sum(1 for term in power_terms if term.lower() in content_lower)
        query_tech_count = sum(1 for term in power_terms if term.lower() in query_lower)

        if query_tech_count > 0:
            return min(tech_count / query_tech_count * 0.1, 0.2)

        return 0.0

    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """
        向现有索引添加新文档

        Args:
            documents: 新文档列表

        Returns:
            是否成功
        """
        try:
            if not documents:
                return True

            logger.info(f"添加新文档到索引，数量: {len(documents)}")

            # 处理新文档
            logger.info("开始处理新文档向量")
            new_vectors, new_metadata = self.vector_processor.process_documents(documents)

            if len(new_vectors) == 0:
                logger.error("新文档向量生成失败")
                return False

            logger.info(f"向量生成成功，形状: {new_vectors.shape}")

            # 验证向量数据
            if not isinstance(new_vectors, np.ndarray):
                logger.error(f"向量数据类型错误: {type(new_vectors)}")
                return False

            if new_vectors.size == 0:
                logger.error("向量数据为空")
                return False

            # 确保向量是float32类型
            new_vectors = new_vectors.astype(np.float32)

            # 检查向量维度
            if len(new_vectors.shape) != 2:
                logger.error(f"向量维度错误: {new_vectors.shape}")
                return False

            # 如果没有现有索引，直接构建
            if not self.index:
                logger.info("没有现有索引，构建新索引")
                return self.build_index(documents)

            # 检查向量维度是否匹配
            current_dim = self.index.d
            new_dim = new_vectors.shape[1]

            if current_dim != new_dim:
                logger.error(f"向量维度不匹配: 现有索引维度={current_dim}, 新向量维度={new_dim}")
                logger.info("重建索引以适应新的向量维度")

                # 获取现有文档
                existing_docs = []
                for meta in self.metadata:
                    existing_docs.append({
                        'content': meta.get('content', ''),
                        'source': meta.get('source', ''),
                        'chunk_id': meta.get('chunk_id', 0),
                        'metadata': meta.get('metadata', {})
                    })

                # 合并现有文档和新文档
                all_documents = existing_docs + documents
                logger.info(f"重建索引，包含 {len(all_documents)} 个文档")

                # 重建索引
                return self.build_index(all_documents)

            # 添加到现有索引
            if hasattr(self.index, 'add'):
                try:
                    # 确保向量是连续的内存布局
                    if not new_vectors.flags['C_CONTIGUOUS']:
                        new_vectors = np.ascontiguousarray(new_vectors)

                    # 归一化新向量 - 使用安全的方式
                    vectors_copy = new_vectors.copy()
                    faiss.normalize_L2(vectors_copy)

                    # 添加到索引
                    logger.info(f"添加 {len(new_vectors)} 个向量到现有索引")
                    self.index.add(vectors_copy)

                    # 更新元数据
                    start_index = len(self.metadata)
                    for i, meta in enumerate(new_metadata):
                        meta["index"] = start_index + i

                    self.metadata.extend(new_metadata)

                    # 保存更新后的索引 - 需要传递所有向量
                    # 重新获取所有向量用于保存
                    all_vectors = np.vstack([self.vector_processor.vectors, vectors_copy]) if hasattr(self.vector_processor, 'vectors') and self.vector_processor.vectors.size > 0 else vectors_copy
                    self._save_index(all_vectors)

                    logger.info(f"成功添加 {len(documents)} 个新文档到索引")
                    return True

                except Exception as faiss_error:
                    logger.error(f"FAISS操作失败: {str(faiss_error)}")
                    return False
            else:
                logger.error("当前索引类型不支持增量添加")
                return False

        except Exception as e:
            logger.error(f"添加文档失败: {str(e)}")
            return False
    
    def get_document_by_id(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        根据文档ID获取文档
        
        Args:
            doc_id: 文档ID
            
        Returns:
            文档信息
        """
        try:
            for meta in self.metadata:
                if meta.get("source") == doc_id or str(meta.get("index")) == doc_id:
                    return {
                        "content": meta["content"],
                        "source": meta["source"],
                        "chunk_id": meta["chunk_id"],
                        "metadata": meta["metadata"]
                    }
            return None
            
        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            return None
    
    def get_similar_documents(self, doc_id: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        获取与指定文档相似的文档
        
        Args:
            doc_id: 文档ID
            top_k: 返回结果数量
            
        Returns:
            相似文档列表
        """
        try:
            # 找到目标文档
            target_doc = self.get_document_by_id(doc_id)
            if not target_doc:
                logger.warning(f"未找到文档: {doc_id}")
                return []
            
            # 使用文档内容进行搜索
            results = self.search(target_doc["content"], top_k + 1)
            
            # 过滤掉自身
            filtered_results = [r for r in results if r["source"] != doc_id][:top_k]
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"获取相似文档失败: {str(e)}")
            return []
    
    def get_index_stats(self) -> Dict[str, Any]:
        """
        获取索引统计信息
        
        Returns:
            统计信息
        """
        try:
            stats = {
                "total_documents": len(self.metadata),
                "index_type": type(self.index).__name__ if self.index else None,
                "dimension": self.index.d if self.index else 0,
                "is_trained": self.index.is_trained if hasattr(self.index, 'is_trained') else True,
                "similarity_threshold": self.similarity_threshold,
                "top_k": self.top_k
            }
            
            # 统计文档来源
            sources = {}
            for meta in self.metadata:
                source = meta.get("source", "unknown")
                sources[source] = sources.get(source, 0) + 1
            
            stats["sources"] = sources
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}
    
    def clear_index(self):
        """清空索引"""
        try:
            self.index = None
            self.metadata = []
            
            # 删除索引文件
            index_file = os.path.join(self.index_path, "text_index.faiss")
            metadata_file = os.path.join(self.index_path, "text_metadata.pkl")
            
            if os.path.exists(index_file):
                os.remove(index_file)
            if os.path.exists(metadata_file):
                os.remove(metadata_file)
            
            logger.info("索引已清空")
            
        except Exception as e:
            logger.error(f"清空索引失败: {str(e)}")
