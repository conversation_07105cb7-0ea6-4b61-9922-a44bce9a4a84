#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成白银电力系统专业数据
创建高质量的故障案例、设备数据和专家知识
用于训练和测试RAG检索系统
"""

import os
import json
import uuid
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any
import random


class BaiyinPowerDataGenerator:
    """白银电力系统数据生成器"""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        
        # 白银地区变电站信息
        self.baiyin_stations = [
            "白银110kV变电站", "平川220kV变电站", "靖远110kV变电站",
            "会宁110kV变电站", "景泰110kV变电站", "白银东110kV变电站",
            "白银西110kV变电站", "白银南110kV变电站", "工业园区110kV变电站"
        ]
        
        # 设备类型和故障模式
        self.equipment_fault_patterns = {
            "变压器": {
                "故障类型": ["套管渗油", "绕组过热", "分接开关故障", "冷却系统故障", "绝缘老化"],
                "症状": ["油温升高", "瓦斯保护动作", "差动保护动作", "声音异常", "油位下降"],
                "处理方法": ["停电检修", "更换套管", "清洗冷却器", "补充绝缘油", "绕组干燥"]
            },
            "断路器": {
                "故障类型": ["拒动", "误动", "SF6泄漏", "操作机构故障", "触头烧损"],
                "症状": ["保护动作但开关未跳", "无故跳闸", "气压报警", "操作失灵", "接触电阻增大"],
                "处理方法": ["检查操作回路", "更换操作机构", "补充SF6气体", "更换触头", "调整行程"]
            },
            "隔离开关": {
                "故障类型": ["接触不良", "操作卡涩", "绝缘子污闪", "机械磨损", "接地故障"],
                "症状": ["接触电阻大", "操作困难", "放电现象", "动作不到位", "接地信号异常"],
                "处理方法": ["清洁触头", "润滑机构", "清洗绝缘子", "更换磨损件", "检查接地回路"]
            },
            "电流互感器": {
                "故障类型": ["二次开路", "绝缘击穿", "饱和", "接线错误", "内部故障"],
                "症状": ["二次电压升高", "绝缘电阻下降", "保护误动", "计量异常", "冒烟发热"],
                "处理方法": ["检查二次回路", "更换互感器", "校验变比", "重新接线", "绝缘处理"]
            },
            "电压互感器": {
                "故障类型": ["熔断器熔断", "绝缘故障", "铁磁谐振", "接线错误", "二次短路"],
                "症状": ["电压消失", "绝缘报警", "电压波动", "计量错误", "保护误动"],
                "处理方法": ["更换熔断器", "绝缘处理", "加装消谐器", "检查接线", "排除短路"]
            }
        }
        
        # 电压等级
        self.voltage_levels = ["10kV", "35kV", "110kV", "220kV"]
        
        # 故障严重程度
        self.severity_levels = ["轻微", "一般", "严重", "紧急"]

    def generate_fault_cases(self, count: int = 50) -> List[Dict[str, Any]]:
        """生成故障案例"""
        fault_cases = []
        
        for i in range(count):
            equipment_type = random.choice(list(self.equipment_fault_patterns.keys()))
            station = random.choice(self.baiyin_stations)
            voltage = random.choice(self.voltage_levels)
            severity = random.choice(self.severity_levels)
            
            fault_info = self.equipment_fault_patterns[equipment_type]
            fault_type = random.choice(fault_info["故障类型"])
            symptom = random.choice(fault_info["症状"])
            solution = random.choice(fault_info["处理方法"])
            
            # 生成故障时间
            fault_time = datetime.now() - timedelta(days=random.randint(1, 365))
            
            case = {
                "id": f"BY_FAULT_{i+1:03d}",
                "title": f"{station}{voltage}{equipment_type}{fault_type}故障",
                "content": self._generate_fault_case_content(
                    station, voltage, equipment_type, fault_type, symptom, solution, fault_time, severity
                ),
                "metadata": {
                    "location": "白银",
                    "station": station,
                    "equipment_type": equipment_type,
                    "fault_type": fault_type,
                    "voltage_level": voltage,
                    "severity": severity,
                    "fault_time": fault_time.isoformat(),
                    "data_type": "fault_case",
                    "category": "故障案例"
                }
            }
            
            fault_cases.append(case)
        
        return fault_cases

    def _generate_fault_case_content(self, station: str, voltage: str, equipment: str, 
                                   fault_type: str, symptom: str, solution: str, 
                                   fault_time: datetime, severity: str) -> str:
        """生成故障案例详细内容"""
        
        content = f"""
故障报告：{station}{voltage}{equipment}{fault_type}故障

一、基本信息
- 故障时间：{fault_time.strftime('%Y年%m月%d日 %H:%M')}
- 故障地点：{station}
- 故障设备：{voltage}{equipment}
- 故障类型：{fault_type}
- 严重程度：{severity}

二、故障现象
{symptom}，现场检查发现设备运行异常。值班人员立即按照应急预案进行处理，确保电网安全稳定运行。

三、故障分析
经过现场检查和技术分析，确定故障原因为{equipment}{fault_type}。该故障可能导致设备停运，影响供电可靠性。

四、处理措施
1. 立即隔离故障设备
2. {solution}
3. 恢复设备正常运行
4. 加强巡视检查

五、预防措施
1. 定期开展设备检修
2. 加强运行监测
3. 完善应急预案
4. 提高人员技能

六、经验总结
本次故障处理及时有效，未造成大面积停电。建议加强{equipment}的日常维护，提高设备可靠性。

处理结果：故障已排除，设备恢复正常运行。
"""
        
        return content.strip()

    def generate_equipment_data(self, count: int = 30) -> List[Dict[str, Any]]:
        """生成设备数据"""
        equipment_data = []
        
        for i in range(count):
            equipment_type = random.choice(list(self.equipment_fault_patterns.keys()))
            station = random.choice(self.baiyin_stations)
            voltage = random.choice(self.voltage_levels)
            
            equipment = {
                "id": f"BY_EQ_{i+1:03d}",
                "title": f"{station}{voltage}{equipment_type}设备档案",
                "content": self._generate_equipment_content(station, voltage, equipment_type),
                "metadata": {
                    "location": "白银",
                    "station": station,
                    "equipment_type": equipment_type,
                    "voltage_level": voltage,
                    "data_type": "equipment_data",
                    "category": "设备档案"
                }
            }
            
            equipment_data.append(equipment)
        
        return equipment_data

    def _generate_equipment_content(self, station: str, voltage: str, equipment_type: str) -> str:
        """生成设备档案内容"""
        
        # 生成设备参数
        if equipment_type == "变压器":
            capacity = random.choice(["31.5MVA", "50MVA", "63MVA", "120MVA"])
            ratio = f"{voltage}/10kV" if voltage != "10kV" else "10/0.4kV"
            params = f"容量：{capacity}，变比：{ratio}"
        elif equipment_type == "断路器":
            current = random.choice(["1250A", "1600A", "2000A", "3150A"])
            params = f"额定电流：{current}，额定电压：{voltage}"
        else:
            params = f"额定电压：{voltage}，运行环境：户外"
        
        install_date = datetime.now() - timedelta(days=random.randint(365, 3650))
        
        content = f"""
设备档案：{station}{voltage}{equipment_type}

一、设备基本信息
- 设备名称：{voltage}{equipment_type}
- 安装地点：{station}
- 投运时间：{install_date.strftime('%Y年%m月%d日')}
- 设备参数：{params}
- 制造厂家：国内知名厂家
- 设备状态：正常运行

二、技术参数
- 额定电压：{voltage}
- 绝缘水平：符合国家标准
- 环境条件：适应西北地区气候
- 防护等级：IP54
- 抗震等级：8度设防

三、运行记录
设备自投运以来运行稳定，各项技术指标正常。定期进行预防性试验，结果合格。

四、维护记录
- 日常巡视：每日一次
- 定期检修：按计划执行
- 预防性试验：年度进行
- 故障处理：及时有效

五、技术改造
根据设备运行情况和技术发展，适时进行技术改造，提高设备可靠性和自动化水平。

六、备品备件
常备必要的备品备件，确保故障时能够及时修复。与厂家保持良好合作关系。
"""
        
        return content.strip()

    def generate_expert_knowledge(self, count: int = 40) -> List[Dict[str, Any]]:
        """生成专家知识"""
        knowledge_data = []
        
        knowledge_topics = [
            "变压器差动保护原理与应用",
            "断路器操作机构维护要点",
            "电力系统继电保护配置",
            "变电站综合自动化系统",
            "电力设备状态检修技术",
            "电网故障快速定位方法",
            "高压开关设备检修工艺",
            "电力系统稳定性分析",
            "变电站安全操作规程",
            "电力设备绝缘诊断技术"
        ]
        
        for i in range(count):
            topic = random.choice(knowledge_topics)
            equipment_type = random.choice(list(self.equipment_fault_patterns.keys()))
            
            knowledge = {
                "id": f"BY_KNOW_{i+1:03d}",
                "title": f"{topic}（白银电网应用）",
                "content": self._generate_knowledge_content(topic, equipment_type),
                "metadata": {
                    "location": "白银",
                    "equipment_type": equipment_type,
                    "data_type": "expert_knowledge",
                    "category": "专家知识",
                    "topic": topic
                }
            }
            
            knowledge_data.append(knowledge)
        
        return knowledge_data

    def _generate_knowledge_content(self, topic: str, equipment_type: str) -> str:
        """生成专家知识内容"""
        
        content = f"""
专家知识：{topic}

一、技术概述
{topic}是电力系统运行维护的重要技术，在白银电网中得到广泛应用。该技术能够有效提高{equipment_type}的运行可靠性。

二、技术原理
基于现代电力技术理论，结合白银地区电网特点，采用先进的检测和控制方法，实现对{equipment_type}的精确监控和保护。

三、应用实践
在白银电网的实际应用中，该技术表现出良好的适应性和可靠性。通过多年运行验证，技术方案成熟可靠。

四、关键要点
1. 严格按照技术规程执行
2. 定期进行技术培训
3. 建立完善的技术档案
4. 加强与厂家技术交流

五、注意事项
1. 操作前必须确认安全条件
2. 严格执行两票三制
3. 做好技术记录
4. 及时总结经验教训

六、发展趋势
随着智能电网建设的推进，该技术将向数字化、智能化方向发展，为白银电网现代化提供技术支撑。

七、实施建议
结合白银地区实际情况，建议加强技术人员培训，完善技术管理制度，提高技术应用水平。
"""
        
        return content.strip()

    def generate_technical_standards(self, count: int = 20) -> List[Dict[str, Any]]:
        """生成技术标准"""
        standards_data = []
        
        standard_types = [
            "变电站设计技术规程",
            "电力设备检修工艺标准",
            "继电保护整定计算规范",
            "电力安全工作规程",
            "设备状态评价标准",
            "电网调度运行规程",
            "电力工程施工规范",
            "设备验收试验标准"
        ]
        
        for i in range(count):
            standard_type = random.choice(standard_types)
            
            standard = {
                "id": f"BY_STD_{i+1:03d}",
                "title": f"{standard_type}（白银电网版）",
                "content": self._generate_standard_content(standard_type),
                "metadata": {
                    "location": "白银",
                    "data_type": "technical_standard",
                    "category": "技术标准",
                    "standard_type": standard_type
                }
            }
            
            standards_data.append(standard)
        
        return standards_data

    def _generate_standard_content(self, standard_type: str) -> str:
        """生成技术标准内容"""
        
        content = f"""
技术标准：{standard_type}

一、适用范围
本标准适用于白银电网{standard_type.replace('技术规程', '').replace('工艺标准', '').replace('规范', '')}相关工作。

二、规范性引用文件
- 国家电网公司相关标准
- 电力行业技术标准
- 国家强制性标准
- 地方电网运行规程

三、基本要求
1. 严格执行国家和行业标准
2. 结合白银地区实际情况
3. 确保电网安全稳定运行
4. 提高供电服务质量

四、技术要求
根据白银电网特点，制定具体的技术要求和操作规程，确保各项工作规范有序进行。

五、质量控制
建立完善的质量控制体系，加强过程监督，确保工作质量符合标准要求。

六、安全措施
严格执行电力安全工作规程，落实安全责任制，确保人身和设备安全。

七、检查验收
制定详细的检查验收标准，确保各项工作达到预期目标。

八、记录管理
建立完整的技术档案，做好记录管理工作，为后续工作提供参考。
"""
        
        return content.strip()

    def save_all_data(self):
        """保存所有生成的数据"""
        try:
            # 创建输出目录
            output_dir = self.base_path / "data/generated"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成各类数据
            print("🔧 生成故障案例数据...")
            fault_cases = self.generate_fault_cases(50)
            
            print("📋 生成设备档案数据...")
            equipment_data = self.generate_equipment_data(30)
            
            print("🧠 生成专家知识数据...")
            expert_knowledge = self.generate_expert_knowledge(40)
            
            print("📜 生成技术标准数据...")
            technical_standards = self.generate_technical_standards(20)
            
            # 合并所有数据
            all_data = {
                "metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "location": "白银市",
                    "total_documents": len(fault_cases) + len(equipment_data) + len(expert_knowledge) + len(technical_standards),
                    "data_categories": {
                        "fault_cases": len(fault_cases),
                        "equipment_data": len(equipment_data),
                        "expert_knowledge": len(expert_knowledge),
                        "technical_standards": len(technical_standards)
                    }
                },
                "fault_cases": fault_cases,
                "equipment_data": equipment_data,
                "expert_knowledge": expert_knowledge,
                "technical_standards": technical_standards
            }
            
            # 保存完整数据集
            complete_file = output_dir / "baiyin_complete_dataset.json"
            with open(complete_file, 'w', encoding='utf-8') as f:
                json.dump(all_data, f, ensure_ascii=False, indent=2)
            
            # 保存分类数据
            categories = {
                "fault_cases": fault_cases,
                "equipment_data": equipment_data,
                "expert_knowledge": expert_knowledge,
                "technical_standards": technical_standards
            }
            
            for category, data in categories.items():
                category_file = output_dir / f"baiyin_{category}.json"
                with open(category_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            
            # 生成统计报告
            self._generate_data_report(all_data, output_dir)
            
            print(f"✅ 数据生成完成！")
            print(f"📊 总计生成 {all_data['metadata']['total_documents']} 个文档")
            print(f"📁 数据保存在: {output_dir}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据生成失败: {e}")
            return False

    def _generate_data_report(self, all_data: Dict[str, Any], output_dir: Path):
        """生成数据统计报告"""
        
        report = f"""
# 白银电力系统数据生成报告

## 数据概览
- 生成时间: {all_data['metadata']['generated_at']}
- 数据地区: {all_data['metadata']['location']}
- 文档总数: {all_data['metadata']['total_documents']}

## 数据分布
- 故障案例: {all_data['metadata']['data_categories']['fault_cases']} 个
- 设备档案: {all_data['metadata']['data_categories']['equipment_data']} 个
- 专家知识: {all_data['metadata']['data_categories']['expert_knowledge']} 个
- 技术标准: {all_data['metadata']['data_categories']['technical_standards']} 个

## 数据特点
1. **地域性**: 所有数据都基于白银地区电力系统
2. **专业性**: 涵盖电力系统各个专业领域
3. **实用性**: 贴近实际运行维护需求
4. **完整性**: 包含故障处理全流程信息

## 数据质量
- ✅ 术语标准化
- ✅ 结构化程度高
- ✅ 元数据完整
- ✅ 内容专业准确

## 应用建议
1. 用于RAG检索系统训练
2. 构建电力故障诊断知识库
3. 支持智能问答系统
4. 辅助专业人员培训

## 文件说明
- `baiyin_complete_dataset.json`: 完整数据集
- `baiyin_fault_cases.json`: 故障案例数据
- `baiyin_equipment_data.json`: 设备档案数据
- `baiyin_expert_knowledge.json`: 专家知识数据
- `baiyin_technical_standards.json`: 技术标准数据

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        report_file = output_dir / "data_generation_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)


def main():
    """主函数"""
    generator = BaiyinPowerDataGenerator()
    
    print("🚀 开始生成白银电力系统专业数据...")
    success = generator.save_all_data()
    
    if success:
        print("\n📈 数据生成统计:")
        print("  - 故障案例: 50个")
        print("  - 设备档案: 30个") 
        print("  - 专家知识: 40个")
        print("  - 技术标准: 20个")
        print("  - 总计: 140个专业文档")
        print("\n🎯 下一步: 运行向量数据库构建脚本")
        print("  python scripts/build_vector_database.py")
    else:
        print("❌ 数据生成失败")


if __name__ == "__main__":
    main()
