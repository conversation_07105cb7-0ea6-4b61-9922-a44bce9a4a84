
import time
import threading
from functools import lru_cache
from typing import Dict, Any, Optional

class OptimizedCache:
    """优化的缓存系统"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        self.max_size = max_size
        self.ttl = ttl
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
        
        # 启动清理线程
        self.cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self.cleanup_thread.start()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                if time.time() - entry['timestamp'] < self.ttl:
                    self.access_times[key] = time.time()
                    return entry['value']
                else:
                    # 过期删除
                    del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
            return None
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self.lock:
            # 检查容量限制
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = {
                'value': value,
                'timestamp': time.time()
            }
            self.access_times[key] = time.time()
    
    def _evict_lru(self) -> None:
        """淘汰最近最少使用的条目"""
        if not self.access_times:
            return
        
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.access_times[lru_key]
    
    def _cleanup_loop(self) -> None:
        """清理过期条目的循环"""
        while True:
            time.sleep(300)  # 每5分钟清理一次
            self._cleanup_expired()
    
    def _cleanup_expired(self) -> None:
        """清理过期条目"""
        current_time = time.time()
        with self.lock:
            expired_keys = [
                key for key, entry in self.cache.items()
                if current_time - entry['timestamp'] >= self.ttl
            ]
            
            for key in expired_keys:
                del self.cache[key]
                if key in self.access_times:
                    del self.access_times[key]

# 全局缓存实例
embedding_cache = OptimizedCache(max_size=1000, ttl=3600)
search_cache = OptimizedCache(max_size=500, ttl=1800)
model_cache = OptimizedCache(max_size=10, ttl=7200)
