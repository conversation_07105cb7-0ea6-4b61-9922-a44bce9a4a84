/* 故障分析智能助手 - 简化样式文件 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    color: #ffc107;
    margin-right: 0.5rem;
}

/* 主容器样式 */
.container-fluid {
    padding: 20px;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: none;
    border-radius: 12px 12px 0 0 !important;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* 渐变背景 */
.bg-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* 按钮样式 */
.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    border: none;
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

/* 模态框样式 */
.modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    margin-right: 0.5rem;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: white;
    border-color: #e9ecef #e9ecef white;
    border-width: 2px 2px 0 2px;
}

/* 提示框样式 */
.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 加载动画 */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 搜索结果样式 */
.search-result-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: white;
    transition: all 0.3s ease;
    cursor: pointer;
}

.search-result-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
    transform: translateY(-1px);
}

.search-result-title {
    font-weight: 600;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.search-result-content {
    color: #6c757d;
    line-height: 1.6;
}

.search-result-meta {
    font-size: 0.875rem;
    color: #adb5bd;
    margin-top: 0.5rem;
}

/* 设备状态样式 */
.status-running {
    color: #28a745;
    font-weight: 600;
}

.status-maintenance {
    color: #ffc107;
    font-weight: 600;
}

.status-fault {
    color: #dc3545;
    font-weight: 600;
}

.status-stopped {
    color: #6c757d;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
        max-width: calc(100vw - 1rem);
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .card-header {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .modal-dialog {
        margin: 0.25rem;
        max-width: calc(100vw - 0.5rem);
        height: calc(100vh - 0.5rem);
    }
    
    .modal-content {
        height: 100%;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}
