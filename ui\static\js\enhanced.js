
// 增强的前端JavaScript功能

// 全局配置
const AppConfig = {
    apiBaseUrl: '/api/v1',
    debounceDelay: 300,
    animationDuration: 300,
    maxRetries: 3
};

// 工具函数
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    },
    
    // 格式化时间
    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    },
    
    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showToast('已复制到剪贴板', 'success');
        } catch (err) {
            console.error('复制失败:', err);
            this.showToast('复制失败', 'error');
        }
    },
    
    // 显示提示消息
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast-message toast-${type}`;
        toast.textContent = message;
        
        // 样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '500',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateX(100%)',
            transition: 'all 0.3s ease'
        });
        
        // 颜色
        const colors = {
            success: '#198754',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#0dcaf0'
        };
        toast.style.backgroundColor = colors[type] || colors.info;
        
        document.body.appendChild(toast);
        
        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // 自动隐藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, duration);
    }
};

// 加载管理器
const LoadingManager = {
    activeLoaders: new Set(),
    
    show(id = 'default') {
        this.activeLoaders.add(id);
        this.updateUI();
    },
    
    hide(id = 'default') {
        this.activeLoaders.delete(id);
        this.updateUI();
    },
    
    updateUI() {
        const hasLoaders = this.activeLoaders.size > 0;
        const overlay = document.getElementById('loading-overlay');
        
        if (hasLoaders && !overlay) {
            this.createOverlay();
        } else if (!hasLoaders && overlay) {
            overlay.remove();
        }
    },
    
    createOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-spinner"></div>
        `;
        document.body.appendChild(overlay);
    }
};

// 性能监控
const PerformanceMonitor = {
    metrics: {},
    
    start(name) {
        this.metrics[name] = {
            startTime: performance.now(),
            endTime: null,
            duration: null
        };
    },
    
    end(name) {
        if (this.metrics[name]) {
            this.metrics[name].endTime = performance.now();
            this.metrics[name].duration = this.metrics[name].endTime - this.metrics[name].startTime;
            console.log(`Performance [${name}]: ${this.metrics[name].duration.toFixed(2)}ms`);
        }
    },
    
    getMetrics() {
        return this.metrics;
    }
};

// 搜索增强
const SearchEnhancer = {
    cache: new Map(),
    
    async search(query, type = 'text', options = {}) {
        const cacheKey = `${query}-${type}-${JSON.stringify(options)}`;
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        PerformanceMonitor.start('search');
        LoadingManager.show('search');
        
        try {
            const response = await fetch(`${AppConfig.apiBaseUrl}/knowledge/search`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query,
                    search_type: type,
                    limit: options.limit || 10,
                    ...options
                })
            });
            
            if (!response.ok) {
                throw new Error(`搜索失败: ${response.status}`);
            }
            
            const result = await response.json();
            
            // 缓存结果
            this.cache.set(cacheKey, result);
            
            // 限制缓存大小
            if (this.cache.size > 100) {
                const firstKey = this.cache.keys().next().value;
                this.cache.delete(firstKey);
            }
            
            return result;
            
        } catch (error) {
            console.error('搜索错误:', error);
            Utils.showToast('搜索失败，请重试', 'error');
            throw error;
        } finally {
            LoadingManager.hide('search');
            PerformanceMonitor.end('search');
        }
    }
};

// 故障分析增强
const FaultAnalysisEnhancer = {
    async analyze(query, options = {}) {
        PerformanceMonitor.start('analysis');
        LoadingManager.show('analysis');
        
        try {
            const response = await fetch(`${AppConfig.apiBaseUrl}/analyze_stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query,
                    thinking_mode: options.thinkingMode || false,
                    test_type: options.testType || 'technical_reasoning'
                })
            });
            
            if (!response.ok) {
                throw new Error(`分析失败: ${response.status}`);
            }
            
            return this.handleStreamResponse(response, options.onProgress);
            
        } catch (error) {
            console.error('分析错误:', error);
            Utils.showToast('分析失败，请重试', 'error');
            throw error;
        } finally {
            LoadingManager.hide('analysis');
            PerformanceMonitor.end('analysis');
        }
    },
    
    async handleStreamResponse(response, onProgress) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let result = {
            reasoning: '',
            final: '',
            complete: false
        };
        
        try {
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');
                
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const data = JSON.parse(line.slice(6));
                            
                            if (data.type === 'reasoning') {
                                result.reasoning = data.content;
                            } else if (data.type === 'final') {
                                result.final = data.content;
                            } else if (data.type === 'complete') {
                                result.complete = true;
                                break;
                            }
                            
                            if (onProgress) {
                                onProgress(data);
                            }
                            
                        } catch (e) {
                            console.warn('解析SSE数据失败:', e);
                        }
                    }
                }
                
                if (result.complete) break;
            }
        } finally {
            reader.releaseLock();
        }
        
        return result;
    }
};

// 界面增强
const UIEnhancer = {
    init() {
        this.setupAnimations();
        this.setupKeyboardShortcuts();
        this.setupAccessibility();
        this.setupResponsive();
    },
    
    setupAnimations() {
        // 为新元素添加动画
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        node.classList.add('fade-in');
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    },
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+K 快速搜索
            if (e.ctrlKey && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('#search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
            
            // Esc 关闭模态框
            if (e.key === 'Escape') {
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(modal => {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                });
            }
        });
    },
    
    setupAccessibility() {
        // 为按钮添加键盘导航
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                const focusableElements = document.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );
                
                focusableElements.forEach(el => {
                    el.addEventListener('focus', () => {
                        el.style.outline = '2px solid #0d6efd';
                        el.style.outlineOffset = '2px';
                    });
                    
                    el.addEventListener('blur', () => {
                        el.style.outline = '';
                        el.style.outlineOffset = '';
                    });
                });
            }
        });
    },
    
    setupResponsive() {
        // 响应式导航
        const handleResize = Utils.throttle(() => {
            const navbar = document.querySelector('.navbar');
            const navbarToggler = document.querySelector('.navbar-toggler');
            
            if (window.innerWidth < 768) {
                navbar?.classList.add('navbar-mobile');
            } else {
                navbar?.classList.remove('navbar-mobile');
            }
        }, 250);
        
        window.addEventListener('resize', handleResize);
        handleResize(); // 初始调用
    }
};

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    UIEnhancer.init();
    console.log('前端增强功能已加载');
});

// 导出到全局
window.AppConfig = AppConfig;
window.Utils = Utils;
window.LoadingManager = LoadingManager;
window.PerformanceMonitor = PerformanceMonitor;
window.SearchEnhancer = SearchEnhancer;
window.FaultAnalysisEnhancer = FaultAnalysisEnhancer;
window.UIEnhancer = UIEnhancer;
