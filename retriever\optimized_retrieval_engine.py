#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化检索引擎 - 解决检索分数过低和质量问题
提升检索准确性和相关性评分
"""

import os
import sys
import re
import math
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    import jieba
    import jieba.posseg as pseg
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logging.warning("jieba不可用，将使用基础分词")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("sklearn不可用，将使用基础相似度计算")


@dataclass
class OptimizedSearchResult:
    """优化的搜索结果"""
    id: str
    title: str
    content: str
    raw_score: float
    optimized_score: float
    relevance_factors: Dict[str, float]
    metadata: Dict[str, Any]
    source: str
    search_type: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "score": self.optimized_score,
            "raw_score": self.raw_score,
            "relevance_factors": self.relevance_factors,
            "metadata": self.metadata,
            "source": self.source,
            "type": self.search_type
        }


class OptimizedRetrievalEngine:
    """优化检索引擎"""
    
    def __init__(self):
        """初始化优化检索引擎"""
        self.logger = logging.getLogger(__name__)
        
        # 电力专业词汇库（扩展版）
        self.power_terms = self._build_enhanced_power_vocabulary()
        
        # 查询扩展词典（优化版）
        self.query_expansion = self._build_enhanced_query_expansion()
        
        # 优化的评分权重
        self.scoring_weights = {
            "semantic_similarity": 0.35,      # 语义相似度
            "keyword_exact_match": 0.25,      # 精确关键词匹配
            "keyword_partial_match": 0.15,    # 部分关键词匹配
            "power_term_bonus": 0.15,         # 电力术语加分
            "content_quality": 0.10           # 内容质量评分
        }
        
        # 相似度阈值（降低以获得更多结果）
        self.similarity_thresholds = {
            "excellent": 0.85,
            "good": 0.70,
            "acceptable": 0.50,
            "minimum": 0.30
        }
        
        # 初始化分词器
        self._init_tokenizer()
        
        # 初始化TF-IDF向量化器
        self._init_tfidf_vectorizer()
        
        self.logger.info("优化检索引擎初始化完成")
    
    def _build_enhanced_power_vocabulary(self) -> Dict[str, float]:
        """构建增强的电力专业词汇库"""
        power_terms = {
            # 设备类型 - 高权重
            "变压器": 3.0, "主变": 3.0, "配变": 2.5, "所变": 2.5,
            "断路器": 3.0, "开关": 2.5, "隔离开关": 2.8,
            "互感器": 2.5, "电流互感器": 2.8, "电压互感器": 2.8,
            "避雷器": 2.5, "绝缘子": 2.5, "导线": 2.0,
            "母线": 2.5, "电缆": 2.0, "接地": 2.0,
            
            # 故障类型 - 高权重
            "故障": 3.0, "事故": 2.8, "异常": 2.5, "缺陷": 2.5,
            "短路": 3.0, "接地故障": 3.0, "过载": 2.8,
            "过热": 2.8, "漏油": 2.5, "放电": 2.8,
            "绝缘": 2.5, "击穿": 2.8, "闪络": 2.8,
            
            # 运行状态 - 中等权重
            "运行": 2.0, "检修": 2.5, "维护": 2.0, "巡视": 2.0,
            "投运": 2.0, "退运": 2.0, "停电": 2.5, "送电": 2.0,
            "操作": 2.0, "切换": 2.0, "倒闸": 2.5,
            
            # 技术参数 - 中等权重
            "电压": 2.0, "电流": 2.0, "功率": 2.0, "频率": 2.0,
            "温度": 2.0, "压力": 2.0, "油位": 2.0, "湿度": 2.0,
            "绝缘电阻": 2.5, "介损": 2.5, "局放": 2.8,
            
            # 地理位置 - 特殊权重
            "白银": 2.5, "白银市": 2.5, "中心变电站": 2.8,
            "110kV": 2.5, "220kV": 2.5, "35kV": 2.0, "10kV": 2.0
        }
        
        return power_terms
    
    def _build_enhanced_query_expansion(self) -> Dict[str, List[str]]:
        """构建增强的查询扩展词典"""
        expansion_dict = {
            # 变压器相关
            "变压器": ["主变", "配变", "所变", "transformer", "变压器设备"],
            "主变": ["变压器", "主变压器", "主变设备"],
            "配变": ["配电变压器", "配变设备"],
            
            # 故障相关
            "故障": ["事故", "异常", "缺陷", "问题", "fault"],
            "短路": ["短路故障", "接地", "相间短路"],
            "过热": ["温度异常", "发热", "热故障"],
            "漏油": ["渗油", "油位异常", "密封"],
            
            # 设备相关
            "断路器": ["开关", "断路器设备", "breaker"],
            "隔离开关": ["隔离", "刀闸", "隔离设备"],
            "互感器": ["CT", "PT", "电流互感器", "电压互感器"],
            
            # 运行相关
            "检修": ["维护", "保养", "检查", "维修"],
            "运行": ["运行状态", "在运", "投运"],
            "操作": ["倒闸", "切换", "投切"],
            
            # 参数相关
            "电压": ["电压等级", "额定电压", "voltage"],
            "电流": ["负荷电流", "额定电流", "current"],
            "温度": ["温升", "热点温度", "环境温度"],
            
            # 地理相关
            "白银": ["白银市", "白银地区"],
            "变电站": ["变电所", "站", "substation"]
        }
        
        return expansion_dict
    
    def _init_tokenizer(self):
        """初始化分词器"""
        if JIEBA_AVAILABLE:
            # 添加电力专业词汇到jieba词典
            for term in self.power_terms.keys():
                jieba.add_word(term)

            self.logger.info("jieba分词器初始化完成")
        else:
            self.logger.warning("jieba不可用，使用基础分词")
    
    def _init_tfidf_vectorizer(self):
        """初始化TF-IDF向量化器"""
        if SKLEARN_AVAILABLE:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=5000,
                ngram_range=(1, 2),
                stop_words=None,
                lowercase=True,
                analyzer='word'
            )
            self.logger.info("TF-IDF向量化器初始化完成")
        else:
            self.tfidf_vectorizer = None
            self.logger.warning("sklearn不可用，TF-IDF功能受限")
    
    def expand_query(self, query: str) -> Tuple[str, List[str]]:
        """
        增强查询扩展
        
        Args:
            query: 原始查询
            
        Returns:
            (扩展查询字符串, 扩展词列表)
        """
        expanded_terms = [query]
        expansion_words = []
        
        # 分词
        if JIEBA_AVAILABLE:
            words = jieba.lcut(query)
        else:
            words = query.split()
        
        # 查询扩展
        for word in words:
            if word in self.query_expansion:
                expansions = self.query_expansion[word][:3]  # 限制扩展数量
                expanded_terms.extend(expansions)
                expansion_words.extend(expansions)
        
        # 去重
        unique_terms = list(dict.fromkeys(expanded_terms))  # 保持顺序的去重
        expanded_query = " ".join(unique_terms)
        
        self.logger.debug(f"查询扩展: {query} -> {expanded_query}")
        return expanded_query, expansion_words

    def calculate_optimized_score(self, query: str, result: Dict[str, Any]) -> OptimizedSearchResult:
        """
        计算优化的相关性评分

        Args:
            query: 查询文本
            result: 原始搜索结果

        Returns:
            优化的搜索结果
        """
        content = result.get("content", "")
        title = result.get("title", "")
        raw_score = result.get("score", 0.0)

        # 计算各个维度的分数
        relevance_factors = {}

        # 1. 语义相似度（使用原始分数，但进行归一化）
        semantic_score = min(1.0, max(0.0, raw_score * 2.0))  # 放大原始分数
        relevance_factors["semantic_similarity"] = semantic_score

        # 2. 精确关键词匹配
        exact_match_score = self._calculate_exact_keyword_match(query, content, title)
        relevance_factors["keyword_exact_match"] = exact_match_score

        # 3. 部分关键词匹配
        partial_match_score = self._calculate_partial_keyword_match(query, content, title)
        relevance_factors["keyword_partial_match"] = partial_match_score

        # 4. 电力术语加分
        power_term_score = self._calculate_power_term_bonus(content, title)
        relevance_factors["power_term_bonus"] = power_term_score

        # 5. 内容质量评分
        quality_score = self._calculate_content_quality(content, title)
        relevance_factors["content_quality"] = quality_score

        # 计算加权总分
        optimized_score = sum(
            score * self.scoring_weights[factor]
            for factor, score in relevance_factors.items()
        )

        # 确保分数在合理范围内
        optimized_score = min(1.0, max(0.0, optimized_score))

        return OptimizedSearchResult(
            id=result.get("id", ""),
            title=title,
            content=content,
            raw_score=raw_score,
            optimized_score=optimized_score,
            relevance_factors=relevance_factors,
            metadata=result.get("metadata", {}),
            source=result.get("source", ""),
            search_type=result.get("type", "")
        )

    def _calculate_exact_keyword_match(self, query: str, content: str, title: str) -> float:
        """计算精确关键词匹配分数"""
        if not query or not (content or title):
            return 0.0

        # 分词
        if JIEBA_AVAILABLE:
            query_words = set(jieba.lcut(query.lower()))
        else:
            query_words = set(query.lower().split())

        # 移除停用词
        query_words = {word for word in query_words if len(word) > 1}

        if not query_words:
            return 0.0

        # 在标题和内容中查找精确匹配
        text_to_search = (title + " " + content).lower()

        matched_words = 0
        for word in query_words:
            if word in text_to_search:
                matched_words += 1

        # 计算匹配率
        match_ratio = matched_words / len(query_words)

        # 标题匹配给予额外加分
        title_bonus = 0.0
        if title:
            title_lower = title.lower()
            title_matches = sum(1 for word in query_words if word in title_lower)
            title_bonus = (title_matches / len(query_words)) * 0.3

        return min(1.0, match_ratio + title_bonus)

    def _calculate_partial_keyword_match(self, query: str, content: str, title: str) -> float:
        """计算部分关键词匹配分数"""
        if not query or not (content or title):
            return 0.0

        text_to_search = (title + " " + content).lower()
        query_lower = query.lower()

        # 计算字符级别的相似度
        char_matches = 0
        total_chars = len(query_lower)

        for char in query_lower:
            if char in text_to_search:
                char_matches += 1

        char_similarity = char_matches / total_chars if total_chars > 0 else 0.0

        # 计算n-gram匹配
        ngram_similarity = self._calculate_ngram_similarity(query_lower, text_to_search)

        # 综合评分
        partial_score = (char_similarity * 0.3 + ngram_similarity * 0.7)

        return min(1.0, partial_score)

    def _calculate_ngram_similarity(self, query: str, text: str, n: int = 2) -> float:
        """计算n-gram相似度"""
        if len(query) < n or len(text) < n:
            return 0.0

        # 生成n-gram
        query_ngrams = set(query[i:i+n] for i in range(len(query)-n+1))
        text_ngrams = set(text[i:i+n] for i in range(len(text)-n+1))

        if not query_ngrams:
            return 0.0

        # 计算交集
        intersection = query_ngrams.intersection(text_ngrams)

        return len(intersection) / len(query_ngrams)

    def _calculate_power_term_bonus(self, content: str, title: str) -> float:
        """计算电力术语加分"""
        text_to_analyze = (title + " " + content).lower()

        total_bonus = 0.0
        matched_terms = 0

        for term, weight in self.power_terms.items():
            if term in text_to_analyze:
                total_bonus += weight
                matched_terms += 1

        # 归一化加分
        if matched_terms > 0:
            # 基础分数：匹配术语数量的平方根（避免过度奖励）
            base_score = math.sqrt(matched_terms) / 10

            # 权重加分：平均权重
            weight_score = (total_bonus / matched_terms) / 10

            # 综合分数
            bonus_score = min(1.0, base_score + weight_score)

            return bonus_score

        return 0.0

    def _calculate_content_quality(self, content: str, title: str) -> float:
        """计算内容质量评分"""
        if not content and not title:
            return 0.0

        quality_factors = []

        # 1. 内容长度评分（适中长度得分更高）
        content_length = len(content)
        if content_length > 0:
            if 100 <= content_length <= 1000:
                length_score = 1.0
            elif 50 <= content_length < 100 or 1000 < content_length <= 2000:
                length_score = 0.8
            elif content_length < 50 or content_length > 2000:
                length_score = 0.5
            else:
                length_score = 0.3
            quality_factors.append(length_score)

        # 2. 标题质量评分
        if title:
            title_length = len(title)
            if 10 <= title_length <= 100:
                title_score = 1.0
            elif 5 <= title_length < 10 or 100 < title_length <= 150:
                title_score = 0.8
            else:
                title_score = 0.5
            quality_factors.append(title_score)

        # 3. 结构化程度评分
        structure_score = 0.0
        if content:
            # 检查是否包含数字、标点等结构化元素
            has_numbers = bool(re.search(r'\d', content))
            has_punctuation = bool(re.search(r'[，。！？；：]', content))
            has_technical_format = bool(re.search(r'[kV|MW|A|℃|%]', content))

            structure_score = sum([has_numbers, has_punctuation, has_technical_format]) / 3
            quality_factors.append(structure_score)

        # 计算平均质量分数
        return sum(quality_factors) / len(quality_factors) if quality_factors else 0.0

    def optimize_search_results(self, query: str, results: List[Dict[str, Any]]) -> List[OptimizedSearchResult]:
        """
        批量优化搜索结果

        Args:
            query: 查询文本
            results: 原始搜索结果列表

        Returns:
            优化后的搜索结果列表
        """
        if not results:
            return []

        optimized_results = []

        for result in results:
            try:
                optimized_result = self.calculate_optimized_score(query, result)
                optimized_results.append(optimized_result)
            except Exception as e:
                self.logger.warning(f"优化结果失败: {e}")
                # 创建基础结果作为回退
                fallback_result = OptimizedSearchResult(
                    id=result.get("id", ""),
                    title=result.get("title", ""),
                    content=result.get("content", ""),
                    raw_score=result.get("score", 0.0),
                    optimized_score=result.get("score", 0.0),
                    relevance_factors={},
                    metadata=result.get("metadata", {}),
                    source=result.get("source", ""),
                    search_type=result.get("type", "")
                )
                optimized_results.append(fallback_result)

        # 按优化分数排序
        optimized_results.sort(key=lambda x: x.optimized_score, reverse=True)

        return optimized_results

    def merge_and_deduplicate_results(self,
                                    results_lists: List[List[OptimizedSearchResult]],
                                    max_results: int = 10) -> List[OptimizedSearchResult]:
        """
        合并和去重多个结果列表

        Args:
            results_lists: 多个结果列表
            max_results: 最大返回结果数

        Returns:
            合并去重后的结果列表
        """
        all_results = []
        seen_content = set()

        # 合并所有结果
        for results_list in results_lists:
            all_results.extend(results_list)

        # 去重（基于内容相似度）
        unique_results = []
        for result in all_results:
            content_key = self._generate_content_key(result.content, result.title)

            if content_key not in seen_content:
                seen_content.add(content_key)
                unique_results.append(result)

        # 按分数排序并限制数量
        unique_results.sort(key=lambda x: x.optimized_score, reverse=True)

        return unique_results[:max_results]

    def _generate_content_key(self, content: str, title: str) -> str:
        """生成内容去重键"""
        # 使用内容和标题的前100个字符作为去重键
        combined_text = (title + " " + content)[:100]

        # 移除空白字符并转换为小写
        normalized_text = re.sub(r'\s+', ' ', combined_text.lower().strip())

        return normalized_text

    def analyze_search_quality(self, query: str, results: List[OptimizedSearchResult]) -> Dict[str, Any]:
        """
        分析搜索质量

        Args:
            query: 查询文本
            results: 优化后的搜索结果

        Returns:
            质量分析报告
        """
        if not results:
            return {
                "overall_quality": "poor",
                "average_score": 0.0,
                "score_distribution": {},
                "recommendations": ["没有找到相关结果，建议调整查询词"]
            }

        # 计算统计信息
        scores = [r.optimized_score for r in results]
        average_score = sum(scores) / len(scores)
        max_score = max(scores)
        min_score = min(scores)

        # 分数分布
        score_distribution = {
            "excellent": len([s for s in scores if s >= self.similarity_thresholds["excellent"]]),
            "good": len([s for s in scores if self.similarity_thresholds["good"] <= s < self.similarity_thresholds["excellent"]]),
            "acceptable": len([s for s in scores if self.similarity_thresholds["acceptable"] <= s < self.similarity_thresholds["good"]]),
            "poor": len([s for s in scores if s < self.similarity_thresholds["acceptable"]])
        }

        # 确定整体质量
        if average_score >= self.similarity_thresholds["good"]:
            overall_quality = "excellent"
        elif average_score >= self.similarity_thresholds["acceptable"]:
            overall_quality = "good"
        elif average_score >= self.similarity_thresholds["minimum"]:
            overall_quality = "acceptable"
        else:
            overall_quality = "poor"

        # 生成建议
        recommendations = self._generate_search_recommendations(query, results, average_score)

        return {
            "overall_quality": overall_quality,
            "average_score": average_score,
            "max_score": max_score,
            "min_score": min_score,
            "score_distribution": score_distribution,
            "total_results": len(results),
            "recommendations": recommendations
        }

    def _generate_search_recommendations(self, query: str, results: List[OptimizedSearchResult], avg_score: float) -> List[str]:
        """生成搜索建议"""
        recommendations = []

        if avg_score < self.similarity_thresholds["acceptable"]:
            recommendations.append("搜索结果相关性较低，建议：")
            recommendations.append("1. 尝试使用更具体的电力专业术语")
            recommendations.append("2. 添加设备型号或故障类型等关键信息")
            recommendations.append("3. 使用同义词或相关词汇扩展查询")

        if len(results) < 3:
            recommendations.append("搜索结果数量较少，建议：")
            recommendations.append("1. 使用更通用的查询词")
            recommendations.append("2. 减少限制性条件")
            recommendations.append("3. 尝试不同的搜索策略")

        # 分析查询词
        if JIEBA_AVAILABLE:
            query_words = jieba.lcut(query)
        else:
            query_words = query.split()

        power_terms_in_query = [word for word in query_words if word in self.power_terms]

        if not power_terms_in_query:
            recommendations.append("查询中缺少电力专业术语，建议添加相关设备或故障类型")

        return recommendations if recommendations else ["搜索结果质量良好"]


# 全局实例
_optimized_engine = None

def get_optimized_engine() -> OptimizedRetrievalEngine:
    """获取优化检索引擎实例（单例模式）"""
    global _optimized_engine
    if _optimized_engine is None:
        _optimized_engine = OptimizedRetrievalEngine()
    return _optimized_engine
