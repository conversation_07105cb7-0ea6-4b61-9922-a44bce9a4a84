"""
OCR处理模块

负责图像文字识别、表格提取、版面分析等功能
"""

import os
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import cv2
import numpy as np
from PIL import Image
import pytesseract
import easyocr
from loguru import logger

# PaddleOCR作为可选依赖
PaddleOCR = None
PADDLE_AVAILABLE = False

try:
    from paddleocr import PaddleOCR as _PaddleOCR
    PaddleOCR = _PaddleOCR
    PADDLE_AVAILABLE = True
    logger.info("PaddleOCR可用")
except ImportError:
    logger.info("PaddleOCR未安装，将使用EasyOCR或Tesseract")
except Exception as e:
    logger.info(f"PaddleOCR不可用: {e}，将使用其他OCR引擎")


class OCRProcessor:
    """OCR处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        # 初始化配置
        self.config = config
        # 获取OCR引擎，默认为paddleocr
        self.engine = config.get("engine", "paddleocr")
        # 获取语言，默认为ch和en
        self.languages = config.get("languages", ["ch", "en"])
        # 获取置信度阈值，默认为0.8
        self.confidence_threshold = config.get("confidence_threshold", 0.8)
        
        # 初始化OCR引擎
        self._init_ocr_engines()
    
    def _init_ocr_engines(self):
        """初始化OCR引擎"""
        self.paddle_ocr = None
        self.easy_ocr = None

        # 尝试初始化PaddleOCR
        if self.engine == "paddleocr" and PADDLE_AVAILABLE and PaddleOCR is not None:
            try:
                self.paddle_ocr = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch',
                    show_log=False
                )
                logger.info("PaddleOCR初始化成功")
                return  # 成功初始化PaddleOCR，直接返回
            except Exception as e:
                logger.warning(f"PaddleOCR初始化失败: {e}")
                self.paddle_ocr = None

        # 尝试初始化EasyOCR（作为备选方案）
        try:
            # 修复EasyOCR语言代码映射
            easyocr_languages = []
            for lang in self.languages:
                if lang in ['ch', 'zh', 'zh-cn', 'chinese']:
                    easyocr_languages.append('ch_sim')  # 简体中文
                elif lang in ['zh-tw', 'traditional']:
                    easyocr_languages.append('ch_tra')  # 繁体中文
                elif lang == 'en' or lang == 'english':
                    easyocr_languages.append('en')
                else:
                    # 对于其他语言，直接使用原始代码
                    easyocr_languages.append(lang)

            # 去重并确保至少有一种语言
            easyocr_languages = list(set(easyocr_languages))
            if not easyocr_languages:
                easyocr_languages = ['ch_sim', 'en']  # 默认支持中英文

            self.easy_ocr = easyocr.Reader(easyocr_languages)
            logger.info(f"EasyOCR初始化成功，支持语言: {easyocr_languages}")

        except Exception as e:
            logger.warning(f"EasyOCR初始化失败: {str(e)}")
            self.easy_ocr = None

        # 检查是否有可用的OCR引擎
        if not self.is_available():
            logger.info("所有OCR引擎都不可用，将使用基础文本处理功能")

    def is_available(self) -> bool:
        """检查OCR引擎是否可用"""
        return (hasattr(self, 'easy_ocr') and self.easy_ocr is not None) or \
               (hasattr(self, 'paddle_ocr') and self.paddle_ocr is not None)

    def extract_text(self, image_path: str) -> Dict[str, Any]:
        """
        从图像中提取文字

        Args:
            image_path: 图像路径

        Returns:
            OCR识别结果
        """
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                raise FileNotFoundError(f"图像文件不存在: {image_path}")

            # 检查OCR引擎是否可用
            if not self.is_available():
                logger.warning("OCR引擎不可用，返回空结果")
                return {
                    'full_text': '',
                    'text_blocks': [],
                    'confidence': 0.0,
                    'image_path': str(image_path),
                    'status': 'ocr_unavailable'
                }

            # 图像预处理
            processed_image = self._preprocess_for_ocr(str(image_path))

            # 根据配置选择OCR引擎
            if self.engine == "paddleocr" and PADDLE_AVAILABLE and hasattr(self, 'paddle_ocr') and self.paddle_ocr:
                result = self._paddle_ocr_extract(processed_image)
            elif self.engine == "easyocr" and hasattr(self, 'easy_ocr') and self.easy_ocr:
                result = self._easy_ocr_extract(processed_image)
            else:
                result = self._tesseract_extract(processed_image)

            # 后处理
            processed_result = self._post_process_ocr_result(result, str(image_path))

            logger.info(f"OCR识别完成: {image_path.name}, 识别到 {len(processed_result['text_blocks'])} 个文本块")
            return processed_result
            
        except Exception as e:
            logger.error(f"OCR识别失败 {image_path}: {str(e)}")
            return {"text_blocks": [], "full_text": "", "confidence": 0.0}

    def _read_image_with_chinese_path(self, image_path: str, flags: int = cv2.IMREAD_COLOR) -> Optional[np.ndarray]:
        """
        支持中文路径的图像读取函数

        Args:
            image_path: 图像路径
            flags: 读取标志

        Returns:
            图像数组或None
        """
        try:
            # 方法1：使用numpy和cv2.imdecode处理中文路径
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)

            # 解码图像
            image = cv2.imdecode(nparr, flags)

            if image is not None:
                return image

            # 方法2：使用PIL作为备选方案
            from PIL import Image as PILImage
            pil_image = PILImage.open(image_path)

            if flags == cv2.IMREAD_GRAYSCALE:
                pil_image = pil_image.convert('L')
                image = np.array(pil_image)
            else:
                pil_image = pil_image.convert('RGB')
                image = np.array(pil_image)
                # PIL使用RGB，OpenCV使用BGR
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            return image

        except Exception as e:
            logger.error(f"读取图像失败 {image_path}: {str(e)}")
            return None
    
    def _preprocess_for_ocr(self, image_path: str) -> np.ndarray:
        """OCR预处理"""
        try:
            # 使用支持中文路径的方式读取图像
            image = self._read_image_with_chinese_path(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 去噪
            denoised = cv2.medianBlur(gray, 3)

            # 二值化
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 形态学操作
            kernel = np.ones((2, 2), np.uint8)
            processed = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            return processed

        except Exception as e:
            logger.error(f"OCR预处理失败: {str(e)}")
            # 降级处理：尝试用支持中文路径的方式读取灰度图
            try:
                return self._read_image_with_chinese_path(image_path, cv2.IMREAD_GRAYSCALE)
            except:
                return np.zeros((100, 100), dtype=np.uint8)  # 返回空图像避免崩溃
    
    def _paddle_ocr_extract(self, image: np.ndarray) -> List[Tuple]:
        """使用PaddleOCR提取文字"""
        try:
            result = self.paddle_ocr.ocr(image, cls=True)
            return result[0] if result and result[0] else []
        except Exception as e:
            logger.error(f"PaddleOCR识别失败: {str(e)}")
            return []
    
    def _easy_ocr_extract(self, image: np.ndarray) -> List[Tuple]:
        """使用EasyOCR提取文字"""
        try:
            result = self.easy_ocr.readtext(image)
            # 转换为统一格式
            formatted_result = []
            for item in result:
                bbox, text, confidence = item
                # 转换bbox格式
                bbox_formatted = [[int(p[0]), int(p[1])] for p in bbox]
                formatted_result.append([bbox_formatted, [text, confidence]])
            return formatted_result
        except Exception as e:
            logger.error(f"EasyOCR识别失败: {str(e)}")
            return []
    
    def _tesseract_extract(self, image: np.ndarray) -> List[Tuple]:
        """使用Tesseract提取文字"""
        try:
            # 检查Tesseract是否可用
            if not self._check_tesseract_available():
                logger.warning("Tesseract不可用，跳过Tesseract识别")
                return []

            # 图像预处理
            enhanced_image = self._enhance_image_for_ocr(image)

            # 获取文字和位置信息
            data = pytesseract.image_to_data(enhanced_image, output_type=pytesseract.Output.DICT, lang='chi_sim+eng')

            result = []
            n_boxes = len(data['text'])

            for i in range(n_boxes):
                if int(data['conf'][i]) > 30:  # 提高置信度阈值
                    text = data['text'][i].strip()
                    if text:
                        x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]
                        bbox = [[x, y], [x + w, y], [x + w, y + h], [x, y + h]]
                        confidence = float(data['conf'][i]) / 100.0
                        result.append([bbox, [text, confidence]])

            return result

        except Exception as e:
            logger.error(f"Tesseract识别失败: {str(e)}")
            return []

    def _check_tesseract_available(self) -> bool:
        """检查Tesseract是否可用"""
        try:
            import subprocess
            result = subprocess.run(['tesseract', '--version'],
                                  capture_output=True, text=True, check=True, timeout=5)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
            return False

    def _enhance_image_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """为OCR增强图像"""
        try:
            # 如果是彩色图像，转换为灰度
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 去噪
            denoised = cv2.medianBlur(gray, 3)

            # 二值化
            _, binary = cv2.threshold(denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # 形态学操作
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            enhanced = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            return enhanced
        except Exception as e:
            logger.warning(f"图像增强失败，使用原图: {e}")
            return image
    
    def _post_process_ocr_result(self, ocr_result: List[Tuple], image_path: str) -> Dict[str, Any]:
        """后处理OCR结果"""
        try:
            text_blocks = []
            full_text = ""
            total_confidence = 0.0
            valid_blocks = 0
            
            for item in ocr_result:
                if len(item) >= 2:
                    bbox, text_info = item[0], item[1]
                    
                    if isinstance(text_info, list) and len(text_info) >= 2:
                        text, confidence = text_info[0], text_info[1]
                    else:
                        text, confidence = str(text_info), 0.5
                    
                    # 过滤低置信度和空文本
                    if confidence >= self.confidence_threshold and text.strip():
                        text_block = {
                            "text": text.strip(),
                            "bbox": bbox,
                            "confidence": float(confidence),
                            "area": self._calculate_bbox_area(bbox)
                        }
                        text_blocks.append(text_block)
                        full_text += text.strip() + " "
                        total_confidence += confidence
                        valid_blocks += 1
            
            # 按位置排序文本块（从上到下，从左到右）
            text_blocks.sort(key=lambda x: (x["bbox"][0][1], x["bbox"][0][0]))
            
            avg_confidence = total_confidence / valid_blocks if valid_blocks > 0 else 0.0
            
            result = {
                "text_blocks": text_blocks,
                "full_text": full_text.strip(),
                "confidence": avg_confidence,
                "total_blocks": len(text_blocks),
                "source": image_path,
                "metadata": {
                    "ocr_engine": self.engine,
                    "languages": self.languages,
                    "confidence_threshold": self.confidence_threshold
                }
            }
            
            return result
            
        except Exception as e:
            logger.error(f"OCR结果后处理失败: {str(e)}")
            return {"text_blocks": [], "full_text": "", "confidence": 0.0}
    
    def _calculate_bbox_area(self, bbox: List[List[int]]) -> float:
        """计算边界框面积"""
        try:
            if len(bbox) >= 4:
                x_coords = [point[0] for point in bbox]
                y_coords = [point[1] for point in bbox]
                width = max(x_coords) - min(x_coords)
                height = max(y_coords) - min(y_coords)
                return width * height
            return 0.0
        except:
            return 0.0
    
    def extract_table(self, image_path: str) -> Dict[str, Any]:
        """
        提取表格数据
        
        Args:
            image_path: 图像路径
            
        Returns:
            表格提取结果
        """
        try:
            # 先进行OCR识别
            ocr_result = self.extract_text(image_path)
            
            # 分析文本块的位置关系，识别表格结构
            table_data = self._analyze_table_structure(ocr_result["text_blocks"])
            
            return {
                "table_data": table_data,
                "source": image_path,
                "ocr_confidence": ocr_result["confidence"]
            }
            
        except Exception as e:
            logger.error(f"表格提取失败: {str(e)}")
            return {"table_data": [], "source": image_path, "ocr_confidence": 0.0}
    
    def _analyze_table_structure(self, text_blocks: List[Dict[str, Any]]) -> List[List[str]]:
        """分析表格结构"""
        try:
            if not text_blocks:
                return []
            
            # 按Y坐标分组（行）
            rows = {}
            for block in text_blocks:
                y_center = (block["bbox"][0][1] + block["bbox"][2][1]) // 2
                
                # 找到最接近的行
                best_row = None
                min_distance = float('inf')
                
                for row_y in rows.keys():
                    distance = abs(y_center - row_y)
                    if distance < min_distance and distance < 20:  # 20像素容差
                        min_distance = distance
                        best_row = row_y
                
                if best_row is None:
                    rows[y_center] = [block]
                else:
                    rows[best_row].append(block)
            
            # 对每行按X坐标排序
            table_data = []
            for row_y in sorted(rows.keys()):
                row_blocks = sorted(rows[row_y], key=lambda x: x["bbox"][0][0])
                row_texts = [block["text"] for block in row_blocks]
                table_data.append(row_texts)
            
            return table_data
            
        except Exception as e:
            logger.error(f"表格结构分析失败: {str(e)}")
            return []
    
    def batch_ocr(self, input_dir: str) -> List[Dict[str, Any]]:
        """
        批量OCR处理
        
        Args:
            input_dir: 输入目录
            
        Returns:
            OCR结果列表
        """
        input_dir = Path(input_dir)
        if not input_dir.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return []
        
        results = []
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        for image_path in input_dir.rglob("*"):
            if image_path.is_file() and image_path.suffix.lower() in image_extensions:
                result = self.extract_text(str(image_path))
                if result["text_blocks"]:
                    results.append(result)
        
        logger.info(f"批量OCR处理完成，共处理 {len(results)} 张图像")
        return results
