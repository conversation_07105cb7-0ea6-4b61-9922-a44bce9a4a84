
/* 增强的前端样式 */

/* 全局优化 */
:root {
    --primary-color: #0d6efd;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
}

/* 响应式设计优化 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .navbar-nav {
        flex-direction: column;
    }
}

/* 加载动画优化 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 卡片样式增强 */
.enhanced-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.enhanced-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header-enhanced {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

/* 按钮样式增强 */
.btn-enhanced {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

/* 输入框样式增强 */
.form-control-enhanced {
    border-radius: var(--border-radius);
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: var(--transition);
    font-size: 1rem;
}

.form-control-enhanced:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    outline: none;
}

/* 结果展示优化 */
.result-container {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
    box-shadow: var(--box-shadow);
    border-left: 4px solid var(--primary-color);
}

.result-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.result-icon {
    width: 24px;
    height: 24px;
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.result-content {
    line-height: 1.6;
    color: #495057;
}

/* 搜索结果样式 */
.search-result-item {
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    cursor: pointer;
    border: 1px solid #e9ecef;
}

.search-result-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-color: var(--primary-color);
}

.search-result-title {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.search-result-meta {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.search-result-preview {
    color: #495057;
    line-height: 1.5;
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-success {
    background: rgba(25, 135, 84, 0.1);
    color: var(--success-color);
}

.status-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
}

.status-error {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-info {
    background: rgba(13, 202, 240, 0.1);
    color: #055160;
}

/* 进度条增强 */
.progress-enhanced {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
    overflow: hidden;
}

.progress-bar-enhanced {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), #0056b3);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* 工具提示增强 */
.tooltip-enhanced {
    position: relative;
    cursor: help;
}

.tooltip-enhanced::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip-enhanced:hover::after {
    opacity: 1;
}

/* 标签页增强 */
.nav-tabs-enhanced {
    border: none;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

.nav-tabs-enhanced .nav-link {
    border: none;
    border-radius: calc(var(--border-radius) - 2px);
    color: #6c757d;
    font-weight: 500;
    transition: var(--transition);
    margin: 0 0.125rem;
}

.nav-tabs-enhanced .nav-link.active {
    background: white;
    color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 模态框增强 */
.modal-enhanced .modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-enhanced .modal-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

/* 表格增强 */
.table-enhanced {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table-enhanced thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: none;
    font-weight: 600;
    color: #495057;
}

.table-enhanced tbody tr {
    transition: var(--transition);
}

.table-enhanced tbody tr:hover {
    background: rgba(13, 110, 253, 0.05);
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { opacity: 0; transform: translateX(-30px); }
    to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #212529;
        --dark-color: #f8f9fa;
    }
    
    body {
        background-color: #1a1a1a;
        color: #f8f9fa;
    }
    
    .card {
        background-color: #2d2d2d;
        border-color: #404040;
    }
    
    .form-control {
        background-color: #2d2d2d;
        border-color: #404040;
        color: #f8f9fa;
    }
}
