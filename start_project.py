#!/usr/bin/env python3
"""
故障分析智能助手项目启动脚本
统一启动所有服务在端口5002
集成兼容性修复和完整功能
"""

import os
import sys
import time
import threading
import subprocess
import importlib.util
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 应用兼容性修复
def apply_compatibility_fixes():
    """应用兼容性修复"""
    logger.info("🔧 应用兼容性修复...")

    try:
        # 修复HuggingFace Hub兼容性
        import huggingface_hub
        if not hasattr(huggingface_hub, 'cached_download'):
            if hasattr(huggingface_hub, 'hf_hub_download'):
                huggingface_hub.cached_download = huggingface_hub.hf_hub_download
                logger.info("✅ 已添加 cached_download 别名")
            else:
                def mock_cached_download(*args, **kwargs):
                    logger.warning("使用模拟的cached_download函数")
                    return None
                huggingface_hub.cached_download = mock_cached_download
                logger.info("✅ 已添加模拟的 cached_download 函数")
        else:
            logger.info("✅ cached_download 已存在")

    except ImportError:
        logger.warning("⚠️ huggingface_hub 不可用，跳过修复")
    except Exception as e:
        logger.warning(f"⚠️ 兼容性修复失败: {e}")

# 应用修复
apply_compatibility_fixes()

from core.config_manager import get_config

def check_system_health():
    """检查系统健康状态"""
    health_status = {
        'python_version': sys.version_info[:2],
        'platform': sys.platform,
        'memory_available': True,
        'disk_space': True,
        'network': True
    }

    # 检查Python版本
    if sys.version_info < (3, 8):
        health_status['python_version_ok'] = False
    else:
        health_status['python_version_ok'] = True

    # 检查内存
    try:
        import psutil
        memory = psutil.virtual_memory()
        if memory.available < 1024 * 1024 * 1024:  # 1GB
            health_status['memory_available'] = False
    except ImportError:
        pass

    # 检查磁盘空间
    try:
        import shutil
        disk_usage = shutil.disk_usage('.')
        free_gb = disk_usage.free // (1024**3)
        if free_gb < 1:
            health_status['disk_space'] = False
    except Exception as e:
        pass

    # 检查网络连接
    try:
        import socket
        socket.create_connection(("8.8.8.8", 53), timeout=3)
    except Exception:
        health_status['network'] = False

    return health_status

def check_dependencies():
    """检查必要的依赖"""
    # 核心依赖（必须）
    core_packages = [
        'flask',
        'flask_cors',
        'loguru',
        'requests',
        'pandas',
        'numpy'
    ]

    missing_core = []

    # 检查核心依赖
    for package in core_packages:
        package_name = package.replace('-', '_') if '-' in package else package
        try:
            __import__(package_name)
        except ImportError:
            missing_core.append(package)

    # 报告结果
    if missing_core:
        logger.error(f"缺少核心依赖: {', '.join(missing_core)}")
        return False

    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "logs",
        "data/raw/uploads",
        "data/structured",
        "data/processed",
        "knowledge_base/text",
        "knowledge_base/images",
        "knowledge_base/mappings",
        "embeddings"
    ]

    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)

def start_web_server():
    """启动Web服务器"""
    logger.info("启动Web服务器...")
    try:
        from ui.app import app
        config = get_config()

        # 生产环境配置
        is_production = config.get('system.environment', 'development') == 'production'

        app.run(
            host=config.get('server.host', '0.0.0.0'),
            port=config.get('server.port', 5002),
            debug=False,  # 生产模式
            threaded=True,
            use_reloader=False,
            # 生产环境额外配置
            processes=config.get('server.workers', 4) if is_production else 1
        )
    except Exception as e:
        logger.error(f"Web服务器启动失败: {e}")

def start_api_server():
    """启动API服务器"""
    logger.info("启动API服务器...")
    try:
        import uvicorn
        from api.main import app
        config = get_config()
        
        uvicorn.run(
            app,
            host=config.get('server.host', '0.0.0.0'),
            port=config.get('server.port', 5002) + 1,  # API服务器使用5003端口
            log_level="info",
            reload=False
        )
    except Exception as e:
        logger.error(f"API服务器启动失败: {e}")

def start_optimized_server():
    """启动主服务器（使用完整功能的ui/app.py）"""
    logger.info("🚀 启动主服务器...")
    try:
        # 导入完整功能的ui/app.py
        from ui.app import app, socketio, SOCKETIO_AVAILABLE
        config = get_config()

        port = config.get('server.port', 5002)
        host = config.get('server.host', '0.0.0.0')
        debug = config.get('system.debug', False)

        logger.info(f"🌐 主服务器启动在 http://{host}:{port}")
        logger.info("📊 使用完整功能版本 (ui/app.py)")

        # 显示功能状态
        logger.info(f"📡 WebSocket支持: {'✅ 可用' if SOCKETIO_AVAILABLE else '❌ 不可用'}")
        logger.info("🔧 实时监控: 已启用")
        logger.info("🤖 DeepSeek AI: 已集成")
        logger.info("🔍 RAG检索: 已启用")
        logger.info("📊 数据处理: 已启用")

        # 启动实时监控系统
        try:
            from ui.app import real_time_monitor
            real_time_monitor.start_monitoring()
            logger.info("📊 实时监控系统已启动")
        except Exception as e:
            logger.warning(f"⚠️ 实时监控启动失败: {e}")

        # 根据SocketIO可用性选择启动方式
        if SOCKETIO_AVAILABLE:
            logger.info("🔗 使用SocketIO启动（支持WebSocket）")
            socketio.run(
                app,
                host=host,
                port=port,
                debug=debug,
                allow_unsafe_werkzeug=True
            )
        else:
            logger.info("⚠️ 使用标准Flask启动（不支持WebSocket）")
            app.run(
                host=host,
                port=port,
                debug=debug,
                threaded=True
            )

    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断，正在停止服务器...")
        # 停止监控系统
        try:
            from ui.app import real_time_monitor
            real_time_monitor.stop_monitoring()
            logger.info("📊 实时监控系统已停止")
        except:
            pass
        raise
    except Exception as e:
        logger.error(f"❌ 主服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
        raise

def print_startup_info():
    """打印启动信息"""
    config = get_config()
    port = config.get('server.port', 5002)
    
    print("\n" + "=" * 70)
    print("🎯 故障分析智能助手系统 v2.0")
    print("=" * 70)
    print(f"📍 主要访问地址: http://localhost:{port}")
    print(f"📍 本地访问: http://127.0.0.1:{port}")
    print(f"📍 API接口: http://localhost:{port}/api/v1/")
    print(f"📍 健康检查: http://localhost:{port}/api/v1/health")
    print(f"📍 系统状态: http://localhost:{port}/api/v1/system/complete-status")
    print(f"📍 仪表板: http://localhost:{port}/api/v1/visualization/dashboard")
    print("=" * 70)

    print("🚀 核心功能:")
    print("   • 🤖 DeepSeek R1/V3 AI模型集成")
    print("   • 🔍 增强RAG智能检索系统")
    print("   • 📊 实时监控和告警系统")
    print("   • 🔧 专业故障分析工具")
    print("   • 📱 多模态数据处理")
    print("   • 🌐 WebSocket实时通信")
    print("   • 📈 数据可视化和报表")
    print("   • 👥 多用户协作功能")
    print("   • 🛡️ 智能代理对话系统")
    print("   • 📋 设备管理和状态监控")

    print("\n🔗 API端点 (58个):")
    print("   • 故障分析: /api/v1/analyze, /api/v1/analyze_stream")
    print("   • 知识库: /api/v1/knowledge/search, /api/v1/knowledge/search/enhanced")
    print("   • 设备管理: /api/v1/equipment, /api/v1/equipment/advanced-management")
    print("   • 实时功能: WebSocket /monitoring, /alerts, /analysis")
    print("   • 可视化: /api/v1/visualization/dashboard, /api/v1/visualization/charts")
    print("   • 报表导出: /api/v1/reports/generate, /api/v1/export/data")

    print("=" * 70)
    print("⚠️  按 Ctrl+C 停止服务器")
    print("=" * 70)

def main():
    """主函数"""
    # 设置日志
    logger.add("logs/startup.log", rotation="1 day", retention="7 days")

    print("🚀 启动故障分析智能助手系统...")
    logger.info("系统启动开始")

    try:
        # 1. 系统健康检查
        logger.info("步骤 1/5: 系统健康检查")
        health_status = check_system_health()

        # 2. 检查依赖
        logger.info("步骤 2/5: 检查依赖")
        if not check_dependencies():
            logger.error("依赖检查失败")
            sys.exit(1)

        # 3. 创建目录
        logger.info("步骤 3/5: 创建目录")
        create_directories()

        # 4. 打印启动信息
        logger.info("步骤 4/5: 显示启动信息")
        print_startup_info()

        # 5. 启动主服务器
        logger.info("步骤 5/5: 启动主服务器")
        start_optimized_server()

    except KeyboardInterrupt:
        logger.info("⚠️ 用户中断，正在停止服务器...")
        print("\n⚠️ 服务器已停止")

        # 清理资源
        try:
            from ui.app import real_time_monitor
            real_time_monitor.stop_monitoring()
            logger.info("✅ 监控系统已停止")
        except:
            pass

    except ImportError as e:
        logger.error(f"❌ 模块导入失败: {e}")
        print(f"\n❌ 模块导入失败: {e}")
        print("💡 建议:")
        print("   1. 检查是否安装了所有依赖: pip install -r requirements.txt")
        print("   2. 检查Python环境是否正确")
        print("   3. 检查项目路径是否正确")
        sys.exit(1)

    except Exception as e:
        logger.error(f"❌ 系统启动失败: {e}")
        print(f"\n❌ 系统启动失败: {e}")

        print("\n💡 故障排除建议:")
        print("   1. 检查端口5002是否被占用")
        print("   2. 检查配置文件是否正确")
        print("   3. 查看日志文件: logs/startup.log")
        print("   4. 尝试重新安装依赖")

        sys.exit(1)

    finally:
        logger.info("系统启动流程结束")

if __name__ == "__main__":
    main()
