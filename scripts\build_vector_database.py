#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建向量数据库脚本
使用Chroma向量数据库存储白银电力系统的知识库
实现高效的语义检索功能
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from data_processing.chroma_manager import ChromaManager
    CHROMA_AVAILABLE = True
except ImportError as e:
    logger.error(f"Chroma依赖缺失: {e}")
    CHROMA_AVAILABLE = False


class VectorDatabaseBuilder:
    """向量数据库构建器"""
    
    def __init__(self, base_path: str = "."):
        self.base_path = Path(base_path)
        
        # 配置Chroma管理器
        self.chroma_config = {
            "persist_directory": str(self.base_path / "embeddings/chroma_store"),
            "collection_name": "baiyin_power_fault_collection",
            "embedding_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
        }
        
        self.chroma_manager = None
        if CHROMA_AVAILABLE:
            try:
                self.chroma_manager = ChromaManager(self.chroma_config)
                logger.info("Chroma管理器初始化成功")
            except Exception as e:
                logger.error(f"Chroma管理器初始化失败: {e}")
        
        # 统计信息
        self.stats = {
            "total_documents": 0,
            "processed_documents": 0,
            "failed_documents": 0,
            "start_time": None,
            "end_time": None
        }

    def load_prepared_documents(self) -> List[Dict[str, Any]]:
        """加载预处理的文档"""
        try:
            documents = []

            # 1. 加载新生成的专业数据（优先级最高）
            generated_file = self.base_path / "data/generated/baiyin_complete_dataset.json"
            if generated_file.exists():
                with open(generated_file, 'r', encoding='utf-8') as f:
                    generated_data = json.load(f)

                    # 提取各类数据
                    for category in ["fault_cases", "equipment_data", "expert_knowledge", "technical_standards"]:
                        if category in generated_data:
                            category_docs = generated_data[category]
                            documents.extend(category_docs)
                            logger.info(f"加载{category}: {len(category_docs)} 个")

            # 2. 加载数据准备脚本的输出
            prepared_file = self.base_path / "data/processed/baiyin_prepared_documents.json"
            if prepared_file.exists():
                with open(prepared_file, 'r', encoding='utf-8') as f:
                    prepared_docs = json.load(f)
                    documents.extend(prepared_docs)
                    logger.info(f"加载预处理文档: {len(prepared_docs)} 个")

            # 3. 加载集成数据库
            integrated_file = self.base_path / "data/integrated/baiyin_integrated_database.json"
            if integrated_file.exists():
                with open(integrated_file, 'r', encoding='utf-8') as f:
                    integrated_data = json.load(f)

                    # 从集成数据中提取文档
                    extracted_docs = self._extract_documents_from_integrated_data(integrated_data)
                    documents.extend(extracted_docs)
                    logger.info(f"从集成数据库提取文档: {len(extracted_docs)} 个")

            # 4. 加载结构化数据
            structured_docs = self._load_structured_documents()
            documents.extend(structured_docs)

            self.stats["total_documents"] = len(documents)
            logger.info(f"总共加载文档: {len(documents)} 个")

            return documents

        except Exception as e:
            logger.error(f"加载文档失败: {e}")
            return []

    def _extract_documents_from_integrated_data(self, integrated_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """从集成数据中提取文档"""
        documents = []
        
        try:
            data_sources = integrated_data.get("data_sources", {})
            
            for category, sources in data_sources.items():
                for source in sources:
                    if source.get("type") == "json":
                        # 处理JSON数据
                        content = self._extract_text_from_json(source.get("data", {}))
                    else:
                        # 处理文本数据
                        content = source.get("content", "")
                    
                    if content and len(content.strip()) > 50:  # 过滤太短的内容
                        doc = {
                            "id": f"{category}_{len(documents)}",
                            "content": content,
                            "source": source.get("source_file", ""),
                            "metadata": {
                                "category": category,
                                "data_type": category,
                                "location": "白银",
                                "extracted_from": "integrated_database"
                            }
                        }
                        documents.append(doc)
            
            return documents
            
        except Exception as e:
            logger.error(f"提取集成数据文档失败: {e}")
            return []

    def _extract_text_from_json(self, data: Any) -> str:
        """从JSON数据中提取文本内容"""
        if isinstance(data, str):
            return data
        elif isinstance(data, dict):
            text_parts = []
            for key, value in data.items():
                if isinstance(value, str) and len(value) > 10:
                    text_parts.append(f"{key}: {value}")
                elif isinstance(value, (dict, list)):
                    sub_text = self._extract_text_from_json(value)
                    if sub_text:
                        text_parts.append(sub_text)
            return "\n".join(text_parts)
        elif isinstance(data, list):
            text_parts = []
            for item in data:
                sub_text = self._extract_text_from_json(item)
                if sub_text:
                    text_parts.append(sub_text)
            return "\n".join(text_parts)
        else:
            return str(data)

    def _load_structured_documents(self) -> List[Dict[str, Any]]:
        """加载结构化数据文档"""
        documents = []
        
        try:
            structured_dir = self.base_path / "data/structured"
            if not structured_dir.exists():
                return documents
            
            for file_path in structured_dir.rglob("*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    content = self._extract_text_from_json(data)
                    if content and len(content.strip()) > 50:
                        doc = {
                            "id": f"structured_{file_path.stem}",
                            "content": content,
                            "source": str(file_path),
                            "metadata": {
                                "category": "structured_data",
                                "data_type": self._infer_data_type(file_path.name),
                                "location": "白银",
                                "file_name": file_path.name
                            }
                        }
                        documents.append(doc)
                        
                except Exception as e:
                    logger.error(f"处理结构化文件失败 {file_path}: {e}")
            
            logger.info(f"加载结构化文档: {len(documents)} 个")
            return documents
            
        except Exception as e:
            logger.error(f"加载结构化文档失败: {e}")
            return []

    def _infer_data_type(self, filename: str) -> str:
        """推断数据类型"""
        filename_lower = filename.lower()
        
        if any(keyword in filename_lower for keyword in ['fault', '故障', 'case']):
            return "fault_case"
        elif any(keyword in filename_lower for keyword in ['equipment', '设备']):
            return "equipment_data"
        elif any(keyword in filename_lower for keyword in ['expert', '专家', 'knowledge']):
            return "expert_knowledge"
        elif any(keyword in filename_lower for keyword in ['standard', '标准']):
            return "technical_standard"
        elif any(keyword in filename_lower for keyword in ['baiyin', '白银']):
            return "baiyin_specific"
        else:
            return "general"

    def build_vector_database(self) -> bool:
        """构建向量数据库"""
        try:
            if not CHROMA_AVAILABLE or not self.chroma_manager:
                logger.error("Chroma不可用，无法构建向量数据库")
                return False
            
            self.stats["start_time"] = datetime.now()
            logger.info("开始构建向量数据库...")
            
            # 1. 加载文档
            documents = self.load_prepared_documents()
            if not documents:
                logger.error("没有找到可处理的文档")
                return False
            
            # 2. 重置集合（清空现有数据）
            logger.info("重置Chroma集合...")
            self.chroma_manager.reset_collection()
            
            # 3. 批量添加文档
            batch_size = 50  # 批量处理大小
            total_batches = (len(documents) + batch_size - 1) // batch_size
            
            for i in range(0, len(documents), batch_size):
                batch = documents[i:i + batch_size]
                batch_num = i // batch_size + 1
                
                logger.info(f"处理批次 {batch_num}/{total_batches}, 文档数: {len(batch)}")
                
                success = self.chroma_manager.add_documents(batch)
                if success:
                    self.stats["processed_documents"] += len(batch)
                else:
                    self.stats["failed_documents"] += len(batch)
                    logger.error(f"批次 {batch_num} 处理失败")
                
                # 短暂休息，避免过载
                time.sleep(0.1)
            
            # 4. 验证结果
            stats = self.chroma_manager.get_collection_stats()
            logger.info(f"向量数据库构建完成，文档数量: {stats.get('total_documents', 0)}")
            
            # 5. 生成报告
            self._generate_build_report()
            
            self.stats["end_time"] = datetime.now()
            return True
            
        except Exception as e:
            logger.error(f"构建向量数据库失败: {e}")
            return False

    def test_vector_database(self) -> bool:
        """测试向量数据库"""
        try:
            if not self.chroma_manager:
                logger.error("Chroma管理器不可用")
                return False
            
            logger.info("开始测试向量数据库...")
            
            # 测试查询
            test_queries = [
                "变压器故障分析",
                "110kV断路器跳闸",
                "绝缘击穿处理方法",
                "白银电网运行状态",
                "设备检修计划"
            ]
            
            test_results = []
            
            for query in test_queries:
                logger.info(f"测试查询: {query}")
                results = self.chroma_manager.search(query, n_results=5)
                
                test_result = {
                    "query": query,
                    "result_count": len(results),
                    "top_score": results[0]["score"] if results else 0,
                    "has_relevant_results": len(results) > 0 and results[0]["score"] > 0.5
                }
                test_results.append(test_result)
                
                logger.info(f"  结果数量: {len(results)}")
                if results:
                    logger.info(f"  最高分数: {results[0]['score']:.3f}")
            
            # 统计测试结果
            successful_queries = sum(1 for r in test_results if r["has_relevant_results"])
            success_rate = successful_queries / len(test_queries) * 100
            
            logger.info(f"测试完成，成功率: {success_rate:.1f}% ({successful_queries}/{len(test_queries)})")
            
            # 保存测试结果
            test_report = {
                "test_time": datetime.now().isoformat(),
                "test_queries": test_results,
                "success_rate": success_rate,
                "database_stats": self.chroma_manager.get_collection_stats()
            }
            
            test_file = self.base_path / "data/processed/vector_database_test_report.json"
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(test_report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"测试报告已保存: {test_file}")
            
            return success_rate >= 60  # 60%以上成功率认为测试通过
            
        except Exception as e:
            logger.error(f"测试向量数据库失败: {e}")
            return False

    def _generate_build_report(self):
        """生成构建报告"""
        try:
            duration = (datetime.now() - self.stats["start_time"]).total_seconds()
            
            report = f"""
# 向量数据库构建报告

## 构建统计
- 开始时间: {self.stats["start_time"].strftime('%Y-%m-%d %H:%M:%S')}
- 构建耗时: {duration:.2f} 秒
- 总文档数: {self.stats["total_documents"]}
- 成功处理: {self.stats["processed_documents"]}
- 失败文档: {self.stats["failed_documents"]}
- 成功率: {(self.stats['processed_documents'] / max(self.stats['total_documents'], 1) * 100):.1f}%

## 数据库配置
- 向量数据库: Chroma
- 存储路径: {self.chroma_config["persist_directory"]}
- 集合名称: {self.chroma_config["collection_name"]}
- 嵌入模型: {self.chroma_config["embedding_model"]}

## 数据来源
- 预处理文档: data/processed/baiyin_prepared_documents.json
- 集成数据库: data/integrated/baiyin_integrated_database.json
- 结构化数据: data/structured/*.json

## 功能特性
- ✅ 语义检索
- ✅ 元数据过滤
- ✅ 设备类型分类
- ✅ 故障类型分类
- ✅ 地区信息标记

## 下一步建议
1. 运行测试脚本: python scripts/test_vector_database.py
2. 集成到Web界面: 更新retriever模块
3. 优化检索参数: 调整相似度阈值
4. 添加更多数据: 持续更新知识库

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            report_file = self.base_path / "data/processed/vector_database_build_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"构建报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"生成构建报告失败: {e}")


def main():
    """主函数"""
    if not CHROMA_AVAILABLE:
        print("❌ Chroma未安装，请运行: pip install chromadb")
        sys.exit(1)
    
    builder = VectorDatabaseBuilder()
    
    logger.info("开始构建白银电力系统向量数据库...")
    
    # 构建数据库
    build_success = builder.build_vector_database()
    
    if build_success:
        print("✅ 向量数据库构建成功！")
        print(f"📊 处理了 {builder.stats['total_documents']} 个文档")
        print(f"✅ 成功存储 {builder.stats['processed_documents']} 个文档")
        
        # 运行测试
        print("\n🧪 开始测试向量数据库...")
        test_success = builder.test_vector_database()
        
        if test_success:
            print("✅ 向量数据库测试通过！")
            print("🚀 系统已准备就绪，可以进行语义检索")
        else:
            print("⚠️ 向量数据库测试未完全通过，建议检查数据质量")
        
        print("\n📁 相关文件:")
        print("  - 构建报告: data/processed/vector_database_build_report.md")
        print("  - 测试报告: data/processed/vector_database_test_report.json")
        print("  - 数据库路径: embeddings/chroma_store/")
        
    else:
        print("❌ 向量数据库构建失败，请检查日志")
        sys.exit(1)


if __name__ == "__main__":
    main()
