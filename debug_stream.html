<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式响应调试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        .output-section {
            margin-top: 30px;
        }
        .output-box {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            min-height: 200px;
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
        }
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #6c757d;
        }
        .status-dot.connected {
            background-color: #28a745;
        }
        .status-dot.streaming {
            background-color: #ffc107;
            animation: pulse 1s infinite;
        }
        .status-dot.error {
            background-color: #dc3545;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .metric-title {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .config-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 DeepSeek 流式响应调试工具</h1>
        
        <div class="status-bar">
            <div class="status-indicator">
                <div class="status-dot" id="statusDot"></div>
                <span id="statusText">就绪</span>
            </div>
            <div>
                <span id="timestamp"></span>
            </div>
        </div>

        <div class="metrics">
            <div class="metric-card">
                <div class="metric-title">响应时间</div>
                <div class="metric-value" id="responseTime">0ms</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">接收字符数</div>
                <div class="metric-value" id="charCount">0</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">流式块数</div>
                <div class="metric-value" id="chunkCount">0</div>
            </div>
            <div class="metric-card">
                <div class="metric-title">平均速度</div>
                <div class="metric-value" id="avgSpeed">0 字/秒</div>
            </div>
        </div>

        <div class="config-section">
            <h3>配置参数</h3>
            <div class="config-grid">
                <div class="input-group">
                    <label for="modelSelect">模型选择</label>
                    <select id="modelSelect">
                        <option value="deepseek-chat">DeepSeek-V3</option>
                        <option value="deepseek-reasoner">DeepSeek-R1</option>
                    </select>
                </div>
                <div class="input-group">
                    <label for="temperature">Temperature</label>
                    <input type="number" id="temperature" value="0.7" min="0" max="2" step="0.1">
                </div>
                <div class="input-group">
                    <label for="maxTokens">Max Tokens</label>
                    <input type="number" id="maxTokens" value="4000" min="1" max="8000">
                </div>
                <div class="input-group">
                    <label for="topP">Top P</label>
                    <input type="number" id="topP" value="0.9" min="0" max="1" step="0.1">
                </div>
            </div>
        </div>

        <div class="input-section">
            <div class="input-group">
                <label for="queryInput">测试查询</label>
                <textarea id="queryInput" placeholder="请输入要测试的故障分析查询...">110kV变压器A相套管渗油，温度异常升高，请分析可能的原因和处理方案。</textarea>
            </div>
        </div>

        <div class="button-group">
            <button class="btn-primary" onclick="startStreamTest()">🚀 开始流式测试</button>
            <button class="btn-secondary" onclick="startNormalTest()">📝 普通请求测试</button>
            <button class="btn-danger" onclick="clearOutput()">🗑️ 清空输出</button>
            <button class="btn-secondary" onclick="exportResults()">💾 导出结果</button>
        </div>

        <div class="output-section">
            <h3>实时输出</h3>
            <div class="output-box" id="outputBox"></div>
        </div>
    </div>

    <script>
        let startTime = 0;
        let charCount = 0;
        let chunkCount = 0;
        let isStreaming = false;

        function updateStatus(status, text) {
            const statusDot = document.getElementById('statusDot');
            const statusText = document.getElementById('statusText');
            
            statusDot.className = `status-dot ${status}`;
            statusText.textContent = text;
            
            document.getElementById('timestamp').textContent = new Date().toLocaleTimeString();
        }

        function updateMetrics() {
            const elapsed = Date.now() - startTime;
            const avgSpeed = elapsed > 0 ? Math.round((charCount / elapsed) * 1000) : 0;
            
            document.getElementById('responseTime').textContent = elapsed + 'ms';
            document.getElementById('charCount').textContent = charCount;
            document.getElementById('chunkCount').textContent = chunkCount;
            document.getElementById('avgSpeed').textContent = avgSpeed + ' 字/秒';
        }

        function appendOutput(text, type = 'normal') {
            const outputBox = document.getElementById('outputBox');
            const span = document.createElement('span');
            
            if (type === 'error') {
                span.style.color = '#dc3545';
                span.style.fontWeight = 'bold';
            } else if (type === 'info') {
                span.style.color = '#007bff';
            } else if (type === 'success') {
                span.style.color = '#28a745';
            }
            
            span.textContent = text;
            outputBox.appendChild(span);
            outputBox.scrollTop = outputBox.scrollHeight;
        }

        async function startStreamTest() {
            if (isStreaming) return;
            
            const query = document.getElementById('queryInput').value.trim();
            if (!query) {
                alert('请输入测试查询');
                return;
            }

            isStreaming = true;
            startTime = Date.now();
            charCount = 0;
            chunkCount = 0;
            
            updateStatus('streaming', '流式传输中...');
            appendOutput(`\n=== 开始流式测试 ===\n查询: ${query}\n时间: ${new Date().toLocaleString()}\n\n`, 'info');

            try {
                const response = await fetch('/api/v1/analysis/fault/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        model_type: document.getElementById('modelSelect').value,
                        thinking_mode: document.getElementById('modelSelect').value === 'deepseek-reasoner',
                        temperature: parseFloat(document.getElementById('temperature').value),
                        max_tokens: parseInt(document.getElementById('maxTokens').value),
                        top_p: parseFloat(document.getElementById('topP').value)
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();

                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                updateStatus('connected', '传输完成');
                                appendOutput('\n\n=== 流式传输完成 ===\n', 'success');
                                isStreaming = false;
                                return;
                            }
                            
                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.content) {
                                    appendOutput(parsed.content);
                                    charCount += parsed.content.length;
                                    chunkCount++;
                                    updateMetrics();
                                }
                            } catch (e) {
                                // 忽略解析错误
                            }
                        }
                    }
                }
                
            } catch (error) {
                updateStatus('error', '传输错误');
                appendOutput(`\n错误: ${error.message}\n`, 'error');
                isStreaming = false;
            }
        }

        async function startNormalTest() {
            const query = document.getElementById('queryInput').value.trim();
            if (!query) {
                alert('请输入测试查询');
                return;
            }

            startTime = Date.now();
            updateStatus('streaming', '请求处理中...');
            appendOutput(`\n=== 开始普通请求测试 ===\n查询: ${query}\n时间: ${new Date().toLocaleString()}\n\n`, 'info');

            try {
                const response = await fetch('/api/v1/analysis/fault', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        model_type: document.getElementById('modelSelect').value
                    })
                });

                const result = await response.json();
                const elapsed = Date.now() - startTime;
                
                updateStatus('connected', '请求完成');
                
                if (result.success) {
                    appendOutput(result.analysis, 'success');
                    appendOutput(`\n\n=== 请求完成 (${elapsed}ms) ===\n`, 'info');
                } else {
                    appendOutput(`错误: ${result.error}\n`, 'error');
                }
                
            } catch (error) {
                updateStatus('error', '请求错误');
                appendOutput(`\n错误: ${error.message}\n`, 'error');
            }
        }

        function clearOutput() {
            document.getElementById('outputBox').innerHTML = '';
            charCount = 0;
            chunkCount = 0;
            updateMetrics();
            updateStatus('', '就绪');
        }

        function exportResults() {
            const output = document.getElementById('outputBox').textContent;
            const blob = new Blob([output], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `debug_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // 初始化
        updateStatus('', '就绪');
        updateMetrics();
    </script>
</body>
</html>
