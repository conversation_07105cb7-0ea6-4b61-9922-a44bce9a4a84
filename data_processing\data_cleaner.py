"""
数据清洗和转换模块
负责知识库数据的清洗、标准化、转换等功能
"""

import os
import re
import json
import csv
import xml.etree.ElementTree as ET
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import pandas as pd
from loguru import logger


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.output_dir = config.get("output_dir", "data/cleaned")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 电力专业术语标准化映射
        self.power_terms_mapping = {
            "变压器": ["变压器", "变电器", "电力变压器", "主变"],
            "断路器": ["断路器", "开关", "高压开关", "SF6断路器"],
            "保护装置": ["保护装置", "继电保护", "保护设备", "保护系统"],
            "差动保护": ["差动保护", "差流保护", "差动继电器"],
            "接地故障": ["接地故障", "单相接地", "接地短路"],
            "短路故障": ["短路故障", "相间短路", "三相短路"],
            "绝缘故障": ["绝缘故障", "绝缘击穿", "绝缘老化"]
        }
        
        # 单位标准化映射
        self.unit_mapping = {
            "电压": {
                "kV": ["kv", "KV", "千伏", "kV"],
                "MV": ["mv", "MV", "兆伏", "MV"],
                "V": ["v", "V", "伏特", "伏"]
            },
            "功率": {
                "kW": ["kw", "KW", "千瓦", "kW"],
                "MW": ["mw", "MW", "兆瓦", "MW"],
                "W": ["w", "W", "瓦特", "瓦"]
            },
            "频率": {
                "Hz": ["hz", "HZ", "赫兹", "Hz"],
                "kHz": ["khz", "KHZ", "千赫", "kHz"]
            }
        }
    
    def clean_data_batch(self, data_source: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        批量清洗数据
        
        Args:
            data_source: 数据源类型
            config: 清洗配置
            
        Returns:
            清洗结果统计
        """
        try:
            logger.info(f"开始批量数据清洗: {data_source}")
            
            # 获取数据
            raw_data = self._get_data_from_source(data_source)
            
            # 执行清洗步骤
            cleaned_data = []
            stats = {
                "total_records": len(raw_data),
                "cleaned_records": 0,
                "removed_duplicates": 0,
                "normalized_texts": 0,
                "extracted_keywords": 0,
                "quality_score": 0,
                "processing_time": 0,
                "errors": []
            }
            
            start_time = datetime.now()
            
            for i, record in enumerate(raw_data):
                try:
                    cleaned_record = self._clean_single_record(record, config)
                    if cleaned_record:
                        cleaned_data.append(cleaned_record)
                        stats["cleaned_records"] += 1
                        
                        if config.get("normalize_text", False):
                            stats["normalized_texts"] += 1
                        
                        if config.get("extract_keywords", False):
                            stats["extracted_keywords"] += 1
                            
                except Exception as e:
                    stats["errors"].append(f"记录 {i}: {str(e)}")
                    logger.warning(f"清洗记录 {i} 失败: {str(e)}")
            
            # 去重处理
            if config.get("remove_duplicates", False):
                original_count = len(cleaned_data)
                cleaned_data = self._remove_duplicates(cleaned_data)
                stats["removed_duplicates"] = original_count - len(cleaned_data)
                stats["cleaned_records"] = len(cleaned_data)
            
            # 计算质量评分
            stats["quality_score"] = self._calculate_data_quality(cleaned_data)
            
            # 计算处理时间
            processing_time = (datetime.now() - start_time).total_seconds()
            stats["processing_time"] = processing_time
            
            # 保存清洗后的数据
            output_file = self._save_cleaned_data(cleaned_data, config)
            stats["output_file"] = output_file
            
            logger.info(f"数据清洗完成: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"批量数据清洗失败: {str(e)}")
            raise
    
    def _get_data_from_source(self, data_source: str) -> List[Dict[str, Any]]:
        """从数据源获取数据"""
        try:
            data = []
            
            if data_source == "uploaded":
                # 从上传目录获取数据
                uploads_dir = self.config.get("uploads_dir", "uploads")
                if os.path.exists(uploads_dir):
                    for root, dirs, files in os.walk(uploads_dir):
                        for file in files:
                            if file.endswith(('.txt', '.docx', '.pdf', '.json')):
                                file_path = os.path.join(root, file)
                                content = self._extract_file_content(file_path)
                                if content:
                                    data.append({
                                        "source": file_path,
                                        "filename": file,
                                        "content": content,
                                        "type": "document"
                                    })
                                    
            elif data_source == "knowledge-base":
                # 从知识库获取数据
                # 这里应该连接到实际的知识库
                data = self._get_knowledge_base_data()
                
            elif data_source == "external":
                # 从外部数据源获取数据
                data = self._get_external_data()
            
            return data
            
        except Exception as e:
            logger.error(f"获取数据源失败: {str(e)}")
            return []
    
    def _extract_file_content(self, file_path: str) -> str:
        """提取文件内容"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
                    
            elif file_ext == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return json.dumps(data, ensure_ascii=False)
                    
            elif file_ext == '.docx':
                try:
                    import docx
                    doc = docx.Document(file_path)
                    return '\n'.join([p.text for p in doc.paragraphs])
                except ImportError:
                    logger.warning("python-docx未安装，跳过DOCX文件")
                    return ""
                    
            elif file_ext == '.pdf':
                # PDF处理需要额外的库
                logger.warning("PDF处理需要额外配置")
                return ""
                
            return ""
            
        except Exception as e:
            logger.error(f"提取文件内容失败 {file_path}: {str(e)}")
            return ""
    
    def _clean_single_record(self, record: Dict[str, Any], config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """清洗单条记录"""
        try:
            cleaned_record = record.copy()
            content = record.get("content", "")
            
            if not content or len(content.strip()) < 10:
                return None
            
            # 文本标准化
            if config.get("normalize_text", False):
                content = self._normalize_text(content)
            
            # 专业术语标准化
            content = self._standardize_power_terms(content)
            
            # 单位标准化
            content = self._standardize_units(content)
            
            # 提取关键词
            if config.get("extract_keywords", False):
                keywords = self._extract_keywords(content)
                cleaned_record["keywords"] = keywords
            
            # 分类标注
            if config.get("auto_classify", False):
                category = self._classify_content(content)
                cleaned_record["category"] = category
            
            cleaned_record["content"] = content
            cleaned_record["cleaned_at"] = datetime.now().isoformat()
            
            return cleaned_record
            
        except Exception as e:
            logger.error(f"清洗单条记录失败: {str(e)}")
            return None
    
    def _normalize_text(self, text: str) -> str:
        """文本标准化"""
        try:
            # 统一换行符
            text = re.sub(r'\r\n|\r', '\n', text)
            
            # 合并多余空白
            text = re.sub(r'[ \t]+', ' ', text)
            text = re.sub(r'\n\s*\n', '\n\n', text)
            
            # 移除特殊字符（保留中文、英文、数字、基本标点）
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s\.,;:!?()（）【】\-_/\\°℃%‰μΩΩkVMVAkWMWVarMVarHzrpm]', '', text)
            
            # 标准化标点符号
            text = re.sub(r'[，,]{2,}', '，', text)
            text = re.sub(r'[。.]{2,}', '。', text)
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"文本标准化失败: {str(e)}")
            return text
    
    def _standardize_power_terms(self, text: str) -> str:
        """标准化电力专业术语"""
        try:
            for standard_term, variants in self.power_terms_mapping.items():
                for variant in variants:
                    if variant != standard_term:
                        text = re.sub(rf'\b{re.escape(variant)}\b', standard_term, text, flags=re.IGNORECASE)
            
            return text
            
        except Exception as e:
            logger.error(f"专业术语标准化失败: {str(e)}")
            return text
    
    def _standardize_units(self, text: str) -> str:
        """标准化单位"""
        try:
            for unit_type, unit_mapping in self.unit_mapping.items():
                for standard_unit, variants in unit_mapping.items():
                    for variant in variants:
                        if variant != standard_unit:
                            # 匹配数字+单位的模式
                            pattern = rf'(\d+(?:\.\d+)?)\s*{re.escape(variant)}\b'
                            replacement = rf'\1{standard_unit}'
                            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
            
            return text
            
        except Exception as e:
            logger.error(f"单位标准化失败: {str(e)}")
            return text
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        try:
            keywords = []
            
            # 电力设备关键词
            equipment_keywords = ["变压器", "断路器", "发电机", "输电线路", "保护装置"]
            for keyword in equipment_keywords:
                if keyword in text:
                    keywords.append(keyword)
            
            # 故障类型关键词
            fault_keywords = ["短路", "接地", "绝缘", "机械", "热故障", "保护误动"]
            for keyword in fault_keywords:
                if keyword in text:
                    keywords.append(keyword)
            
            # 电压等级关键词
            voltage_pattern = r'(\d+)kV'
            voltages = re.findall(voltage_pattern, text)
            for voltage in voltages:
                keywords.append(f"{voltage}kV")
            
            return list(set(keywords))  # 去重
            
        except Exception as e:
            logger.error(f"关键词提取失败: {str(e)}")
            return []
    
    def _classify_content(self, text: str) -> str:
        """内容分类"""
        try:
            # 简单的基于关键词的分类
            if any(word in text for word in ["故障", "事故", "异常"]):
                return "故障案例"
            elif any(word in text for word in ["标准", "规程", "规范"]):
                return "技术标准"
            elif any(word in text for word in ["检修", "维护", "保养"]):
                return "维护手册"
            elif any(word in text for word in ["操作", "运行", "控制"]):
                return "操作规程"
            else:
                return "其他"
                
        except Exception as e:
            logger.error(f"内容分类失败: {str(e)}")
            return "未分类"
    
    def _remove_duplicates(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去除重复数据"""
        try:
            seen_contents = set()
            unique_data = []
            
            for record in data:
                content = record.get("content", "")
                # 使用内容的哈希值作为去重依据
                content_hash = hash(content[:500])  # 使用前500字符
                
                if content_hash not in seen_contents:
                    seen_contents.add(content_hash)
                    unique_data.append(record)
            
            return unique_data
            
        except Exception as e:
            logger.error(f"去重处理失败: {str(e)}")
            return data
    
    def _calculate_data_quality(self, data: List[Dict[str, Any]]) -> float:
        """计算数据质量评分"""
        try:
            if not data:
                return 0.0
            
            total_score = 0.0
            
            for record in data:
                score = 0.0
                
                # 内容长度评分 (30%)
                content_length = len(record.get("content", ""))
                if content_length > 100:
                    score += 30
                elif content_length > 50:
                    score += 20
                elif content_length > 20:
                    score += 10
                
                # 关键词数量评分 (25%)
                keywords = record.get("keywords", [])
                score += min(25, len(keywords) * 5)
                
                # 分类准确性评分 (25%)
                if record.get("category") and record.get("category") != "未分类":
                    score += 25
                
                # 结构化程度评分 (20%)
                if record.get("filename"):
                    score += 5
                if record.get("source"):
                    score += 5
                if record.get("cleaned_at"):
                    score += 10
                
                total_score += score
            
            return total_score / len(data)
            
        except Exception as e:
            logger.error(f"质量评分计算失败: {str(e)}")
            return 0.0
    
    def _save_cleaned_data(self, data: List[Dict[str, Any]], config: Dict[str, Any]) -> str:
        """保存清洗后的数据"""
        try:
            output_format = config.get("output_format", "json")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            if output_format == "json":
                output_file = os.path.join(self.output_dir, f"cleaned_data_{timestamp}.json")
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
            elif output_format == "csv":
                output_file = os.path.join(self.output_dir, f"cleaned_data_{timestamp}.csv")
                if data:
                    df = pd.DataFrame(data)
                    df.to_csv(output_file, index=False, encoding='utf-8')
                    
            elif output_format == "xml":
                output_file = os.path.join(self.output_dir, f"cleaned_data_{timestamp}.xml")
                self._save_as_xml(data, output_file)
                
            else:
                output_file = os.path.join(self.output_dir, f"cleaned_data_{timestamp}.json")
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"清洗数据已保存: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"保存清洗数据失败: {str(e)}")
            return ""
    
    def _save_as_xml(self, data: List[Dict[str, Any]], output_file: str):
        """保存为XML格式"""
        try:
            root = ET.Element("cleaned_data")
            
            for record in data:
                record_elem = ET.SubElement(root, "record")
                for key, value in record.items():
                    if isinstance(value, (str, int, float)):
                        elem = ET.SubElement(record_elem, key)
                        elem.text = str(value)
                    elif isinstance(value, list):
                        list_elem = ET.SubElement(record_elem, key)
                        for item in value:
                            item_elem = ET.SubElement(list_elem, "item")
                            item_elem.text = str(item)
            
            tree = ET.ElementTree(root)
            tree.write(output_file, encoding='utf-8', xml_declaration=True)
            
        except Exception as e:
            logger.error(f"保存XML失败: {str(e)}")
    
    def _get_knowledge_base_data(self) -> List[Dict[str, Any]]:
        """获取知识库数据（模拟）"""
        return [
            {
                "source": "knowledge_base",
                "content": "110kV变压器差动保护动作故障分析案例",
                "type": "case_study"
            }
        ]
    
    def _get_external_data(self) -> List[Dict[str, Any]]:
        """获取外部数据（模拟）"""
        return [
            {
                "source": "external_api",
                "content": "国网技术标准文档",
                "type": "standard"
            }
        ]
