"""
安全中间件

提供XSS防护、CSRF保护、速率限制等安全功能
"""

import time
import hashlib
import secrets
from typing import Dict, Any, Optional, Callable
from collections import defaultdict, deque
from datetime import datetime, timedelta
from fastapi import Request, HTTPException, Response
from fastapi.security import HTTPBearer
import bleach
import logging

logger = logging.getLogger(__name__)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, max_requests: int = 100, window_seconds: int = 60):
        """
        初始化速率限制器
        
        Args:
            max_requests: 时间窗口内最大请求数
            window_seconds: 时间窗口大小（秒）
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = defaultdict(deque)
        
    def is_allowed(self, client_id: str) -> bool:
        """
        检查是否允许请求
        
        Args:
            client_id: 客户端标识（通常是IP地址）
            
        Returns:
            是否允许请求
        """
        now = time.time()
        client_requests = self.requests[client_id]
        
        # 清理过期的请求记录
        while client_requests and client_requests[0] <= now - self.window_seconds:
            client_requests.popleft()
        
        # 检查是否超过限制
        if len(client_requests) >= self.max_requests:
            return False
        
        # 记录当前请求
        client_requests.append(now)
        return True
    
    def get_remaining_requests(self, client_id: str) -> int:
        """获取剩余请求数"""
        now = time.time()
        client_requests = self.requests[client_id]
        
        # 清理过期的请求记录
        while client_requests and client_requests[0] <= now - self.window_seconds:
            client_requests.popleft()
        
        return max(0, self.max_requests - len(client_requests))
    
    def get_reset_time(self, client_id: str) -> Optional[datetime]:
        """获取限制重置时间"""
        client_requests = self.requests[client_id]
        if not client_requests:
            return None
        
        oldest_request = client_requests[0]
        reset_time = datetime.fromtimestamp(oldest_request + self.window_seconds)
        return reset_time


class CSRFProtection:
    """CSRF保护"""
    
    def __init__(self, secret_key: str = None):
        """
        初始化CSRF保护
        
        Args:
            secret_key: 密钥，用于生成和验证token
        """
        self.secret_key = secret_key or secrets.token_urlsafe(32)
        self.tokens = {}  # 存储有效的token
        self.token_lifetime = 3600  # token有效期（秒）
    
    def generate_token(self, session_id: str) -> str:
        """
        生成CSRF token
        
        Args:
            session_id: 会话ID
            
        Returns:
            CSRF token
        """
        timestamp = str(int(time.time()))
        data = f"{session_id}:{timestamp}:{self.secret_key}"
        token = hashlib.sha256(data.encode()).hexdigest()
        
        # 存储token
        self.tokens[token] = {
            'session_id': session_id,
            'timestamp': int(timestamp)
        }
        
        return token
    
    def verify_token(self, token: str, session_id: str) -> bool:
        """
        验证CSRF token
        
        Args:
            token: 要验证的token
            session_id: 会话ID
            
        Returns:
            是否有效
        """
        if not token or token not in self.tokens:
            return False
        
        token_info = self.tokens[token]
        
        # 检查会话ID
        if token_info['session_id'] != session_id:
            return False
        
        # 检查是否过期
        if time.time() - token_info['timestamp'] > self.token_lifetime:
            del self.tokens[token]
            return False
        
        return True
    
    def cleanup_expired_tokens(self):
        """清理过期的token"""
        current_time = time.time()
        expired_tokens = [
            token for token, info in self.tokens.items()
            if current_time - info['timestamp'] > self.token_lifetime
        ]
        
        for token in expired_tokens:
            del self.tokens[token]


class XSSProtection:
    """XSS防护"""
    
    # 允许的HTML标签和属性
    ALLOWED_TAGS = [
        'p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li',
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'blockquote', 'code', 'pre'
    ]
    
    ALLOWED_ATTRIBUTES = {
        '*': ['class'],
        'a': ['href', 'title'],
        'img': ['src', 'alt', 'width', 'height']
    }
    
    @classmethod
    def sanitize_html(cls, html_content: str, strip_tags: bool = False) -> str:
        """
        清理HTML内容，防止XSS攻击
        
        Args:
            html_content: 要清理的HTML内容
            strip_tags: 是否完全移除HTML标签
            
        Returns:
            清理后的内容
        """
        if not html_content:
            return ""
        
        if strip_tags:
            # 完全移除HTML标签
            return bleach.clean(html_content, tags=[], strip=True)
        else:
            # 只允许安全的HTML标签
            return bleach.clean(
                html_content,
                tags=cls.ALLOWED_TAGS,
                attributes=cls.ALLOWED_ATTRIBUTES,
                strip=True
            )
    
    @classmethod
    def sanitize_input(cls, user_input: str) -> str:
        """
        清理用户输入，防止XSS攻击
        
        Args:
            user_input: 用户输入
            
        Returns:
            清理后的输入
        """
        if not user_input:
            return ""
        
        # 转义HTML特殊字符
        escaped = (user_input
                  .replace('&', '&amp;')
                  .replace('<', '&lt;')
                  .replace('>', '&gt;')
                  .replace('"', '&quot;')
                  .replace("'", '&#x27;')
                  .replace('/', '&#x2F;'))
        
        return escaped
    
    @classmethod
    def validate_json_input(cls, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证和清理JSON输入
        
        Args:
            json_data: JSON数据
            
        Returns:
            清理后的JSON数据
        """
        if not isinstance(json_data, dict):
            return {}
        
        cleaned_data = {}
        for key, value in json_data.items():
            # 清理键名
            clean_key = cls.sanitize_input(str(key))
            
            # 清理值
            if isinstance(value, str):
                clean_value = cls.sanitize_input(value)
            elif isinstance(value, dict):
                clean_value = cls.validate_json_input(value)
            elif isinstance(value, list):
                clean_value = [
                    cls.sanitize_input(str(item)) if isinstance(item, str) else item
                    for item in value
                ]
            else:
                clean_value = value
            
            cleaned_data[clean_key] = clean_value
        
        return cleaned_data


class SecurityMiddleware:
    """安全中间件"""
    
    def __init__(self, 
                 rate_limit_requests: int = 100,
                 rate_limit_window: int = 60,
                 csrf_secret_key: str = None):
        """
        初始化安全中间件
        
        Args:
            rate_limit_requests: 速率限制请求数
            rate_limit_window: 速率限制时间窗口
            csrf_secret_key: CSRF密钥
        """
        self.rate_limiter = RateLimiter(rate_limit_requests, rate_limit_window)
        self.csrf_protection = CSRFProtection(csrf_secret_key)
        self.xss_protection = XSSProtection()
        
    def get_client_id(self, request: Request) -> str:
        """获取客户端标识"""
        # 优先使用X-Forwarded-For头（代理环境）
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            return forwarded_for.split(',')[0].strip()
        
        # 使用X-Real-IP头
        real_ip = request.headers.get('X-Real-IP')
        if real_ip:
            return real_ip
        
        # 使用客户端IP
        return request.client.host if request.client else 'unknown'
    
    async def check_rate_limit(self, request: Request) -> None:
        """检查速率限制"""
        client_id = self.get_client_id(request)
        
        if not self.rate_limiter.is_allowed(client_id):
            remaining = self.rate_limiter.get_remaining_requests(client_id)
            reset_time = self.rate_limiter.get_reset_time(client_id)
            
            logger.warning(f"速率限制触发: {client_id}")
            
            raise HTTPException(
                status_code=429,
                detail="请求过于频繁，请稍后重试",
                headers={
                    'X-RateLimit-Limit': str(self.rate_limiter.max_requests),
                    'X-RateLimit-Remaining': str(remaining),
                    'X-RateLimit-Reset': str(int(reset_time.timestamp())) if reset_time else '',
                    'Retry-After': str(self.rate_limiter.window_seconds)
                }
            )
    
    def generate_csrf_token(self, session_id: str) -> str:
        """生成CSRF token"""
        return self.csrf_protection.generate_token(session_id)
    
    async def verify_csrf_token(self, request: Request, session_id: str) -> None:
        """验证CSRF token"""
        # 对于GET请求，不需要CSRF验证
        if request.method == 'GET':
            return
        
        # 获取token
        token = request.headers.get('X-CSRF-Token')
        if not token:
            # 尝试从表单数据中获取
            if hasattr(request, '_body'):
                body = await request.body()
                if b'csrf_token=' in body:
                    # 简单解析（实际应用中应该使用更robust的解析）
                    token_start = body.find(b'csrf_token=') + len(b'csrf_token=')
                    token_end = body.find(b'&', token_start)
                    if token_end == -1:
                        token_end = len(body)
                    token = body[token_start:token_end].decode()
        
        if not token or not self.csrf_protection.verify_token(token, session_id):
            logger.warning(f"CSRF验证失败: {self.get_client_id(request)}")
            raise HTTPException(
                status_code=403,
                detail="CSRF验证失败"
            )
    
    def sanitize_request_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清理请求数据"""
        return self.xss_protection.validate_json_input(data)


# 全局安全中间件实例
security_middleware = SecurityMiddleware()


def get_security_middleware() -> SecurityMiddleware:
    """获取安全中间件实例"""
    return security_middleware
