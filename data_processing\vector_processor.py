"""
向量处理模块

负责文本和图像的向量化、相似度计算等功能
支持FAISS和Chroma向量数据库
"""

import os
import pickle
import re
import json
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import numpy as np
import faiss
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from loguru import logger

# SentenceTransformer作为可选依赖
SentenceTransformer = None
SENTENCE_TRANSFORMERS_AVAILABLE = False
NETWORK_FAILED_GLOBAL = False  # 全局网络失败标记

# 临时禁用SentenceTransformers以避免网络连接问题
# 直接使用TF-IDF作为向量化方案
logger.info("临时禁用SentenceTransformers，使用TF-IDF向量化器")

# try:
#     from sentence_transformers import SentenceTransformer as _SentenceTransformer
#     SentenceTransformer = _SentenceTransformer
#     SENTENCE_TRANSFORMERS_AVAILABLE = True
#     logger.info("SentenceTransformers可用")
# except ImportError as e:
#     logger.warning(f"SentenceTransformers不可用: {e}，将使用TF-IDF备选方案")
# except Exception as e:
#     logger.warning(f"SentenceTransformers加载失败: {e}，将使用TF-IDF备选方案")

# Chroma数据库支持
try:
    import chromadb
    from chromadb.config import Settings
    CHROMA_AVAILABLE = True
except ImportError:
    CHROMA_AVAILABLE = False
    logger.warning("Chroma未安装，将使用FAISS作为向量数据库")


class VectorProcessor:
    """向量处理器 - 支持FAISS和Chroma向量数据库"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.model_name = config.get("model_name", "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")
        # 如果model_path为空字符串或None，设为None以使用在线模型
        model_path = config.get("model_path", "")
        self.model_path = model_path if model_path and model_path.strip() else None
        self.dimension = config.get("dimension", 384)
        self.batch_size = config.get("batch_size", 32)
        self.device = config.get("device", "cpu")

        # 向量化优化配置
        self.normalize_embeddings = config.get("normalize_embeddings", True)
        self.use_gpu = config.get("use_gpu", False) and self._check_gpu_availability()

        # 向量数据库配置
        self.vector_db_type = config.get("vector_db_type", "faiss")  # faiss 或 chroma
        self.chroma_path = config.get("chroma_path", "./embeddings/chroma_store")
        self.chroma_collection_name = config.get("chroma_collection_name", "power_fault_collection")

        # FAISS索引优化配置
        self.index_type = config.get("index_type", "IndexHNSWFlat")  # 默认使用HNSW索引
        self.hnsw_m = config.get("hnsw_m", 16)  # HNSW连接数
        self.hnsw_ef_construction = config.get("hnsw_ef_construction", 200)  # 构建时搜索参数
        self.hnsw_ef_search = config.get("hnsw_ef_search", 100)  # 搜索时参数

        # 初始化向量数据库
        self.chroma_client = None
        self.chroma_collection = None
        if self.vector_db_type == "chroma" and CHROMA_AVAILABLE:
            self._init_chroma_db()

        # 初始化嵌入模型
        self._init_embedding_model()

        # 初始化文本预处理器
        self._init_text_preprocessor()

    def _check_gpu_availability(self) -> bool:
        """检查GPU可用性"""
        try:
            import torch
            return torch.cuda.is_available()
        except ImportError:
            return False

    def _check_network_connection(self) -> bool:
        """检查网络连接是否可用"""
        try:
            import socket
            import urllib.request

            # 设置短超时
            socket.setdefaulttimeout(3)

            # 尝试连接HuggingFace镜像源
            try:
                urllib.request.urlopen('https://hf-mirror.com', timeout=3)
                return True
            except:
                pass

            # 尝试连接原始HuggingFace
            try:
                urllib.request.urlopen('https://huggingface.co', timeout=3)
                return True
            except:
                pass

            return False

        except Exception:
            return False

    def _init_chroma_db(self):
        """初始化Chroma向量数据库"""
        try:
            if not CHROMA_AVAILABLE:
                logger.error("Chroma未安装，无法初始化Chroma数据库")
                return False

            # 创建Chroma客户端（本地化部署）
            os.makedirs(self.chroma_path, exist_ok=True)

            # 配置Chroma设置（内网本地化部署）
            settings = Settings(
                chroma_db_impl="duckdb+parquet",
                persist_directory=self.chroma_path,
                anonymized_telemetry=False  # 关闭遥测（内网安全要求）
            )

            self.chroma_client = chromadb.Client(settings)

            # 创建或获取集合
            try:
                self.chroma_collection = self.chroma_client.get_collection(
                    name=self.chroma_collection_name
                )
                logger.info(f"加载现有Chroma集合: {self.chroma_collection_name}")
            except:
                # 集合不存在，创建新集合
                self.chroma_collection = self.chroma_client.create_collection(
                    name=self.chroma_collection_name,
                    metadata={"description": "电力故障诊断知识库"}
                )
                logger.info(f"创建新Chroma集合: {self.chroma_collection_name}")

            logger.info(f"Chroma数据库初始化成功，存储路径: {self.chroma_path}")
            return True

        except Exception as e:
            logger.error(f"Chroma数据库初始化失败: {str(e)}")
            self.chroma_client = None
            self.chroma_collection = None
            return False

    def _init_text_preprocessor(self):
        """初始化文本预处理器"""
        # 电力专业术语词典
        self.power_terms = {
            '变压器', '断路器', '隔离开关', '电流互感器', '电压互感器',
            '避雷器', '电容器', '电抗器', '母线', '导线',
            '故障', '跳闸', '合闸', '保护', '动作', '运行', '检修',
            '绝缘', '接地', '短路', '过载', '过压', '欠压', '频率',
            'kV', 'MV', 'kW', 'MW', 'MVA', 'Hz', 'A', 'V', 'Ω'
        }

        # 停用词（针对电力领域优化）
        self.stop_words = {
            '的', '了', '在', '是', '有', '和', '与', '及', '或', '但',
            '因为', '所以', '如果', '那么', '这样', '那样', '这个', '那个',
            '可以', '应该', '需要', '必须', '能够', '可能', '也许', '大概'
        }

    def _init_embedding_model(self):
        """初始化嵌入模型"""
        global NETWORK_FAILED_GLOBAL  # 在函数开始就声明global

        self.embedding_model = None

        # 检查是否为离线模式
        if os.environ.get('OFFLINE_MODE') == '1' or os.environ.get('USE_TFIDF_ONLY') == '1':
            logger.info("离线模式：直接使用TF-IDF向量化器")
            self._init_fallback_vectorizer()
            return

        # 检查SentenceTransformers是否可用
        if not SENTENCE_TRANSFORMERS_AVAILABLE or SentenceTransformer is None:
            logger.warning("SentenceTransformers不可用，直接使用TF-IDF备选方案")
            self._init_fallback_vectorizer()
            return

        # 检查全局网络失败标记
        if NETWORK_FAILED_GLOBAL:
            logger.warning("全局网络连接失败标记已设置，直接使用TF-IDF备选方案")
            self._init_fallback_vectorizer()
            return

        try:
            # 尝试加载配置文件中的模型设置
            config_path = "configs/embedding_config.json"
            if os.path.exists(config_path):
                try:
                    import json
                    with open(config_path, 'r', encoding='utf-8') as f:
                        embedding_config = json.load(f)

                    primary_model = embedding_config["embedding_models"]["primary"]
                    if primary_model["status"] == "online":
                        # 使用配置文件中的模型
                        self.model_name = primary_model["path"]
                        logger.info(f"使用配置文件中的模型: {self.model_name}")
                except Exception as config_error:
                    logger.warning(f"配置文件加载失败: {config_error}")

            logger.info(f"初始化嵌入模型: {self.model_name}")

            # 尝试从本地路径加载（如果存在）
            if self.model_path and os.path.exists(self.model_path):
                try:
                    self.embedding_model = SentenceTransformer(self.model_path, device=self.device)
                    logger.info(f"从本地路径加载嵌入模型: {self.model_path}")
                    return
                except Exception as local_error:
                    logger.warning(f"本地模型加载失败: {local_error}")

            # 直接尝试加载，如果失败则快速回退
            # 不进行网络检测，因为检测本身也会触发网络连接

            # 尝试加载模型，设置严格的超时限制
            try:
                logger.info("尝试加载SentenceTransformer模型...")

                # 设置极短的超时时间，快速失败
                import socket
                original_timeout = socket.getdefaulttimeout()
                socket.setdefaulttimeout(2)  # 2秒超时，快速失败

                # 设置镜像源
                os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

                # 尝试加载模型
                self.embedding_model = SentenceTransformer(
                    self.model_name,
                    device=self.device,
                    cache_folder=None
                )

                # 恢复原始超时设置
                socket.setdefaulttimeout(original_timeout)

                logger.info(f"成功加载嵌入模型: {self.model_name}")

                # 如果指定了本地路径，保存模型
                if self.model_path and os.path.dirname(self.model_path):
                    try:
                        os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
                        self.embedding_model.save(self.model_path)
                        logger.info(f"模型已保存到: {self.model_path}")
                    except Exception as save_error:
                        logger.warning(f"模型保存失败: {save_error}")

                return  # 成功加载，退出

            except Exception as download_error:
                # 恢复原始超时设置
                socket.setdefaulttimeout(original_timeout)
                logger.warning(f"SentenceTransformer加载失败: {download_error}")
                # 设置全局网络失败标记，避免后续重试
                NETWORK_FAILED_GLOBAL = True
                # 立即回退到TF-IDF，不再尝试其他镜像源

            # 所有镜像源都失败，使用备选方案
            logger.warning("所有在线模型加载失败，使用TF-IDF向量化作为备选方案")
            self._init_fallback_vectorizer()

        except Exception as e:
            logger.error(f"嵌入模型初始化失败: {str(e)}")
            # 使用备选方案
            self._init_fallback_vectorizer()

    def _init_fallback_vectorizer(self):
        """初始化备选的TF-IDF向量化器"""
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer

            # 中文停用词列表
            chinese_stopwords = [
                '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'
            ]

            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=2000,  # 增加特征数量
                ngram_range=(1, 2),  # 支持1-2元组，减少复杂度
                stop_words=chinese_stopwords,
                lowercase=True,
                analyzer='word',
                min_df=1,  # 最小文档频率
                max_df=0.8,  # 降低最大文档频率阈值
                sublinear_tf=True,  # 使用对数TF缩放
                token_pattern=r'[\u4e00-\u9fff]+|[a-zA-Z0-9]+',  # 支持中文和英文
                binary=False  # 使用TF权重而不是二进制
            )

            self.embedding_model = None  # 标记使用备选方案
            logger.info("TF-IDF向量化器初始化成功（增强版）")

        except ImportError:
            logger.error("sklearn未安装，无法使用TF-IDF备选方案")
            self.tfidf_vectorizer = None
            self.embedding_model = None
    
    def encode_texts(self, texts: List[str]) -> np.ndarray:
        """
        对文本进行向量化编码

        Args:
            texts: 文本列表

        Returns:
            向量矩阵
        """
        try:
            if not texts:
                return np.array([], dtype=np.float32).reshape(0, 0)

            # 过滤空文本
            valid_texts = [text for text in texts if text and text.strip()]
            if not valid_texts:
                logger.warning("所有文本都为空")
                return np.array([], dtype=np.float32).reshape(0, 0)

            # 使用SentenceTransformer模型
            if self.embedding_model is not None:
                embeddings = self.embedding_model.encode(
                    valid_texts,
                    batch_size=self.batch_size,
                    show_progress_bar=True,
                    convert_to_numpy=True
                )
                # 确保数据类型和内存布局
                embeddings = embeddings.astype(np.float32)
                if not embeddings.flags['C_CONTIGUOUS']:
                    embeddings = np.ascontiguousarray(embeddings)

                logger.info(f"成功编码 {len(valid_texts)} 个文本，向量维度: {embeddings.shape[1]}")
                return embeddings

            # 使用TF-IDF备选方案
            elif hasattr(self, 'tfidf_vectorizer') and self.tfidf_vectorizer is not None:
                try:
                    # 检查文本数量，如果太少则使用简单向量化
                    if len(valid_texts) < 2:
                        logger.warning(f"文本数量太少({len(valid_texts)})，使用简单向量化")
                        return self._simple_text_vectorize(valid_texts)

                    # 如果是第一次使用，需要先fit
                    if not hasattr(self.tfidf_vectorizer, 'vocabulary_'):
                        self.tfidf_vectorizer.fit(valid_texts)

                    embeddings = self.tfidf_vectorizer.transform(valid_texts).toarray()
                    embeddings = embeddings.astype(np.float32)
                    if not embeddings.flags['C_CONTIGUOUS']:
                        embeddings = np.ascontiguousarray(embeddings)

                    logger.info(f"使用TF-IDF编码 {len(valid_texts)} 个文本，向量维度: {embeddings.shape[1]}")
                    return embeddings
                except Exception as tfidf_error:
                    logger.warning(f"TF-IDF编码失败: {tfidf_error}，使用简单向量化")
                    return self._simple_text_vectorize(valid_texts)

            else:
                # 最后的备选方案：简单的词频向量
                logger.warning("使用简单词频向量作为最后备选方案")
                return self._simple_text_vectorize(valid_texts)

        except Exception as e:
            logger.error(f"文本编码失败: {str(e)}")
            # 返回简单向量而不是抛出异常
            return self._simple_text_vectorize(texts if texts else [""])

    def _simple_text_vectorize(self, texts: List[str]) -> np.ndarray:
        """简单的文本向量化备选方案"""
        try:
            if not texts:
                return np.array([], dtype=np.float32).reshape(0, 0)

            # 过滤空文本
            valid_texts = [text for text in texts if text and text.strip()]
            if not valid_texts:
                logger.warning("所有文本都为空，返回零向量")
                return np.zeros((len(texts), 100), dtype=np.float32)

            # 创建简单的词汇表
            all_words = set()
            for text in valid_texts:
                words = text.lower().split()
                all_words.update(words)

            vocab = list(all_words)[:1000]  # 限制词汇表大小

            if not vocab:
                logger.warning("词汇表为空，返回零向量")
                return np.zeros((len(texts), 100), dtype=np.float32)

            # 创建词频向量
            vectors = []
            for text in texts:
                if text and text.strip():
                    words = text.lower().split()
                    vector = [words.count(word) for word in vocab]
                else:
                    vector = [0] * len(vocab)
                vectors.append(vector)

            embeddings = np.array(vectors, dtype=np.float32)

            # 确保内存布局连续
            if not embeddings.flags['C_CONTIGUOUS']:
                embeddings = np.ascontiguousarray(embeddings)

            logger.info(f"使用简单词频向量编码 {len(texts)} 个文本，向量维度: {embeddings.shape[1]}")
            return embeddings

        except Exception as e:
            logger.error(f"简单向量化也失败: {str(e)}")
            # 返回零向量
            return np.zeros((len(texts) if texts else 1, 100), dtype=np.float32)

    def encode_single_text(self, text: str) -> np.ndarray:
        """
        对单个文本进行向量化编码

        Args:
            text: 输入文本

        Returns:
            向量
        """
        try:
            if not text or not text.strip():
                logger.warning("输入文本为空")
                return np.array([], dtype=np.float32)

            if self.embedding_model is not None:
                embedding = self.embedding_model.encode([text], convert_to_numpy=True)
                result = embedding[0].astype(np.float32)

                # 确保内存布局连续
                if not result.flags['C_CONTIGUOUS']:
                    result = np.ascontiguousarray(result)

                return result
            else:
                # 使用备选方案
                embeddings = self.encode_texts([text])
                if len(embeddings) > 0:
                    return embeddings[0]
                else:
                    return np.array([], dtype=np.float32)

        except Exception as e:
            logger.error(f"单文本编码失败: {str(e)}")
            return np.array([], dtype=np.float32)
    
    def calculate_similarity(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """
        计算两个向量的余弦相似度
        
        Args:
            vector1: 向量1
            vector2: 向量2
            
        Returns:
            相似度分数
        """
        try:
            # 归一化向量
            norm1 = np.linalg.norm(vector1)
            norm2 = np.linalg.norm(vector2)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
            
            # 计算余弦相似度
            similarity = np.dot(vector1, vector2) / (norm1 * norm2)
            return float(similarity)
            
        except Exception as e:
            logger.error(f"相似度计算失败: {str(e)}")
            return 0.0
    
    def batch_similarity(self, query_vector: np.ndarray, vectors: np.ndarray) -> np.ndarray:
        """
        批量计算相似度
        
        Args:
            query_vector: 查询向量
            vectors: 向量矩阵
            
        Returns:
            相似度数组
        """
        try:
            # 归一化
            query_norm = query_vector / np.linalg.norm(query_vector)
            vectors_norm = vectors / np.linalg.norm(vectors, axis=1, keepdims=True)
            
            # 计算余弦相似度
            similarities = np.dot(vectors_norm, query_norm)
            return similarities
            
        except Exception as e:
            logger.error(f"批量相似度计算失败: {str(e)}")
            return np.array([])
    
    def create_faiss_index(self, vectors: np.ndarray, index_type: str = None) -> faiss.Index:
        """
        创建优化的FAISS索引 - 根据数据规模自动选择最佳索引类型

        Args:
            vectors: 向量矩阵
            index_type: 索引类型（可选，自动选择）

        Returns:
            FAISS索引
        """
        try:
            if vectors.size == 0:
                raise ValueError("向量矩阵为空")

            dimension = vectors.shape[1]
            num_vectors = vectors.shape[0]

            # 自动选择索引类型
            if index_type is None:
                index_type = self._select_optimal_index_type(num_vectors, dimension)

            logger.info(f"创建FAISS索引: 类型={index_type}, 向量数量={num_vectors}, 维度={dimension}")

            # 预处理向量
            processed_vectors = self._preprocess_vectors_for_index(vectors, index_type)

            # 根据索引类型创建索引
            index = self._create_index_by_type(index_type, dimension, num_vectors)

            # 训练索引（如果需要）
            if hasattr(index, 'train') and hasattr(index, 'is_trained') and not index.is_trained:
                logger.info("训练FAISS索引...")
                index.train(processed_vectors)

            # 添加向量到索引
            index.add(processed_vectors)

            # 优化索引参数
            self._optimize_index_parameters(index, index_type)

            logger.info(f"成功创建FAISS索引，类型: {index_type}, 向量数量: {index.ntotal}")
            return index

        except Exception as e:
            logger.error(f"FAISS索引创建失败: {str(e)}")
            raise

    def _select_optimal_index_type(self, num_vectors: int, dimension: int) -> str:
        """根据数据规模自动选择最佳索引类型"""
        # 小规模数据：使用精确搜索
        if num_vectors < 1000:
            return "IndexFlatIP"

        # 中等规模数据：使用HNSW索引（速度和精度平衡）
        elif num_vectors < 100000:
            return "IndexHNSWFlat"

        # 大规模数据：使用IVF索引（内存效率高）
        else:
            return "IndexIVFFlat"

    def _preprocess_vectors_for_index(self, vectors: np.ndarray, index_type: str) -> np.ndarray:
        """为索引预处理向量"""
        try:
            # 验证输入
            if not isinstance(vectors, np.ndarray):
                logger.error("输入不是numpy数组")
                return np.array([])

            if vectors.size == 0:
                logger.error("向量数组为空")
                return np.array([])

            # 确保数据类型
            vectors = vectors.astype(np.float32)

            # 确保内存布局连续
            if not vectors.flags['C_CONTIGUOUS']:
                vectors = np.ascontiguousarray(vectors)

            # 对于内积索引，需要归一化向量
            if "IP" in index_type:
                # 创建副本以避免修改原始数据
                vectors_copy = vectors.copy()
                faiss.normalize_L2(vectors_copy)
                return vectors_copy

            return vectors

        except Exception as e:
            logger.error(f"向量预处理失败: {str(e)}")
            return np.array([])

    def _create_index_by_type(self, index_type: str, dimension: int, num_vectors: int) -> faiss.Index:
        """根据类型创建索引"""
        if index_type == "IndexFlatIP":
            return faiss.IndexFlatIP(dimension)

        elif index_type == "IndexFlatL2":
            return faiss.IndexFlatL2(dimension)

        elif index_type == "IndexHNSWFlat":
            index = faiss.IndexHNSWFlat(dimension, self.hnsw_m)
            index.hnsw.efConstruction = self.hnsw_ef_construction
            return index

        elif index_type == "IndexIVFFlat":
            # 计算合适的聚类中心数量
            nlist = min(max(int(np.sqrt(num_vectors)), 10), 1000)
            quantizer = faiss.IndexFlatL2(dimension)
            return faiss.IndexIVFFlat(quantizer, dimension, nlist)

        elif index_type == "IndexIVFPQ":
            # 使用乘积量化压缩
            nlist = min(max(int(np.sqrt(num_vectors)), 10), 1000)
            m = min(dimension // 4, 64)  # 子向量数量
            quantizer = faiss.IndexFlatL2(dimension)
            return faiss.IndexIVFPQ(quantizer, dimension, nlist, m, 8)

        else:
            logger.warning(f"不支持的索引类型: {index_type}，使用默认IndexFlatIP")
            return faiss.IndexFlatIP(dimension)

    def _optimize_index_parameters(self, index: faiss.Index, index_type: str):
        """优化索引参数"""
        if index_type == "IndexHNSWFlat":
            # 设置搜索参数
            index.hnsw.efSearch = self.hnsw_ef_search

        elif index_type.startswith("IndexIVF"):
            # 设置IVF搜索参数
            if hasattr(index, 'nprobe'):
                index.nprobe = min(max(index.nlist // 10, 1), 50)
    
    def search_similar(self, index: faiss.Index, query_vector: np.ndarray, k: int = 10) -> tuple:
        """
        在FAISS索引中搜索相似向量

        Args:
            index: FAISS索引
            query_vector: 查询向量
            k: 返回结果数量

        Returns:
            (相似度分数, 索引位置)
        """
        try:
            # 验证输入
            if not isinstance(query_vector, np.ndarray):
                logger.error("查询向量不是numpy数组")
                return np.array([]), np.array([])

            if query_vector.size == 0:
                logger.error("查询向量为空")
                return np.array([]), np.array([])

            # 重塑和转换查询向量
            query_vector = query_vector.reshape(1, -1).astype(np.float32)

            # 确保内存布局连续
            if not query_vector.flags['C_CONTIGUOUS']:
                query_vector = np.ascontiguousarray(query_vector)

            # 归一化查询向量
            query_copy = query_vector.copy()
            faiss.normalize_L2(query_copy)

            # 搜索
            scores, indices = index.search(query_copy, k)

            return scores[0], indices[0]

        except Exception as e:
            logger.error(f"向量搜索失败: {str(e)}")
            return np.array([]), np.array([])
    
    def save_index(self, index: faiss.Index, file_path: str):
        """
        保存FAISS索引到文件
        
        Args:
            index: FAISS索引
            file_path: 保存路径
        """
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            faiss.write_index(index, file_path)
            logger.info(f"FAISS索引已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"索引保存失败: {str(e)}")
    
    def load_index(self, file_path: str) -> Optional[faiss.Index]:
        """
        从文件加载FAISS索引
        
        Args:
            file_path: 索引文件路径
            
        Returns:
            FAISS索引
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"索引文件不存在: {file_path}")
                return None
            
            index = faiss.read_index(file_path)
            logger.info(f"成功加载FAISS索引: {file_path}, 向量数量: {index.ntotal}")
            return index
            
        except Exception as e:
            logger.error(f"索引加载失败: {str(e)}")
            return None
    
    def save_vectors_metadata(self, vectors: np.ndarray, metadata: List[Dict[str, Any]], file_path: str):
        """
        保存向量和元数据
        
        Args:
            vectors: 向量矩阵
            metadata: 元数据列表
            file_path: 保存路径
        """
        try:
            data = {
                "vectors": vectors,
                "metadata": metadata,
                "dimension": vectors.shape[1] if len(vectors) > 0 else 0,
                "count": len(vectors)
            }
            
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'wb') as f:
                pickle.dump(data, f)
            
            logger.info(f"向量和元数据已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"向量元数据保存失败: {str(e)}")
    
    def load_vectors_metadata(self, file_path: str) -> tuple:
        """
        加载向量和元数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            (向量矩阵, 元数据列表)
        """
        try:
            if not os.path.exists(file_path):
                logger.error(f"向量文件不存在: {file_path}")
                return np.array([]), []
            
            with open(file_path, 'rb') as f:
                data = pickle.load(f)
            
            vectors = data.get("vectors", np.array([]))
            metadata = data.get("metadata", [])
            
            logger.info(f"成功加载向量和元数据: {file_path}, 向量数量: {len(vectors)}")
            return vectors, metadata
            
        except Exception as e:
            logger.error(f"向量元数据加载失败: {str(e)}")
            return np.array([]), []
    
    def process_documents(self, documents: List[Dict[str, Any]]) -> tuple:
        """
        处理文档列表，生成向量和元数据

        Args:
            documents: 文档列表

        Returns:
            (向量矩阵, 元数据列表)
        """
        try:
            if not documents:
                logger.warning("文档列表为空")
                return np.array([]), []

            logger.info(f"开始处理 {len(documents)} 个文档")

            # 提取文本内容
            texts = [doc.get("content", "") for doc in documents]

            # 检查文本内容
            valid_texts = []
            for i, text in enumerate(texts):
                if text and text.strip():
                    valid_texts.append(text)
                    logger.info(f"文档 {i} 内容长度: {len(text)} 字符")
                else:
                    logger.warning(f"文档 {i} 内容为空")

            if not valid_texts:
                logger.error("所有文档内容都为空")
                return np.array([]), []

            # 向量化
            logger.info(f"开始向量化 {len(valid_texts)} 个有效文本")
            vectors = self.encode_texts(texts)

            if vectors.size == 0:
                logger.error("向量化失败，返回空向量")
                return np.array([]), []

            logger.info(f"向量化成功，向量形状: {vectors.shape}")

            # 构建元数据
            metadata = []
            for i, doc in enumerate(documents):
                meta = {
                    "index": i,
                    "source": doc.get("source", ""),
                    "chunk_id": doc.get("chunk_id", 0),
                    "content": doc.get("content", ""),
                    "metadata": doc.get("metadata", {})
                }
                metadata.append(meta)

            logger.info(f"成功处理 {len(documents)} 个文档，生成 {len(metadata)} 个元数据")
            return vectors, metadata

        except Exception as e:
            logger.error(f"文档处理失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return np.array([]), []

    def add_documents_to_chroma(self, documents: List[Dict[str, Any]]) -> bool:
        """
        将文档添加到Chroma数据库

        Args:
            documents: 文档列表

        Returns:
            是否成功
        """
        try:
            if not self.chroma_collection:
                logger.error("Chroma集合未初始化")
                return False

            if not documents:
                logger.warning("文档列表为空")
                return True

            # 提取文本内容
            texts = [doc.get("content", "") for doc in documents]

            # 生成向量
            embeddings = self.encode_texts(texts)
            if embeddings.size == 0:
                logger.error("向量生成失败")
                return False

            # 准备元数据
            metadatas = []
            ids = []

            for i, doc in enumerate(documents):
                # 生成唯一ID
                doc_id = doc.get("id", f"doc_{i}_{hash(doc.get('content', ''))}")
                ids.append(str(doc_id))

                # 准备元数据（Chroma要求所有值都是字符串、数字或布尔值）
                metadata = {
                    "source": str(doc.get("source", "")),
                    "chunk_id": int(doc.get("chunk_id", i)),
                    "equipment_type": str(doc.get("metadata", {}).get("equipment_type", "")),
                    "fault_type": str(doc.get("metadata", {}).get("fault_type", "")),
                    "location": str(doc.get("metadata", {}).get("location", "白银")),
                    "timestamp": str(doc.get("metadata", {}).get("timestamp", ""))
                }
                metadatas.append(metadata)

            # 批量添加到Chroma
            self.chroma_collection.add(
                documents=texts,
                embeddings=embeddings.tolist(),
                metadatas=metadatas,
                ids=ids
            )

            logger.info(f"成功添加 {len(documents)} 个文档到Chroma数据库")
            return True

        except Exception as e:
            logger.error(f"添加文档到Chroma失败: {str(e)}")
            return False

    def search_chroma(self, query: str, n_results: int = 10, where: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        在Chroma数据库中搜索

        Args:
            query: 查询文本
            n_results: 返回结果数量
            where: 过滤条件

        Returns:
            搜索结果列表
        """
        try:
            if not self.chroma_collection:
                logger.error("Chroma集合未初始化")
                return []

            # 生成查询向量
            query_embedding = self.encode_texts([query])
            if query_embedding.size == 0:
                logger.error("查询向量生成失败")
                return []

            # 执行搜索
            results = self.chroma_collection.query(
                query_embeddings=query_embedding.tolist(),
                n_results=n_results,
                where=where,
                include=["documents", "metadatas", "distances"]
            )

            # 格式化结果
            formatted_results = []
            if results["documents"] and len(results["documents"]) > 0:
                documents = results["documents"][0]
                metadatas = results["metadatas"][0] if results["metadatas"] else [{}] * len(documents)
                distances = results["distances"][0] if results["distances"] else [0.0] * len(documents)

                for i, (doc, metadata, distance) in enumerate(zip(documents, metadatas, distances)):
                    formatted_results.append({
                        "content": doc,
                        "metadata": metadata,
                        "score": 1.0 - distance,  # 转换为相似度分数
                        "distance": distance,
                        "rank": i + 1
                    })

            logger.info(f"Chroma搜索完成，返回 {len(formatted_results)} 个结果")
            return formatted_results

        except Exception as e:
            logger.error(f"Chroma搜索失败: {str(e)}")
            return []

    def get_chroma_stats(self) -> Dict[str, Any]:
        """
        获取Chroma数据库统计信息

        Returns:
            统计信息
        """
        try:
            if not self.chroma_collection:
                return {"error": "Chroma集合未初始化"}

            count = self.chroma_collection.count()

            return {
                "collection_name": self.chroma_collection_name,
                "document_count": count,
                "storage_path": self.chroma_path,
                "status": "active"
            }

        except Exception as e:
            logger.error(f"获取Chroma统计信息失败: {str(e)}")
            return {"error": str(e)}
