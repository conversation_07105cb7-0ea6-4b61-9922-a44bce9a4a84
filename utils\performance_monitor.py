
import time
import psutil
import threading
from datetime import datetime
from typing import Dict, List
import json

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, interval: int = 60):
        self.interval = interval
        self.metrics: List[Dict] = []
        self.running = False
        self.monitor_thread = None
    
    def start(self):
        """启动监控"""
        if not self.running:
            self.running = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("✅ 性能监控已启动")
    
    def stop(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("⏹️ 性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.running:
            try:
                metrics = self._collect_metrics()
                self.metrics.append(metrics)
                
                # 保持最近1000条记录
                if len(self.metrics) > 1000:
                    self.metrics = self.metrics[-1000:]
                
                # 检查异常情况
                self._check_alerts(metrics)
                
                time.sleep(self.interval)
                
            except Exception as e:
                print(f"监控异常: {e}")
                time.sleep(self.interval)
    
    def _collect_metrics(self) -> Dict:
        """收集性能指标"""
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('.').percent,
            "network_io": dict(psutil.net_io_counters()._asdict()),
            "process_count": len(psutil.pids())
        }
    
    def _check_alerts(self, metrics: Dict):
        """检查告警条件"""
        alerts = []
        
        if metrics["cpu_percent"] > 80:
            alerts.append(f"CPU使用率过高: {metrics['cpu_percent']}%")
        
        if metrics["memory_percent"] > 85:
            alerts.append(f"内存使用率过高: {metrics['memory_percent']}%")
        
        if metrics["disk_percent"] > 90:
            alerts.append(f"磁盘使用率过高: {metrics['disk_percent']}%")
        
        if alerts:
            print(f"⚠️ 性能告警: {'; '.join(alerts)}")
    
    def get_summary(self) -> Dict:
        """获取性能摘要"""
        if not self.metrics:
            return {}
        
        recent_metrics = self.metrics[-10:]  # 最近10条记录
        
        return {
            "avg_cpu": sum(m["cpu_percent"] for m in recent_metrics) / len(recent_metrics),
            "avg_memory": sum(m["memory_percent"] for m in recent_metrics) / len(recent_metrics),
            "avg_disk": sum(m["disk_percent"] for m in recent_metrics) / len(recent_metrics),
            "total_records": len(self.metrics),
            "monitoring_duration": len(self.metrics) * self.interval / 3600  # 小时
        }
    
    def save_report(self, filename: str = "performance_report.json"):
        """保存性能报告"""
        report = {
            "summary": self.get_summary(),
            "metrics": self.metrics[-100:],  # 最近100条记录
            "generated_at": datetime.now().isoformat()
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 性能报告已保存: {filename}")

# 全局监控实例
performance_monitor = PerformanceMonitor(interval=60)
