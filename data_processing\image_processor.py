"""
图像处理模块

负责图像预处理、特征提取、格式转换等功能
"""

import os
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import cv2
import numpy as np
from PIL import Image, ImageEnhance
from loguru import logger


class ImageProcessor:
    """图像处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.max_size = config.get("max_size", [1920, 1080])
        self.quality = config.get("quality", 85)
        self.supported_formats = config.get("supported_formats", {}).get("image", [])
        
    def process_image(self, image_path: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        处理单张图像
        
        Args:
            image_path: 输入图像路径
            output_path: 输出图像路径（可选）
            
        Returns:
            处理结果信息
        """
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                raise FileNotFoundError(f"图像文件不存在: {image_path}")
            
            # 读取图像
            image = Image.open(image_path)
            original_size = image.size
            
            # 图像预处理
            processed_image = self._preprocess_image(image)
            
            # 提取图像特征
            features = self._extract_features(processed_image)
            
            # 保存处理后的图像
            if output_path:
                output_path = Path(output_path)
                output_path.parent.mkdir(parents=True, exist_ok=True)
                processed_image.save(output_path, quality=self.quality, optimize=True)
            
            result = {
                "source": str(image_path),
                "original_size": original_size,
                "processed_size": processed_image.size,
                "features": features,
                "output_path": str(output_path) if output_path else None,
                "metadata": {
                    "file_name": image_path.name,
                    "file_type": image_path.suffix.lower(),
                    "file_size": image_path.stat().st_size,
                    "format": image.format
                }
            }
            
            logger.info(f"成功处理图像 {image_path.name}")
            return result
            
        except Exception as e:
            logger.error(f"处理图像 {image_path} 时出错: {str(e)}")
            return {}

    def _read_image_with_chinese_path(self, image_path: str, flags: int = cv2.IMREAD_COLOR) -> Optional[np.ndarray]:
        """
        支持中文路径的图像读取函数

        Args:
            image_path: 图像路径
            flags: 读取标志

        Returns:
            图像数组或None
        """
        try:
            # 方法1：使用numpy和cv2.imdecode处理中文路径
            with open(image_path, 'rb') as f:
                image_data = f.read()

            # 将字节数据转换为numpy数组
            nparr = np.frombuffer(image_data, np.uint8)

            # 解码图像
            image = cv2.imdecode(nparr, flags)

            if image is not None:
                return image

            # 方法2：使用PIL作为备选方案
            pil_image = Image.open(image_path)

            if flags == cv2.IMREAD_GRAYSCALE:
                pil_image = pil_image.convert('L')
                image = np.array(pil_image)
            else:
                pil_image = pil_image.convert('RGB')
                image = np.array(pil_image)
                # PIL使用RGB，OpenCV使用BGR
                image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)

            return image

        except Exception as e:
            logger.error(f"读取图像失败 {image_path}: {str(e)}")
            return None
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """图像预处理"""
        try:
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整图像大小
            if image.size[0] > self.max_size[0] or image.size[1] > self.max_size[1]:
                image.thumbnail(self.max_size, Image.Resampling.LANCZOS)
            
            # 图像增强
            image = self._enhance_image(image)
            
            return image
            
        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            return image
    
    def _enhance_image(self, image: Image.Image) -> Image.Image:
        """图像增强"""
        try:
            # 亮度增强
            brightness_enhancer = ImageEnhance.Brightness(image)
            image = brightness_enhancer.enhance(1.1)
            
            # 对比度增强
            contrast_enhancer = ImageEnhance.Contrast(image)
            image = contrast_enhancer.enhance(1.1)
            
            # 锐度增强
            sharpness_enhancer = ImageEnhance.Sharpness(image)
            image = sharpness_enhancer.enhance(1.1)
            
            return image
            
        except Exception as e:
            logger.error(f"图像增强失败: {str(e)}")
            return image
    
    def _extract_features(self, image: Image.Image) -> Dict[str, Any]:
        """提取图像特征 - 优化版本"""
        try:
            # 转换为OpenCV格式
            cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

            # 基础特征
            height, width = cv_image.shape[:2]
            original_area = width * height

            # 如果图像太大，进行下采样以提高处理速度
            if height > 300 or width > 300:
                scale_factor = min(300/height, 300/width)
                new_height = int(height * scale_factor)
                new_width = int(width * scale_factor)
                cv_image = cv2.resize(cv_image, (new_width, new_height))

            # 快速特征提取
            features = {
                "basic": {
                    "width": width,
                    "height": height,
                    "aspect_ratio": width / height,
                    "area": original_area
                }
            }

            # 只在图像不太大时提取详细特征
            if original_area < 90000:  # 300x300
                # 颜色特征
                features["color"] = self._extract_color_features(cv_image)

                # 简化的纹理特征
                features["texture"] = self._extract_simple_texture_features(cv_image)

                # 边缘特征
                features["edge"] = self._extract_edge_features(cv_image)
            else:
                # 大图像只提取基本特征
                gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
                features["color"] = {"mean_brightness": float(np.mean(gray))}
                features["texture"] = {"std_dev": float(np.std(gray))}
                features["edge"] = {"edge_density": 0.0}

            return features

        except Exception as e:
            logger.error(f"特征提取失败: {str(e)}")
            return {}
    
    def _extract_color_features(self, image: np.ndarray) -> Dict[str, Any]:
        """提取颜色特征"""
        try:
            # 计算颜色直方图
            hist_b = cv2.calcHist([image], [0], None, [256], [0, 256])
            hist_g = cv2.calcHist([image], [1], None, [256], [0, 256])
            hist_r = cv2.calcHist([image], [2], None, [256], [0, 256])
            
            # 计算颜色统计信息
            mean_color = np.mean(image, axis=(0, 1))
            std_color = np.std(image, axis=(0, 1))
            
            return {
                "mean_bgr": mean_color.tolist(),
                "std_bgr": std_color.tolist(),
                "hist_b": hist_b.flatten().tolist()[:50],  # 只保存前50个值
                "hist_g": hist_g.flatten().tolist()[:50],
                "hist_r": hist_r.flatten().tolist()[:50]
            }
            
        except Exception as e:
            logger.error(f"颜色特征提取失败: {str(e)}")
            return {}
    
    def _extract_texture_features(self, image: np.ndarray) -> Dict[str, Any]:
        """提取纹理特征"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算LBP特征
            lbp = self._calculate_lbp(gray)
            
            # 计算纹理统计信息
            texture_stats = {
                "mean": float(np.mean(lbp)),
                "std": float(np.std(lbp)),
                "entropy": self._calculate_entropy(lbp)
            }
            
            return texture_stats
            
        except Exception as e:
            logger.error(f"纹理特征提取失败: {str(e)}")
            return {}

    def _extract_simple_texture_features(self, image: np.ndarray) -> Dict[str, Any]:
        """提取简化的纹理特征 - 快速版本"""
        try:
            # 转换为灰度图
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image

            # 基本统计特征
            mean_val = np.mean(gray)
            std_val = np.std(gray)

            # 简单的梯度特征替代LBP
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            gradient_mean = np.mean(gradient_magnitude)

            return {
                "mean": float(mean_val),
                "std": float(std_val),
                "gradient_mean": float(gradient_mean)
            }

        except Exception as e:
            logger.error(f"简化纹理特征提取失败: {str(e)}")
            return {}

    def _extract_edge_features(self, image: np.ndarray) -> Dict[str, Any]:
        """提取边缘特征"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Canny边缘检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 计算边缘统计信息
            edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
            
            return {
                "edge_density": float(edge_density),
                "total_edges": int(np.sum(edges > 0))
            }
            
        except Exception as e:
            logger.error(f"边缘特征提取失败: {str(e)}")
            return {}
    
    def _calculate_lbp(self, image: np.ndarray, radius: int = 1, n_points: int = 8) -> np.ndarray:
        """计算局部二值模式(LBP) - 优化版本"""
        try:
            height, width = image.shape

            # 如果图像太大，进行下采样以提高处理速度
            if height > 200 or width > 200:
                scale_factor = min(200/height, 200/width)
                new_height = int(height * scale_factor)
                new_width = int(width * scale_factor)
                image = cv2.resize(image, (new_width, new_height))
                height, width = new_height, new_width

            lbp = np.zeros((height, width), dtype=np.uint8)

            # 预计算角度和偏移量
            angles = [2 * np.pi * k / n_points for k in range(n_points)]
            offsets = [(int(radius * np.cos(angle)), int(radius * np.sin(angle))) for angle in angles]

            # 添加边界检查，避免越界
            for i in range(radius, height - radius):
                for j in range(radius, width - radius):
                    center = image[i, j]
                    code = 0

                    for k, (dx, dy) in enumerate(offsets):
                        x, y = i + dx, j + dy

                        # 确保坐标在有效范围内
                        if 0 <= x < height and 0 <= y < width:
                            if image[x, y] >= center:
                                code |= (1 << k)

                    lbp[i, j] = code

            return lbp

        except Exception as e:
            logger.error(f"LBP计算失败: {str(e)}")
            # 返回简化的纹理特征作为fallback
            try:
                # 使用简单的梯度特征替代LBP
                grad_x = cv2.Sobel(image, cv2.CV_64F, 1, 0, ksize=3)
                grad_y = cv2.Sobel(image, cv2.CV_64F, 0, 1, ksize=3)
                magnitude = np.sqrt(grad_x**2 + grad_y**2)
                return magnitude.astype(np.uint8)
            except:
                return np.zeros_like(image)
    
    def _calculate_entropy(self, image: np.ndarray) -> float:
        """计算图像熵"""
        try:
            hist, _ = np.histogram(image, bins=256, range=(0, 256))
            hist = hist / np.sum(hist)  # 归一化
            hist = hist[hist > 0]  # 移除零值
            entropy = -np.sum(hist * np.log2(hist))
            return float(entropy)
            
        except Exception as e:
            logger.error(f"熵计算失败: {str(e)}")
            return 0.0
    
    def batch_process(self, input_dir: str, output_dir: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        批量处理图像
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录（可选）
            
        Returns:
            处理结果列表
        """
        input_dir = Path(input_dir)
        if not input_dir.exists():
            logger.error(f"输入目录不存在: {input_dir}")
            return []
        
        results = []
        
        # 遍历目录下的所有支持的图像文件
        for image_path in input_dir.rglob("*"):
            if image_path.is_file() and image_path.suffix.lower() in self.supported_formats:
                output_path = None
                if output_dir:
                    output_path = Path(output_dir) / image_path.relative_to(input_dir)
                
                result = self.process_image(str(image_path), str(output_path) if output_path else None)
                if result:
                    results.append(result)
        
        logger.info(f"批量处理完成，共处理 {len(results)} 张图像")
        return results

    def detect_defects(self, image_path: str) -> Dict[str, Any]:
        """
        检测图像中的设备缺陷

        Args:
            image_path: 图像路径

        Returns:
            缺陷检测结果
        """
        try:
            image = self._read_image_with_chinese_path(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")

            # 转换为灰度图
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # 检测锈蚀
            rust_areas = self._detect_rust(image)

            # 检测裂纹
            crack_areas = self._detect_cracks(gray)

            # 检测泄漏
            leak_areas = self._detect_leaks(image)

            defects = {
                "rust": rust_areas,
                "cracks": crack_areas,
                "leaks": leak_areas,
                "total_defects": len(rust_areas) + len(crack_areas) + len(leak_areas)
            }

            return defects

        except Exception as e:
            logger.error(f"缺陷检测失败: {str(e)}")
            return {}

    def _detect_rust(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """检测锈蚀区域"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # 定义锈蚀颜色范围（橙红色）
            lower_rust = np.array([5, 50, 50])
            upper_rust = np.array([25, 255, 255])

            # 创建掩码
            mask = cv2.inRange(hsv, lower_rust, upper_rust)

            # 形态学操作
            kernel = np.ones((5, 5), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            rust_areas = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 100:  # 过滤小区域
                    x, y, w, h = cv2.boundingRect(contour)
                    rust_areas.append({
                        "type": "rust",
                        "bbox": [x, y, w, h],
                        "area": area,
                        "confidence": 0.8
                    })

            return rust_areas

        except Exception as e:
            logger.error(f"锈蚀检测失败: {str(e)}")
            return []

    def _detect_cracks(self, gray_image: np.ndarray) -> List[Dict[str, Any]]:
        """检测裂纹"""
        try:
            # 高斯模糊
            blurred = cv2.GaussianBlur(gray_image, (5, 5), 0)

            # Canny边缘检测
            edges = cv2.Canny(blurred, 50, 150)

            # 形态学操作连接断开的边缘
            kernel = np.ones((3, 3), np.uint8)
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            crack_areas = []
            for contour in contours:
                # 计算轮廓的长宽比
                x, y, w, h = cv2.boundingRect(contour)
                aspect_ratio = max(w, h) / min(w, h)

                # 裂纹通常是细长的
                if aspect_ratio > 3 and cv2.contourArea(contour) > 50:
                    crack_areas.append({
                        "type": "crack",
                        "bbox": [x, y, w, h],
                        "length": max(w, h),
                        "confidence": 0.7
                    })

            return crack_areas

        except Exception as e:
            logger.error(f"裂纹检测失败: {str(e)}")
            return []

    def _detect_leaks(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """检测泄漏（基于颜色和纹理特征）"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

            # 检测油污（暗色区域）
            lower_oil = np.array([0, 0, 0])
            upper_oil = np.array([180, 255, 80])

            mask = cv2.inRange(hsv, lower_oil, upper_oil)

            # 形态学操作
            kernel = np.ones((7, 7), np.uint8)
            mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)

            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            leak_areas = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 200:  # 过滤小区域
                    x, y, w, h = cv2.boundingRect(contour)
                    leak_areas.append({
                        "type": "leak",
                        "bbox": [x, y, w, h],
                        "area": area,
                        "confidence": 0.6
                    })

            return leak_areas

        except Exception as e:
            logger.error(f"泄漏检测失败: {str(e)}")
            return []
