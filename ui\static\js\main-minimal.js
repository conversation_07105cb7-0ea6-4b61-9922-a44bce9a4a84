// 最小化版本的main.js - 用于测试基本功能
console.log('🔧 加载最小化版本的main.js...');

// 全局变量
let currentTab = 'fault-analysis';
let analysisHistory = [];
let equipmentList = [];
const API_BASE_URL = '/api/v1';

// 检查是否已经存在bootstrap对象，避免重复定义
if (typeof window.bootstrap === 'undefined') {
    window.bootstrap = {};
}

// 安全的Modal类定义
if (typeof window.bootstrap.Modal === 'undefined') {
    window.bootstrap.Modal = class BootstrapModal {
        constructor(element) {
            this.element = typeof element === 'string' ? document.getElementById(element) : element;
        }

        show() {
            if (this.element) {
                this.element.style.display = 'block';
                this.element.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        }

        hide() {
            if (this.element) {
                this.element.style.display = 'none';
                this.element.classList.remove('show');
                document.body.style.overflow = '';
            }
        }

        static getInstance(element) {
            return new window.bootstrap.Modal(element);
        }
    };
}

// 安全的Tab类定义
if (typeof window.bootstrap.Tab === 'undefined') {
    window.bootstrap.Tab = class BootstrapTab {
        constructor(element) {
            this.element = typeof element === 'string' ? document.querySelector(element) : element;
        }

        show() {
            if (!this.element) return;

            const target = this.element.getAttribute('href') || this.element.getAttribute('data-bs-target');
            if (!target) return;

            // 隐藏所有tab内容
            const tabContent = document.querySelectorAll('.tab-content');
            tabContent.forEach(content => {
                content.classList.remove('active');
                content.style.display = 'none';
            });

            // 移除所有tab的active状态
            const allTabs = document.querySelectorAll('.nav-link');
            allTabs.forEach(tab => tab.classList.remove('active'));

            // 激活当前tab
            this.element.classList.add('active');

            // 显示目标内容
            const targetContent = document.querySelector(target);
            if (targetContent) {
                targetContent.classList.add('active');
                targetContent.style.display = 'block';
            }
        }
    };
}

// 安全的Alert类定义
if (typeof window.bootstrap.Alert === 'undefined') {
    window.bootstrap.Alert = class BootstrapAlert {
        constructor(element) {
            this.element = typeof element === 'string' ? document.getElementById(element) : element;
        }

        close() {
            if (this.element) {
                this.element.style.opacity = '0';
                setTimeout(() => {
                    if (this.element && this.element.parentNode) {
                        this.element.parentNode.removeChild(this.element);
                    }
                }, 300);
            }
        }

        static getInstance(element) {
            return new window.bootstrap.Alert(element);
        }
    };
}

// 安全的Toast类定义
if (typeof window.bootstrap.Toast === 'undefined') {
    window.bootstrap.Toast = class BootstrapToast {
        constructor(element, options = {}) {
            this.element = typeof element === 'string' ? document.getElementById(element) : element;
            this.options = {
                autohide: options.autohide !== false,
                delay: options.delay || 5000
            };
        }

        show() {
            if (this.element) {
                this.element.style.display = 'block';
                this.element.classList.add('show');
                
                if (this.options.autohide) {
                    setTimeout(() => {
                        this.hide();
                    }, this.options.delay);
                }
            }
        }

        hide() {
            if (this.element) {
                this.element.style.opacity = '0';
                setTimeout(() => {
                    this.element.style.display = 'none';
                    this.element.classList.remove('show');
                    if (this.element.parentNode) {
                        this.element.parentNode.removeChild(this.element);
                    }
                }, 300);
            }
        }

        static getInstance(element) {
            return new window.bootstrap.Toast(element);
        }
    };
}

// 基本的工具函数
function safeGetElement(selector, byId = false) {
    try {
        return byId ? document.getElementById(selector) : document.querySelector(selector);
    } catch (error) {
        console.warn(`获取元素失败: ${selector}`, error);
        return null;
    }
}

function safeGetElements(selector) {
    try {
        return document.querySelectorAll(selector);
    } catch (error) {
        console.warn(`获取元素列表失败: ${selector}`, error);
        return [];
    }
}

function safeClassListOperation(element, operation, className) {
    if (element && element.classList) {
        try {
            element.classList[operation](className);
        } catch (error) {
            console.warn(`类操作失败: ${operation} ${className}`, error);
        }
    }
}

// 显示警告信息
function showAlert(message, type = 'info') {
    console.log(`Alert [${type}]: ${message}`);
    
    // 创建简单的警告框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.maxWidth = '400px';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// 切换标签页
function switchTab(tabName) {
    console.log(`切换到标签页: ${tabName}`);
    
    // 更新导航栏状态
    const navLinks = safeGetElements('.nav-link');
    navLinks.forEach(link => {
        safeClassListOperation(link, 'remove', 'active');
    });

    const navLink = safeGetElement(`[data-tab="${tabName}"]`);
    if (navLink) {
        safeClassListOperation(navLink, 'add', 'active');
    }

    // 隐藏所有内容区域
    const contentAreas = safeGetElements('.main-tab-content');
    contentAreas.forEach(area => {
        safeClassListOperation(area, 'remove', 'active');
    });

    const contentArea = safeGetElement(tabName, true);
    if (contentArea) {
        safeClassListOperation(contentArea, 'add', 'active');
    }

    currentTab = tabName;
}

// 初始化应用
function initializeApp() {
    console.log('🚀 初始化故障分析智能助手（最小化版本）...');
    
    try {
        // 基本的事件绑定
        const navLinks = document.querySelectorAll('.nav-link[data-tab]');
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const tabName = this.getAttribute('data-tab');
                switchTab(tabName);
            });
        });
        
        console.log('✅ 基本功能初始化完成');
        showAlert('系统初始化完成', 'success');
        
    } catch (error) {
        console.error('❌ 初始化失败:', error);
        showAlert('系统初始化失败: ' + error.message, 'danger');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM加载完成，开始初始化...');
    
    // 延迟初始化，确保所有资源加载完成
    setTimeout(() => {
        initializeApp();
    }, 100);
});

// 导出到全局
window.initializeApp = initializeApp;
window.switchTab = switchTab;
window.showAlert = showAlert;

console.log('✅ 最小化main.js加载完成');
