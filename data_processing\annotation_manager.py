"""
数据标注管理器
负责文档和图片的标注、清洗、转换等功能
"""

import os
import json
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from loguru import logger


class AnnotationManager:
    """数据标注管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.annotation_dir = config.get("annotation_dir", "data/annotations")
        self.output_dir = config.get("output_dir", "data/processed")
        
        # 创建必要的目录
        os.makedirs(self.annotation_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 标注类型配置
        self.annotation_types = {
            "defect": {
                "color": (255, 0, 0),  # 红色
                "description": "设备缺陷"
            },
            "equipment": {
                "color": (0, 255, 0),  # 绿色
                "description": "设备识别"
            },
            "text": {
                "color": (0, 0, 255),  # 蓝色
                "description": "文字区域"
            },
            "measurement": {
                "color": (255, 255, 0),  # 黄色
                "description": "测量点"
            }
        }
        
    def create_document_annotation(self, 
                                 file_path: str, 
                                 metadata: Dict[str, Any]) -> str:
        """
        创建文档标注
        
        Args:
            file_path: 文档文件路径
            metadata: 标注元数据
            
        Returns:
            标注文件ID
        """
        try:
            annotation_id = str(uuid.uuid4())
            
            # 构建标注数据
            annotation_data = {
                "annotation_id": annotation_id,
                "source_file": file_path,
                "annotation_type": "document",
                "created_time": datetime.now().isoformat(),
                "metadata": metadata,
                "content_analysis": self._analyze_document_content(file_path),
                "tags": metadata.get("tags", []),
                "categories": metadata.get("categories", []),
                "quality_score": self._calculate_quality_score(metadata)
            }
            
            # 保存标注文件
            annotation_file = os.path.join(
                self.annotation_dir, 
                f"doc_{annotation_id}.json"
            )
            
            with open(annotation_file, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"文档标注已创建: {annotation_id}")
            return annotation_id
            
        except Exception as e:
            logger.error(f"创建文档标注失败: {str(e)}")
            raise
    
    def create_image_annotation(self, 
                              image_path: str, 
                              annotations: List[Dict[str, Any]], 
                              metadata: Dict[str, Any]) -> str:
        """
        创建图片标注
        
        Args:
            image_path: 图片文件路径
            annotations: 标注列表
            metadata: 元数据
            
        Returns:
            标注文件ID
        """
        try:
            annotation_id = str(uuid.uuid4())
            
            # 读取图片信息
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")
            
            height, width = image.shape[:2]
            
            # 验证和标准化标注数据
            validated_annotations = []
            for ann in annotations:
                validated_ann = self._validate_annotation(ann, width, height)
                if validated_ann:
                    validated_annotations.append(validated_ann)
            
            # 构建标注数据
            annotation_data = {
                "annotation_id": annotation_id,
                "source_file": image_path,
                "annotation_type": "image",
                "created_time": datetime.now().isoformat(),
                "image_info": {
                    "width": width,
                    "height": height,
                    "channels": image.shape[2] if len(image.shape) > 2 else 1,
                    "format": os.path.splitext(image_path)[1].lower()
                },
                "annotations": validated_annotations,
                "metadata": metadata,
                "quality_score": self._calculate_annotation_quality(validated_annotations)
            }
            
            # 保存标注文件
            annotation_file = os.path.join(
                self.annotation_dir, 
                f"img_{annotation_id}.json"
            )
            
            with open(annotation_file, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)
            
            # 生成标注可视化图片
            annotated_image_path = self._create_annotated_image(
                image_path, validated_annotations, annotation_id
            )
            
            annotation_data["annotated_image_path"] = annotated_image_path
            
            # 更新标注文件
            with open(annotation_file, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"图片标注已创建: {annotation_id}")
            return annotation_id
            
        except Exception as e:
            logger.error(f"创建图片标注失败: {str(e)}")
            raise
    
    def _validate_annotation(self, annotation: Dict[str, Any], 
                           width: int, height: int) -> Optional[Dict[str, Any]]:
        """验证标注数据"""
        try:
            # 检查必要字段
            if not all(key in annotation for key in ['startX', 'startY', 'endX', 'endY', 'type']):
                return None
            
            # 标准化坐标
            x1 = max(0, min(annotation['startX'], width))
            y1 = max(0, min(annotation['startY'], height))
            x2 = max(0, min(annotation['endX'], width))
            y2 = max(0, min(annotation['endY'], height))
            
            # 确保有效的矩形
            if abs(x2 - x1) < 5 or abs(y2 - y1) < 5:
                return None
            
            return {
                "type": annotation['type'],
                "bbox": [min(x1, x2), min(y1, y2), max(x1, x2), max(y1, y2)],
                "color": annotation.get('color', '#ff0000'),
                "confidence": annotation.get('confidence', 1.0),
                "description": annotation.get('description', ''),
                "area": abs(x2 - x1) * abs(y2 - y1)
            }
            
        except Exception as e:
            logger.warning(f"标注验证失败: {str(e)}")
            return None
    
    def _create_annotated_image(self, image_path: str, 
                              annotations: List[Dict[str, Any]], 
                              annotation_id: str) -> str:
        """创建标注可视化图片"""
        try:
            # 读取原图
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图片: {image_path}")
            
            # 绘制标注
            for ann in annotations:
                bbox = ann['bbox']
                ann_type = ann['type']
                
                # 获取颜色
                color = self.annotation_types.get(ann_type, {}).get('color', (255, 0, 0))
                
                # 绘制矩形
                cv2.rectangle(image, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, 2)
                
                # 绘制标签
                label = f"{ann_type}"
                if ann.get('confidence'):
                    label += f" ({ann['confidence']:.2f})"
                
                # 计算文字位置
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                label_y = bbox[1] - 10 if bbox[1] - 10 > 10 else bbox[1] + 20
                
                # 绘制文字背景
                cv2.rectangle(image, 
                            (bbox[0], label_y - label_size[1] - 5),
                            (bbox[0] + label_size[0] + 5, label_y + 5),
                            color, -1)
                
                # 绘制文字
                cv2.putText(image, label, (bbox[0] + 2, label_y), 
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # 保存标注图片
            output_path = os.path.join(
                self.output_dir, 
                f"annotated_{annotation_id}.jpg"
            )
            cv2.imwrite(output_path, image)
            
            return output_path
            
        except Exception as e:
            logger.error(f"创建标注图片失败: {str(e)}")
            return ""
    
    def _analyze_document_content(self, file_path: str) -> Dict[str, Any]:
        """分析文档内容"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            analysis = {
                "file_type": file_ext,
                "file_size": os.path.getsize(file_path),
                "word_count": 0,
                "language": "zh-cn",
                "encoding": "utf-8"
            }
            
            # 根据文件类型进行分析
            if file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    analysis["word_count"] = len(content)
                    analysis["line_count"] = len(content.split('\n'))
                    
            elif file_ext == '.docx':
                try:
                    import docx
                    doc = docx.Document(file_path)
                    text = '\n'.join([p.text for p in doc.paragraphs])
                    analysis["word_count"] = len(text)
                    analysis["paragraph_count"] = len(doc.paragraphs)
                    analysis["table_count"] = len(doc.tables)
                except ImportError:
                    logger.warning("python-docx未安装，跳过DOCX分析")
            
            return analysis
            
        except Exception as e:
            logger.error(f"文档内容分析失败: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_quality_score(self, metadata: Dict[str, Any]) -> float:
        """计算标注质量评分"""
        try:
            score = 0.0
            
            # 基础信息完整性 (40%)
            if metadata.get('title'):
                score += 10
            if metadata.get('description'):
                score += 10
            if metadata.get('tags'):
                score += 10
            if metadata.get('type'):
                score += 10
            
            # 专业信息完整性 (30%)
            if metadata.get('equipment_type'):
                score += 15
            if metadata.get('voltage_level'):
                score += 15
            
            # 分类信息 (20%)
            if metadata.get('fault_types'):
                score += 20
            
            # 自动化程度 (10%)
            if metadata.get('auto_extract'):
                score += 5
            if metadata.get('auto_classify'):
                score += 5
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"质量评分计算失败: {str(e)}")
            return 0.0
    
    def _calculate_annotation_quality(self, annotations: List[Dict[str, Any]]) -> float:
        """计算图片标注质量评分"""
        try:
            if not annotations:
                return 0.0
            
            score = 0.0
            
            # 标注数量 (30%)
            annotation_count = len(annotations)
            if annotation_count >= 1:
                score += 10
            if annotation_count >= 3:
                score += 10
            if annotation_count >= 5:
                score += 10
            
            # 标注类型多样性 (30%)
            types = set(ann['type'] for ann in annotations)
            score += min(30, len(types) * 10)
            
            # 标注精度 (40%)
            avg_confidence = sum(ann.get('confidence', 1.0) for ann in annotations) / len(annotations)
            score += avg_confidence * 40
            
            return min(100.0, score)
            
        except Exception as e:
            logger.error(f"标注质量评分计算失败: {str(e)}")
            return 0.0
    
    def get_annotation_statistics(self) -> Dict[str, Any]:
        """获取标注统计信息"""
        try:
            stats = {
                "total_annotations": 0,
                "document_annotations": 0,
                "image_annotations": 0,
                "annotation_types": {},
                "quality_distribution": {"high": 0, "medium": 0, "low": 0}
            }
            
            # 扫描标注目录
            if os.path.exists(self.annotation_dir):
                for file in os.listdir(self.annotation_dir):
                    if file.endswith('.json'):
                        try:
                            with open(os.path.join(self.annotation_dir, file), 'r', encoding='utf-8') as f:
                                data = json.load(f)
                                
                                stats["total_annotations"] += 1
                                
                                if data.get("annotation_type") == "document":
                                    stats["document_annotations"] += 1
                                elif data.get("annotation_type") == "image":
                                    stats["image_annotations"] += 1
                                
                                # 质量分布
                                quality = data.get("quality_score", 0)
                                if quality >= 80:
                                    stats["quality_distribution"]["high"] += 1
                                elif quality >= 60:
                                    stats["quality_distribution"]["medium"] += 1
                                else:
                                    stats["quality_distribution"]["low"] += 1
                                    
                        except Exception as e:
                            logger.warning(f"读取标注文件失败 {file}: {str(e)}")
            
            return stats
            
        except Exception as e:
            logger.error(f"获取标注统计失败: {str(e)}")
            return {}
