#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强知识库模块 - 集成优化RAG检索系统
支持720+文档的高级检索和智能问答
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from data_processing.chroma_manager import ChromaManager
    from data_processing.advanced_retrieval_optimizer import AdvancedRetrievalOptimizer
    OPTIMIZED_RAG_AVAILABLE = True
except ImportError as e:
    OPTIMIZED_RAG_AVAILABLE = False
    logging.error(f"优化RAG模块不可用: {e}")


class EnhancedKnowledgeBase:
    """增强知识库 - 集成优化RAG检索"""

    def __init__(self):
        self.base_path = Path(__file__).parent.parent

        # 配置优化的Chroma管理器
        self.chroma_config = {
            "persist_directory": str(self.base_path / "embeddings/final_optimized_chroma_store"),
            "collection_name": "baiyin_power_final_collection",
            "embedding_model": "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"
        }

        self.chroma_manager = None
        self.retrieval_optimizer = None
        self.is_available = False

        if OPTIMIZED_RAG_AVAILABLE:
            try:
                self.chroma_manager = ChromaManager(self.chroma_config)
                self.retrieval_optimizer = AdvancedRetrievalOptimizer(self.chroma_manager)
                self.is_available = True
                logging.info("✅ 增强知识库初始化成功")
            except Exception as e:
                logging.error(f"❌ 增强知识库初始化失败: {e}")

    def search(self, query: str, search_type: str = "advanced", limit: int = 10) -> Dict[str, Any]:
        """增强搜索功能"""
        try:
            if not self.is_available:
                return {"success": False, "error": "增强知识库不可用"}

            logging.info(f"🔍 增强知识库搜索: {query[:50]}...")
            print(f"🔍 增强知识库搜索: {query}, 类型: {search_type}, 限制: {limit}")
            print(f"📊 检索优化器可用: {self.retrieval_optimizer is not None}")
            print(f"📊 Chroma管理器可用: {self.chroma_manager is not None}")

            if search_type == "advanced" and self.retrieval_optimizer:
                print("🚀 使用高级检索优化器")
                # 使用高级检索优化器
                results = self.retrieval_optimizer.get_optimized_results(query, n_results=limit)

                # 转换为标准格式
                formatted_results = []
                for i, result in enumerate(results.get("results", [])):
                    # 生成唯一ID
                    content = result.get("content", "")
                    metadata = result.get("metadata", {})

                    # 基于内容和元数据生成ID
                    import hashlib
                    content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
                    doc_id = f"enhanced_{content_hash}_{i}"

                    # 如果元数据中有特定信息，使用更有意义的ID
                    if metadata.get("equipment_type") and metadata.get("fault_type"):
                        doc_id = f"fault_{metadata.get('equipment_type')}_{metadata.get('fault_type')}_{content_hash}"
                    elif metadata.get("data_type"):
                        doc_id = f"{metadata.get('data_type')}_{content_hash}"

                    formatted_result = {
                        "id": doc_id,
                        "title": result.get("title", content[:100]),
                        "content": content,
                        "score": result.get("final_score", result.get("score", 0)),
                        "metadata": metadata,
                        "source": result.get("source", "优化知识库"),
                        "type": "advanced_rag"
                    }
                    print(f"🔧 生成增强搜索结果ID: {doc_id}")
                    formatted_results.append(formatted_result)

                return {
                    "success": True,
                    "results": formatted_results,
                    "total": len(formatted_results),
                    "query": query,
                    "search_type": "advanced",
                    "intent": results.get("intent", {}),
                    "analysis": results.get("analysis", {}),
                    "response_time": results.get("response_time", 0)
                }

            elif self.chroma_manager:
                print("🔧 使用基础向量检索")
                # 使用基础向量检索
                results = self.chroma_manager.search(query, n_results=limit)

                formatted_results = []
                for i, result in enumerate(results):
                    # 生成唯一ID
                    content = result.get("content", "")
                    metadata = result.get("metadata", {})

                    # 基于内容生成ID
                    import hashlib
                    content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
                    doc_id = f"vector_{content_hash}_{i}"

                    # 如果元数据中有特定信息，使用更有意义的ID
                    if metadata.get("equipment_type") and metadata.get("fault_type"):
                        doc_id = f"vector_{metadata.get('equipment_type')}_{metadata.get('fault_type')}_{content_hash}"
                    elif metadata.get("data_type"):
                        doc_id = f"vector_{metadata.get('data_type')}_{content_hash}"

                    formatted_result = {
                        "id": doc_id,
                        "title": content[:100],
                        "content": content,
                        "score": result.get("score", 0),
                        "metadata": metadata,
                        "source": "向量数据库",
                        "type": "vector_search"
                    }
                    formatted_results.append(formatted_result)

                return {
                    "success": True,
                    "results": formatted_results,
                    "total": len(formatted_results),
                    "query": query,
                    "search_type": "basic"
                }

            else:
                return {"success": False, "error": "没有可用的搜索引擎"}

        except Exception as e:
            logging.error(f"增强知识库搜索失败: {e}")
            return {"success": False, "error": str(e)}

    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """获取文档详情"""
        try:
            if not self.is_available or not self.chroma_manager:
                return None

            # 尝试从向量数据库获取文档
            collection = self.chroma_manager.collection
            if collection:
                results = collection.get(ids=[doc_id])
                if results and results.get("documents"):
                    return {
                        "id": doc_id,
                        "content": results["documents"][0],
                        "metadata": results.get("metadatas", [{}])[0],
                        "source": "优化知识库"
                    }

            return None

        except Exception as e:
            logging.error(f"获取文档失败: {e}")
            return None

    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        try:
            if not self.is_available or not self.chroma_manager:
                return {"total_documents": 0, "status": "不可用"}

            stats = self.chroma_manager.get_collection_stats()
            return {
                "total_documents": stats.get("total_documents", 0),
                "status": "可用",
                "database_type": "优化向量数据库",
                "features": ["高级检索", "意图识别", "查询扩展", "重排序"]
            }

        except Exception as e:
            logging.error(f"获取统计信息失败: {e}")
            return {"total_documents": 0, "status": "错误", "error": str(e)}

    def add_document(self, file_path: str, metadata: Dict[str, Any] = None) -> bool:
        """
        添加文档到增强知识库

        Args:
            file_path: 文档文件路径
            metadata: 文档元数据

        Returns:
            bool: 是否添加成功
        """
        try:
            if not self.is_available:
                logging.warning("增强知识库不可用，无法添加文档")
                return False

            if not self.chroma_manager:
                logging.error("Chroma管理器不可用")
                return False

            logging.info(f"添加文档到增强知识库: {file_path}")

            # 准备文档数据
            if metadata is None:
                metadata = {}

            # 添加文件信息到元数据
            import os
            import time
            metadata.update({
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_type': 'document',
                'added_time': str(time.time())
            })

            # 读取文档内容
            content = ""
            if os.path.exists(file_path):
                try:
                    # 根据文件类型读取内容
                    if file_path.lower().endswith('.txt'):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                    elif file_path.lower().endswith('.docx'):
                        # 使用docx库读取
                        try:
                            from docx import Document
                            doc = Document(file_path)
                            content = '\n'.join([paragraph.text for paragraph in doc.paragraphs])
                        except ImportError:
                            logging.warning("docx库不可用，跳过DOCX文件处理")
                            content = f"DOCX文档: {os.path.basename(file_path)}"
                    else:
                        content = f"文档: {os.path.basename(file_path)}"

                except Exception as e:
                    logging.error(f"读取文档内容失败: {e}")
                    content = f"文档: {os.path.basename(file_path)}"
            else:
                content = f"文档: {os.path.basename(file_path)}"

            # 生成文档ID
            import hashlib
            doc_id = hashlib.md5(f"{file_path}_{time.time()}".encode()).hexdigest()

            # 添加到Chroma
            document_data = {
                'content': content,
                'metadata': metadata,
                'id': doc_id
            }
            success = self.chroma_manager.add_documents([document_data])

            if success:
                logging.info(f"文档已成功添加到增强知识库: {file_path}")
                return True
            else:
                logging.error(f"文档添加到增强知识库失败: {file_path}")
                return False

        except Exception as e:
            logging.error(f"添加文档到增强知识库异常: {e}")
            return False

    def add_image(self, file_path: str, metadata: Dict[str, Any] = None) -> bool:
        """
        添加图片到增强知识库

        Args:
            file_path: 图片文件路径
            metadata: 图片元数据

        Returns:
            bool: 是否添加成功
        """
        try:
            if not self.is_available:
                logging.warning("增强知识库不可用，无法添加图片")
                return False

            if not self.chroma_manager:
                logging.error("Chroma管理器不可用")
                return False

            logging.info(f"添加图片到增强知识库: {file_path}")

            # 准备图片数据
            if metadata is None:
                metadata = {}

            # 添加文件信息到元数据
            import os
            import time
            metadata.update({
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'file_type': 'image',
                'added_time': str(time.time())
            })

            # 生成图片描述内容
            title = metadata.get('title', os.path.basename(file_path))
            description = metadata.get('description', '')
            category = metadata.get('category', '未分类')

            content = f"图片: {title}"
            if description:
                content += f"\n描述: {description}"
            if category:
                content += f"\n分类: {category}"

            # 添加设备类型等专业信息
            if metadata.get('equipment_type'):
                content += f"\n设备类型: {metadata['equipment_type']}"
            if metadata.get('voltage_level'):
                content += f"\n电压等级: {metadata['voltage_level']}"
            if metadata.get('fault_type'):
                content += f"\n故障类型: {metadata['fault_type']}"

            # 生成图片ID
            import hashlib
            img_id = hashlib.md5(f"{file_path}_{time.time()}".encode()).hexdigest()

            # 添加到Chroma
            document_data = {
                'content': content,
                'metadata': metadata,
                'id': img_id
            }
            success = self.chroma_manager.add_documents([document_data])

            if success:
                logging.info(f"图片已成功添加到增强知识库: {file_path}")
                return True
            else:
                logging.error(f"图片添加到增强知识库失败: {file_path}")
                return False

        except Exception as e:
            logging.error(f"添加图片到增强知识库异常: {e}")
            return False


# 创建全局实例（延迟初始化）
enhanced_knowledge_base = None

def get_enhanced_knowledge_base():
    """获取增强知识库实例（延迟初始化）"""
    global enhanced_knowledge_base
    if enhanced_knowledge_base is None:
        try:
            enhanced_knowledge_base = EnhancedKnowledgeBase()
            logging.info("✅ 延迟初始化增强知识库成功")
        except Exception as e:
            logging.error(f"❌ 延迟初始化增强知识库失败: {e}")
            # 创建一个不可用的实例
            enhanced_knowledge_base = type('MockKnowledgeBase', (), {
                'is_available': False,
                'search': lambda self, *args, **kwargs: {"success": False, "error": "知识库不可用"}
            })()
    return enhanced_knowledge_base

# 立即创建实例
enhanced_knowledge_base = get_enhanced_knowledge_base()
