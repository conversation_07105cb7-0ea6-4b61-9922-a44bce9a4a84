{"embedding_models": {"primary": {"name": "paraphrase-multilingual-MiniLM-L12-v2", "path": "paraphrase-multilingual-MiniLM-L12-v2", "dimension": 384, "status": "online"}, "fallback": {"type": "tfidf", "max_features": 2000, "ngram_range": [1, 2]}, "available_models": [{"name": "paraphrase-multilingual-MiniLM-L12-v2", "description": "多语言模型，支持中文，体积较小", "dimension": 384, "priority": 1, "path": "paraphrase-multilingual-MiniLM-L12-v2", "status": "online"}, {"name": "distiluse-base-multilingual-cased", "description": "多语言模型，性能较好", "dimension": 512, "priority": 2, "path": "distiluse-base-multilingual-cased", "status": "online"}]}, "vector_processor": {"batch_size": 32, "device": "cpu", "normalize_embeddings": true}}