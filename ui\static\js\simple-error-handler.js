// 简化的错误处理器 - 专注于解决核心问题
(function() {
    'use strict';

    console.log('🔧 加载简化错误处理器...');

    // 全局错误处理 - 只处理关键错误，不过度干预
    window.addEventListener('error', function(event) {
        console.error('JavaScript错误:', event.message);

        // 只处理bootstrap相关错误
        if (event.message && event.message.includes('bootstrap')) {
            console.log('🔧 检测到Bootstrap错误，尝试修复...');
            setTimeout(fixBootstrapObjects, 100);
        }
    });
    
    // Promise错误处理 - 简化版本
    window.addEventListener('unhandledrejection', function(event) {
        console.error('Promise错误:', event.reason);
        // 不阻止错误，让系统自然处理
    });
    
    // 修复Bootstrap对象 - 简化版本
    function fixBootstrapObjects() {
        try {
            console.log('🔧 修复Bootstrap对象...');

            if (typeof window.bootstrap === 'undefined') {
                window.bootstrap = {};
            }

            // 只修复最基本的Modal类
            if (!window.bootstrap.Modal) {
                window.bootstrap.Modal = function(element) {
                    this.element = element;
                    this.show = function() {
                        if (this.element) {
                            this.element.style.display = 'block';
                            this.element.classList.add('show');
                        }
                    };
                    this.hide = function() {
                        if (this.element) {
                            this.element.style.display = 'none';
                            this.element.classList.remove('show');
                        }
                    };
                };
                window.bootstrap.Modal.getInstance = function(element) {
                    return new window.bootstrap.Modal(element);
                };
            }

            // 基本的Tab类
            if (!window.bootstrap.Tab) {
                window.bootstrap.Tab = function(element) {
                    this.element = element;
                    this.show = function() {
                        if (this.element) {
                            this.element.click();
                        }
                    };
                };
            }

            console.log('✅ Bootstrap修复完成');
        } catch (error) {
            console.error('❌ Bootstrap修复失败:', error);
        }
    }
    
    // 导出修复函数到全局
    window.fixBootstrapObjects = fixBootstrapObjects;
    
    // 页面加载完成后立即修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', fixBootstrapObjects);
    } else {
        fixBootstrapObjects();
    }
    
    console.log('✅ 简化错误处理器加载完成');
    
})();
