"""
数据处理模块

提供文本清洗、图像解析、向量化处理等功能
"""

# 延迟导入，避免依赖问题
def _lazy_import():
    """延迟导入模块"""
    global TextProcessor, ImageProcessor, VectorProcessor, OCRProcessor

    try:
        from .text_processor import TextProcessor
    except ImportError as e:
        print(f"Warning: TextProcessor导入失败: {e}")
        TextProcessor = None

    try:
        from .image_processor import ImageProcessor
    except ImportError as e:
        print(f"Warning: ImageProcessor导入失败: {e}")
        ImageProcessor = None

    try:
        from .vector_processor import VectorProcessor
    except ImportError as e:
        print(f"Warning: VectorProcessor导入失败: {e}")
        VectorProcessor = None

    try:
        from .ocr_processor import OCRProcessor
    except ImportError as e:
        print(f"Warning: OCRProcessor导入失败: {e}")
        OCRProcessor = None

# 立即导入OCRProcessor（通常没有依赖问题）
try:
    from .ocr_processor import OCRProcessor
except ImportError as e:
    print(f"Warning: OCRProcessor导入失败: {e}")
    OCRProcessor = None

# 其他模块延迟导入
TextProcessor = None
ImageProcessor = None
VectorProcessor = None

__all__ = [
    "TextProcessor",
    "ImageProcessor",
    "VectorProcessor",
    "OCRProcessor",
    "_lazy_import"
]
