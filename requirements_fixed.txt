# 故障分析智能助手项目依赖 - 修复版本

# 基础科学计算库
numpy==1.24.3
scipy==1.11.4

# 核心框架（兼容版本）
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 大语言模型相关
transformers==4.36.2
torch==2.1.2
sentence-transformers==2.2.2
openai==1.6.1

# <PERSON><PERSON><PERSON><PERSON>生态（兼容版本）
langchain==0.1.0
langchain-community==0.0.10

# 向量数据库
chromadb
faiss-cpu==1.7.4

# 数据处理
pandas==2.1.4
openpyxl==3.1.2
python-docx==1.1.0
PyPDF2==3.0.1
pillow==10.1.0
opencv-python

# OCR和图像处理
pytesseract==0.3.10
easyocr==1.7.0
paddleocr

# Web框架和模板
flask==3.0.0
flask-cors==4.0.0
flask-socketio==5.3.6
jinja2==3.1.2

# 配置管理
pyyaml==6.0.1
python-dotenv==1.0.0

# 日志和监控
loguru==0.7.2

# 工具库
requests==2.31.0
httpx
tqdm==4.66.1
click==8.1.7
rich==13.7.0
psutil==5.9.6

# 中文处理
jieba

# 时间序列和科学计算
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
scikit-learn==1.3.0
scikit-image

# 异步和并发
aiohttp==3.9.1
celery==5.3.4
redis==5.0.1
