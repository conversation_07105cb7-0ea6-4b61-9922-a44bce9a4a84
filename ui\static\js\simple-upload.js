// 简化的上传功能

// 文档上传相关变量
let selectedDocFile = null;
let selectedImageFiles = [];

// 文档文件选择处理 - 简化版本
function handleDocFileSelectSimple(input) {

    const file = input.files[0];
    if (!file) {

        return;
    }

    // 验证文件类型
    const allowedTypes = ['.pdf', '.docx', '.txt', '.md', '.csv', '.xlsx'];
    const fileExt = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExt)) {
        alert('不支持的文件类型。支持的类型：' + allowedTypes.join(', '));
        input.value = '';
        return;
    }

    // 验证文件大小 (20MB)
    if (file.size > 20 * 1024 * 1024) {
        alert('文件大小不能超过20MB');
        input.value = '';
        return;
    }

    selectedDocFile = file;
    showDocFileInfo(file);
}

// 显示文档文件信息
function showDocFileInfo(file) {
    const fileInfo = document.getElementById('doc-file-info');
    const fileName = document.getElementById('doc-file-name');
    const fileSize = document.getElementById('doc-file-size');

    if (fileInfo && fileName && fileSize) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.classList.remove('d-none');
    }
}

// 图片文件选择处理 - 简化版本
function handleImgFileSelectSimple(input) {

    const files = Array.from(input.files);
    if (!files.length) {

        return;
    }

    // 验证文件类型和大小
    const validFiles = [];
    for (const file of files) {
        if (!file.type.startsWith('image/')) {
            alert(`${file.name} 不是有效的图片文件`);
            continue;
        }

        if (file.size > 10 * 1024 * 1024) {
            alert(`${file.name} 文件大小超过10MB`);
            continue;
        }

        validFiles.push(file);
    }

    if (validFiles.length === 0) {
        input.value = '';
        return;
    }

    selectedImageFiles = validFiles;
    showImgFileInfo(validFiles);
}

// 显示图片文件信息
function showImgFileInfo(files) {
    const container = document.getElementById('img-preview-container');
    const fileCount = document.getElementById('img-file-count');
    const previewList = document.getElementById('img-preview-list');

    if (container && fileCount && previewList) {
        fileCount.textContent = `已选择 ${files.length} 个图片文件`;
        
        // 创建预览
        previewList.innerHTML = '';
        files.forEach((file, index) => {
            const preview = document.createElement('div');
            preview.className = 'img-preview-item';
            preview.innerHTML = `
                <small>${file.name}</small>
                <br>
                <small class="text-muted">${formatFileSize(file.size)}</small>
            `;
            previewList.appendChild(preview);
        });

        container.classList.remove('d-none');
    }
}

// 上传文档
async function uploadDocument() {
    if (!selectedDocFile) {
        alert('请先选择文档文件');
        return;
    }

    const title = document.getElementById('doc-title').value.trim();
    const category = document.getElementById('doc-category').value;
    const description = document.getElementById('doc-description').value.trim();

    if (!title) {
        alert('请输入文档标题');
        return;
    }

    if (!category) {
        alert('请选择文档类别');
        return;
    }

    const uploadBtn = document.getElementById('doc-upload-btn');
    const progressContainer = document.getElementById('doc-upload-progress');
    const progressBar = document.getElementById('doc-progress-bar');
    const progressText = document.getElementById('doc-progress-text');

    try {
        // 显示进度
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';
        progressContainer.classList.remove('d-none');
        progressBar.style.width = '0%';
        progressText.textContent = '准备上传...';

        // 创建FormData
        const formData = new FormData();
        formData.append('file', selectedDocFile);

        // 创建metadata对象
        const metadata = {
            title: title,
            category: category,
            description: description,
            type: 'document'
        };
        formData.append('metadata', JSON.stringify(metadata));

        // 模拟进度更新
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 20;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
            progressText.textContent = `上传中... ${Math.round(progress)}%`;
        }, 200);

        // 发送请求
        const response = await fetch('/api/v1/knowledge/documents/add', {
            method: 'POST',
            body: formData
        });

        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressText.textContent = '上传完成';

        if (response.ok) {
            const result = await response.json();
            alert('文档上传成功！');
            closeModal('addDocumentModal');
            resetDocumentForm();
        } else {
            const error = await response.json();
            throw new Error(error.message || '上传失败');
        }

    } catch (error) {
        console.error('上传失败:', error);
        alert('上传失败: ' + error.message);
        progressText.textContent = '上传失败';
        progressBar.style.width = '0%';
    } finally {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="bi bi-cloud-upload me-1"></i>上传文档';
        setTimeout(() => {
            progressContainer.classList.add('d-none');
        }, 2000);
    }
}

// 上传图片
async function uploadImages() {
    if (!selectedImageFiles.length) {
        alert('请先选择图片文件');
        return;
    }

    const title = document.getElementById('img-title').value.trim();
    const category = document.getElementById('img-category').value;
    const description = document.getElementById('img-description').value.trim();

    if (!title) {
        alert('请输入图片标题');
        return;
    }

    if (!category) {
        alert('请选择图片类别');
        return;
    }

    const uploadBtn = document.getElementById('img-upload-btn');
    const progressContainer = document.getElementById('img-upload-progress');
    const progressBar = document.getElementById('img-progress-bar');
    const progressText = document.getElementById('img-progress-text');

    try {
        // 显示进度
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 上传中...';
        progressContainer.classList.remove('d-none');
        progressBar.style.width = '0%';
        progressText.textContent = '准备上传...';

        // 创建FormData
        const formData = new FormData();
        selectedImageFiles.forEach((file) => {
            formData.append('files', file);
        });
        formData.append('title', title);
        formData.append('category', category);
        formData.append('description', description);

        // 模拟进度更新
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
            progressText.textContent = `上传中... ${Math.round(progress)}%`;
        }, 300);

        // 发送请求
        const response = await fetch('/api/v1/knowledge/images/add', {
            method: 'POST',
            body: formData
        });

        clearInterval(progressInterval);
        progressBar.style.width = '100%';
        progressText.textContent = '上传完成';

        if (response.ok) {
            const result = await response.json();
            alert(`成功上传 ${selectedImageFiles.length} 个图片文件！`);
            closeModal('addImageModal');
            resetImageForm();
        } else {
            const error = await response.json();
            throw new Error(error.message || '上传失败');
        }

    } catch (error) {
        console.error('上传失败:', error);
        alert('上传失败: ' + error.message);
        progressText.textContent = '上传失败';
        progressBar.style.width = '0%';
    } finally {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="bi bi-cloud-upload me-1"></i>上传图片';
        setTimeout(() => {
            progressContainer.classList.add('d-none');
        }, 2000);
    }
}

// 重置文档表单
function resetDocumentForm() {
    selectedDocFile = null;
    document.getElementById('doc-title').value = '';
    document.getElementById('doc-category').value = '';
    document.getElementById('doc-description').value = '';
    document.getElementById('doc-file').value = '';
    document.getElementById('doc-file-info').classList.add('d-none');
}

// 重置图片表单
function resetImageForm() {
    selectedImageFiles = [];
    document.getElementById('img-title').value = '';
    document.getElementById('img-category').value = '';
    document.getElementById('img-description').value = '';
    document.getElementById('img-file').value = '';
    document.getElementById('img-preview-container').classList.add('d-none');
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {

    // 绑定文件选择事件
    const docFileInput = document.getElementById('doc-file');
    const imgFileInput = document.getElementById('img-file');

    if (docFileInput) {
        docFileInput.addEventListener('change', function() {
            handleDocFileSelectSimple(this);
        });
    }

    if (imgFileInput) {
        imgFileInput.addEventListener('change', function() {
            handleImgFileSelectSimple(this);
        });
    }

    // 绑定上传区域点击事件
    const docUploadArea = document.querySelector('#addDocumentModal .upload-area');
    const imgUploadArea = document.querySelector('#addImageModal .upload-area');

    if (docUploadArea) {
        docUploadArea.addEventListener('click', function() {

            docFileInput.click();
        });
    }

    if (imgUploadArea) {
        imgUploadArea.addEventListener('click', function() {

            imgFileInput.click();
        });
    }
});
