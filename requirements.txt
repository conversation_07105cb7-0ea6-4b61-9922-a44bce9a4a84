# 故障分析智能助手项目依赖 - 兼容性优化版本

# 基础科学计算库（优先安装）
numpy==1.24.3
scipy==1.11.4

# 核心异步和HTTP库
anyio>=3.7.1,<4.0.0
httpx>=0.24.0

# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# AI/ML核心库（兼容版本）
torch==2.1.2
transformers==4.36.2
sentence-transformers==2.2.2
openai==1.6.1

# Lang<PERSON>hain生态
tenacity>=8.1.0,<9.0.0
langchain==0.1.0
langchain-community==0.0.10

# 向量数据库
faiss-cpu==1.7.4
chromadb>=1.0.0

# 数据处理
pandas==2.1.4
openpyxl==3.1.2
python-docx==1.1.0
PyPDF2==3.0.1
pillow==10.1.0
opencv-python

# OCR和图像处理
pytesseract==0.3.10
easyocr==1.7.0
paddleocr

# Web框架和模板
flask==3.0.0
flask-cors==4.0.0
flask-socketio==5.3.6
python-socketio==5.9.0
jinja2==3.1.2
aiofiles==23.2.1
python-multipart==0.0.6

# 配置管理
pyyaml==6.0.1
python-dotenv==1.0.0

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 数据库
sqlalchemy==2.0.23
alembic==1.13.1
# sqlite3 is built-in to Python

# 工具库
requests>=2.31.0
tqdm>=4.66.0
click>=8.1.0
rich>=13.7.0
psutil>=5.9.0

# 中文处理
jieba

# 开发工具（可选，生产环境可移除）
# pytest==7.4.3
# pytest-asyncio==0.21.1
# black==23.11.0
# flake8==6.1.0
# mypy==1.7.1

# 时间序列和科学计算
matplotlib>=3.8.0
seaborn>=0.13.0
plotly>=5.17.0
scikit-learn>=1.3.0
scikit-image

# 异步和并发
aiohttp==3.9.1
celery==5.3.4
redis==5.0.1
