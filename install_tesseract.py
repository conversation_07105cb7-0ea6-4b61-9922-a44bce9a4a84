#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tesseract OCR安装和配置脚本
"""

import os
import sys
import subprocess
import requests
import zipfile
from pathlib import Path

def download_file(url, filename):
    """下载文件"""
    print(f"正在下载 {filename}...")
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(filename, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        
        print(f"✅ {filename} 下载完成")
        return True
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def install_tesseract_windows():
    """在Windows上安装Tesseract OCR"""
    print("🔧 开始安装Tesseract OCR (Windows)...")
    
    # Tesseract Windows安装包URL (最新版本)
    tesseract_url = "https://github.com/UB-Mannheim/tesseract/releases/download/v5.4.0.20240606/tesseract-ocr-w64-setup-5.4.0.20240606.exe"
    installer_name = "tesseract-installer.exe"
    
    # 下载安装包
    if not download_file(tesseract_url, installer_name):
        return False
    
    print("请手动运行安装程序并按照以下步骤操作：")
    print("1. 运行 tesseract-installer.exe")
    print("2. 选择安装路径（建议默认路径）")
    print("3. 确保选择安装中文语言包")
    print("4. 完成安装后，将安装路径添加到系统PATH环境变量")
    
    # 尝试自动运行安装程序
    try:
        subprocess.run([installer_name], check=False)
    except Exception as e:
        print(f"⚠️ 无法自动运行安装程序: {e}")
        print(f"请手动运行 {installer_name}")
    
    return True

def check_tesseract_installation():
    """检查Tesseract是否已安装"""
    print("🔍 检查Tesseract安装状态...")
    
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, check=True)
        print("✅ Tesseract已安装")
        print(f"版本信息: {result.stdout.split()[1]}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Tesseract未安装或未添加到PATH")
        return False

def configure_tesseract_path():
    """配置Tesseract路径"""
    print("🔧 配置Tesseract路径...")
    
    # 常见的Tesseract安装路径
    possible_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME')),
    ]
    
    tesseract_path = None
    for path in possible_paths:
        if os.path.exists(path):
            tesseract_path = path
            break
    
    if tesseract_path:
        print(f"✅ 找到Tesseract: {tesseract_path}")
        
        # 更新OCR处理器配置
        config_content = f'''
# Tesseract OCR配置
import pytesseract
pytesseract.pytesseract.tesseract_cmd = r"{tesseract_path}"

# 中文语言包配置
TESSERACT_CONFIG = {{
    'lang': 'chi_sim+eng',  # 简体中文+英文
    'config': '--oem 3 --psm 6'  # OCR引擎模式和页面分割模式
}}
'''
        
        # 保存配置文件
        with open('tesseract_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ Tesseract配置文件已创建: tesseract_config.py")
        return True
    else:
        print("❌ 未找到Tesseract安装路径")
        return False

def test_ocr_functionality():
    """测试OCR功能"""
    print("🧪 测试OCR功能...")
    
    try:
        import pytesseract
        from PIL import Image
        import numpy as np
        
        # 创建一个简单的测试图像
        test_image = Image.new('RGB', (200, 100), color='white')
        
        # 尝试OCR识别
        try:
            text = pytesseract.image_to_string(test_image, lang='chi_sim+eng')
            print("✅ OCR功能测试成功")
            return True
        except Exception as e:
            print(f"❌ OCR功能测试失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 缺少必要的Python包: {e}")
        print("请运行: pip install pytesseract pillow")
        return False

def install_python_packages():
    """安装必要的Python包"""
    print("📦 安装Python OCR相关包...")
    
    packages = ['pytesseract', 'pillow', 'opencv-python']
    
    for package in packages:
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                         check=True, capture_output=True)
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def main():
    """主函数"""
    print("🚀 开始Tesseract OCR安装和配置...")
    
    # 检查操作系统
    if os.name != 'nt':
        print("⚠️ 此脚本仅支持Windows系统")
        print("Linux/Mac用户请使用包管理器安装Tesseract")
        return
    
    # 1. 检查是否已安装
    if check_tesseract_installation():
        print("Tesseract已安装，跳过安装步骤")
    else:
        # 2. 安装Tesseract
        if not install_tesseract_windows():
            print("❌ Tesseract安装失败")
            return
        
        print("请完成Tesseract安装后重新运行此脚本")
        return
    
    # 3. 安装Python包
    if not install_python_packages():
        print("❌ Python包安装失败")
        return
    
    # 4. 配置路径
    if not configure_tesseract_path():
        print("❌ Tesseract路径配置失败")
        return
    
    # 5. 测试功能
    if test_ocr_functionality():
        print("🎉 Tesseract OCR安装和配置完成！")
    else:
        print("⚠️ OCR功能测试失败，请检查配置")

if __name__ == "__main__":
    main()
