"""
Tesseract OCR 安装和配置工具

自动检测和配置Tesseract OCR环境
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_tesseract_installed():
    """检查Tesseract是否已安装"""
    try:
        result = subprocess.run(['tesseract', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version_info = result.stdout.split('\n')[0]
            print(f"✅ Tesseract已安装: {version_info}")
            return True
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        pass
    
    print("❌ Tesseract未安装或不在PATH中")
    return False


def find_tesseract_path():
    """查找Tesseract安装路径"""
    possible_paths = []
    
    if platform.system() == "Windows":
        possible_paths = [
            r"C:\Program Files\Tesseract-OCR\tesseract.exe",
            r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
            r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
            r"D:\Program Files\Tesseract-OCR\tesseract.exe",
            r"E:\Program Files\Tesseract-OCR\tesseract.exe"
        ]
    elif platform.system() == "Linux":
        possible_paths = [
            "/usr/bin/tesseract",
            "/usr/local/bin/tesseract",
            "/opt/tesseract/bin/tesseract"
        ]
    elif platform.system() == "Darwin":  # macOS
        possible_paths = [
            "/usr/local/bin/tesseract",
            "/opt/homebrew/bin/tesseract",
            "/usr/bin/tesseract"
        ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ 找到Tesseract: {path}")
            return path
    
    return None


def install_tesseract():
    """安装Tesseract OCR"""
    system = platform.system()
    
    print(f"🔧 开始安装Tesseract OCR (系统: {system})...")
    
    try:
        if system == "Windows":
            print("📥 Windows系统请手动下载安装:")
            print("   1. 访问: https://github.com/UB-Mannheim/tesseract/wiki")
            print("   2. 下载最新版本的Windows安装包")
            print("   3. 安装时选择包含中文语言包")
            print("   4. 将安装目录添加到系统PATH")
            return False
            
        elif system == "Linux":
            # Ubuntu/Debian
            if os.path.exists("/usr/bin/apt"):
                subprocess.run(['sudo', 'apt', 'update'], check=True)
                subprocess.run(['sudo', 'apt', 'install', '-y', 'tesseract-ocr', 'tesseract-ocr-chi-sim'], check=True)
            # CentOS/RHEL
            elif os.path.exists("/usr/bin/yum"):
                subprocess.run(['sudo', 'yum', 'install', '-y', 'tesseract', 'tesseract-langpack-chi_sim'], check=True)
            else:
                print("❌ 不支持的Linux发行版，请手动安装Tesseract")
                return False
                
        elif system == "Darwin":  # macOS
            # 使用Homebrew
            subprocess.run(['brew', 'install', 'tesseract', 'tesseract-lang'], check=True)
        
        print("✅ Tesseract安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ 包管理器未找到，请手动安装Tesseract")
        return False


def create_tesseract_config():
    """创建Tesseract配置文件"""
    tesseract_path = find_tesseract_path()
    
    if tesseract_path:
        config_content = f'''"""
Tesseract OCR 配置文件
自动生成于: {os.path.basename(__file__)}
"""

import os

# Tesseract可执行文件路径
TESSERACT_CMD = r"{tesseract_path}"

# 设置pytesseract配置
try:
    import pytesseract
    pytesseract.pytesseract.tesseract_cmd = TESSERACT_CMD
    print("✅ Tesseract配置已应用")
except ImportError:
    print("⚠️ pytesseract未安装，请运行: pip install pytesseract")

# 支持的语言
SUPPORTED_LANGUAGES = [
    'eng',      # 英语
    'chi_sim',  # 简体中文
    'chi_tra'   # 繁体中文
]

# OCR配置选项
OCR_CONFIG = {{
    'lang': 'chi_sim+eng',  # 中英文混合
    'config': '--oem 3 --psm 6'  # OCR引擎模式和页面分割模式
}}
'''
        
        # 保存配置文件
        with open('tesseract_config.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print("✅ Tesseract配置文件已创建: tesseract_config.py")
        return True
    else:
        print("❌ 未找到Tesseract安装路径")
        return False


def test_ocr_functionality():
    """测试OCR功能"""
    print("🧪 测试OCR功能...")
    
    try:
        import pytesseract
        from PIL import Image
        import numpy as np
        
        # 创建一个简单的测试图像
        test_image = Image.new('RGB', (200, 100), color='white')
        
        # 尝试OCR识别
        try:
            text = pytesseract.image_to_string(test_image, lang='chi_sim+eng')
            print("✅ OCR功能测试成功")
            return True
        except Exception as e:
            print(f"❌ OCR功能测试失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        print("请安装: pip install pytesseract pillow")
        return False


def main():
    """主函数"""
    print("🔧 Tesseract OCR 安装和配置工具")
    print("=" * 50)
    
    # 1. 检查是否已安装
    if check_tesseract_installed():
        print("✅ Tesseract已可用")
    else:
        # 2. 尝试查找安装路径
        tesseract_path = find_tesseract_path()
        if tesseract_path:
            print(f"✅ 找到Tesseract但不在PATH中: {tesseract_path}")
        else:
            # 3. 尝试安装
            print("🔧 尝试自动安装Tesseract...")
            if not install_tesseract():
                print("❌ 自动安装失败，请手动安装")
                return False
    
    # 4. 创建配置文件
    print("\n🔧 创建配置文件...")
    create_tesseract_config()
    
    # 5. 测试功能
    print("\n🧪 测试OCR功能...")
    test_ocr_functionality()
    
    print("\n✅ Tesseract配置完成!")
    print("💡 提示:")
    print("   - 配置文件已保存为 tesseract_config.py")
    print("   - 在代码中导入此配置文件即可使用OCR功能")
    print("   - 如果测试失败，请检查语言包是否正确安装")


if __name__ == "__main__":
    main()
