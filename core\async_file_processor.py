"""
异步文件处理器

提供文档和图片的异步处理功能
"""

import asyncio
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional, Callable
import logging

logger = logging.getLogger(__name__)


class AsyncFileProcessor:
    """异步文件处理器"""
    
    def __init__(self):
        """初始化处理器"""
        self.text_processor = None
        self.ocr_processor = None
        self.image_processor = None
        self.knowledge_base = None
        
    def _lazy_init_processors(self):
        """延迟初始化处理器"""
        if self.text_processor is None:
            try:
                from data_processing.text_processor import TextProcessor
                self.text_processor = TextProcessor()
            except Exception as e:
                logger.error(f"初始化文本处理器失败: {str(e)}")
        
        if self.ocr_processor is None:
            try:
                from data_processing.ocr_processor import OCRProcessor
                self.ocr_processor = OCRProcessor()
            except Exception as e:
                logger.error(f"初始化OCR处理器失败: {str(e)}")
        
        if self.image_processor is None:
            try:
                from data_processing.image_processor import ImageProcessor
                self.image_processor = ImageProcessor()
            except Exception as e:
                logger.error(f"初始化图像处理器失败: {str(e)}")
    
    def _get_knowledge_base(self):
        """获取知识库实例"""
        if self.knowledge_base is None:
            try:
                from retriever.knowledge_base import KnowledgeBase
                self.knowledge_base = KnowledgeBase()
            except Exception as e:
                logger.error(f"获取知识库实例失败: {str(e)}")
        return self.knowledge_base
    
    async def process_document_async(self, task_id: str, progress_callback: Callable, 
                                   file_path: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        异步处理文档
        
        Args:
            task_id: 任务ID
            progress_callback: 进度回调函数
            file_path: 文件路径
            metadata: 元数据
            
        Returns:
            处理结果
        """
        try:
            progress_callback(task_id, 0.1, "开始处理文档")
            
            # 延迟初始化处理器
            self._lazy_init_processors()
            
            if not self.text_processor:
                raise Exception("文本处理器初始化失败")
            
            progress_callback(task_id, 0.2, "提取文档内容")
            
            # 提取文档内容
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 在线程池中执行文档处理（避免阻塞事件循环）
            loop = asyncio.get_event_loop()
            documents = await loop.run_in_executor(
                None, 
                self.text_processor.process_file, 
                str(file_path_obj)
            )
            
            progress_callback(task_id, 0.6, f"文档分块完成，共 {len(documents)} 个块")
            
            # 添加到知识库
            knowledge_base = self._get_knowledge_base()
            if knowledge_base and documents:
                progress_callback(task_id, 0.8, "添加到知识库")
                
                # 构建文档信息
                doc_info = {
                    'file_path': file_path,
                    'title': metadata.get('title', file_path_obj.name),
                    'content': '\n'.join([doc.get('content', '') for doc in documents]),
                    'doc_type': 'document',
                    'metadata': metadata
                }
                
                # 在线程池中执行知识库操作
                success = await loop.run_in_executor(
                    None,
                    knowledge_base.add_document,
                    file_path,
                    metadata.get('doc_id'),
                    metadata.get('title'),
                    None,  # content will be extracted from file
                    'document',
                    metadata
                )
                
                if not success:
                    raise Exception("添加到知识库失败")
            
            progress_callback(task_id, 1.0, "文档处理完成")
            
            return {
                'success': True,
                'file_path': file_path,
                'documents_count': len(documents),
                'metadata': metadata,
                'message': f"文档处理完成，共生成 {len(documents)} 个文档块"
            }
            
        except Exception as e:
            logger.error(f"异步文档处理失败 {task_id}: {str(e)}")
            raise
    
    async def process_image_async(self, task_id: str, progress_callback: Callable,
                                file_path: str, metadata: Dict[str, Any],
                                enable_ocr: bool = False, enable_defect_detection: bool = False) -> Dict[str, Any]:
        """
        异步处理图片
        
        Args:
            task_id: 任务ID
            progress_callback: 进度回调函数
            file_path: 文件路径
            metadata: 元数据
            enable_ocr: 是否启用OCR
            enable_defect_detection: 是否启用缺陷检测
            
        Returns:
            处理结果
        """
        try:
            progress_callback(task_id, 0.1, "开始处理图片")
            
            # 延迟初始化处理器
            self._lazy_init_processors()
            
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            result = {
                'success': True,
                'file_path': file_path,
                'metadata': metadata,
                'processing_results': {}
            }
            
            current_progress = 0.2
            progress_step = 0.3
            
            # OCR处理
            if enable_ocr and self.ocr_processor:
                progress_callback(task_id, current_progress, "执行OCR文字识别")
                
                loop = asyncio.get_event_loop()
                ocr_result = await loop.run_in_executor(
                    None,
                    self.ocr_processor.extract_text,
                    str(file_path_obj)
                )
                
                result['processing_results']['ocr'] = ocr_result
                current_progress += progress_step
            
            # 缺陷检测
            if enable_defect_detection and self.image_processor:
                progress_callback(task_id, current_progress, "执行缺陷检测")
                
                loop = asyncio.get_event_loop()
                defect_result = await loop.run_in_executor(
                    None,
                    self.image_processor.detect_defects,
                    str(file_path_obj)
                )
                
                result['processing_results']['defects'] = defect_result
                current_progress += progress_step
            
            # 添加到知识库
            progress_callback(task_id, 0.8, "添加到知识库")
            
            knowledge_base = self._get_knowledge_base()
            if knowledge_base:
                loop = asyncio.get_event_loop()
                success = await loop.run_in_executor(
                    None,
                    knowledge_base.add_image,
                    file_path,
                    metadata
                )
                
                if not success:
                    raise Exception("添加到知识库失败")
            
            progress_callback(task_id, 1.0, "图片处理完成")
            
            result['message'] = "图片处理完成"
            return result
            
        except Exception as e:
            logger.error(f"异步图片处理失败 {task_id}: {str(e)}")
            raise
    
    def process_document_sync(self, task_id: str, progress_callback: Callable,
                            file_path: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        同步处理文档（用于在线程池中执行）
        """
        try:
            progress_callback(task_id, 0.1, "开始处理文档")
            
            # 延迟初始化处理器
            self._lazy_init_processors()
            
            if not self.text_processor:
                raise Exception("文本处理器初始化失败")
            
            progress_callback(task_id, 0.2, "提取文档内容")
            
            # 提取文档内容
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            documents = self.text_processor.process_file(str(file_path_obj))
            
            progress_callback(task_id, 0.6, f"文档分块完成，共 {len(documents)} 个块")
            
            # 添加到知识库
            knowledge_base = self._get_knowledge_base()
            if knowledge_base and documents:
                progress_callback(task_id, 0.8, "添加到知识库")
                
                success = knowledge_base.add_document(
                    file_path=file_path,
                    doc_id=metadata.get('doc_id'),
                    title=metadata.get('title'),
                    content=None,  # content will be extracted from file
                    doc_type='document',
                    metadata=metadata
                )
                
                if not success:
                    raise Exception("添加到知识库失败")
            
            progress_callback(task_id, 1.0, "文档处理完成")
            
            return {
                'success': True,
                'file_path': file_path,
                'documents_count': len(documents),
                'metadata': metadata,
                'message': f"文档处理完成，共生成 {len(documents)} 个文档块"
            }
            
        except Exception as e:
            logger.error(f"同步文档处理失败 {task_id}: {str(e)}")
            raise
    
    def process_image_sync(self, task_id: str, progress_callback: Callable,
                         file_path: str, metadata: Dict[str, Any],
                         enable_ocr: bool = False, enable_defect_detection: bool = False) -> Dict[str, Any]:
        """
        同步处理图片（用于在线程池中执行）
        """
        try:
            progress_callback(task_id, 0.1, "开始处理图片")
            
            # 延迟初始化处理器
            self._lazy_init_processors()
            
            file_path_obj = Path(file_path)
            if not file_path_obj.exists():
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            result = {
                'success': True,
                'file_path': file_path,
                'metadata': metadata,
                'processing_results': {}
            }
            
            current_progress = 0.2
            progress_step = 0.3
            
            # OCR处理
            if enable_ocr and self.ocr_processor:
                progress_callback(task_id, current_progress, "执行OCR文字识别")
                ocr_result = self.ocr_processor.extract_text(str(file_path_obj))
                result['processing_results']['ocr'] = ocr_result
                current_progress += progress_step
            
            # 缺陷检测
            if enable_defect_detection and self.image_processor:
                progress_callback(task_id, current_progress, "执行缺陷检测")
                defect_result = self.image_processor.detect_defects(str(file_path_obj))
                result['processing_results']['defects'] = defect_result
                current_progress += progress_step
            
            # 添加到知识库
            progress_callback(task_id, 0.8, "添加到知识库")
            
            knowledge_base = self._get_knowledge_base()
            if knowledge_base:
                success = knowledge_base.add_image(file_path, metadata)
                if not success:
                    raise Exception("添加到知识库失败")
            
            progress_callback(task_id, 1.0, "图片处理完成")
            
            result['message'] = "图片处理完成"
            return result
            
        except Exception as e:
            logger.error(f"同步图片处理失败 {task_id}: {str(e)}")
            raise


# 全局文件处理器实例
file_processor = AsyncFileProcessor()


def get_file_processor() -> AsyncFileProcessor:
    """获取文件处理器实例"""
    return file_processor
