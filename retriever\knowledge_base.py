"""
知识库管理模块

统一管理文档、图像等知识资源
"""

import os
import json
import time
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
from loguru import logger

from data_processing.text_processor import TextProcessor
from data_processing.image_processor import ImageProcessor
from data_processing.ocr_processor import OCRProcessor
from .text_retriever import TextRetriever
from .multimodal_retriever import MultimodalRetriever


class KnowledgeBase:
    """知识库管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.text_path = config.get("text_path", "./knowledge_base/text")
        self.images_path = config.get("images_path", "./knowledge_base/images")
        self.mappings_path = config.get("mappings_path", "./knowledge_base/mappings")
        
        # 初始化处理器
        self.text_processor = TextProcessor(config.get("data_processing", {}))
        self.image_processor = ImageProcessor(config.get("data_processing", {}).get("image", {}))
        self.ocr_processor = OCRProcessor(config.get("data_processing", {}).get("ocr", {}))
        
        # 初始化检索器
        self.text_retriever = TextRetriever(config.get("vector_db", {}))
        self.multimodal_retriever = MultimodalRetriever(config)
        
        # 确保目录存在
        self._ensure_directories()
        
        # 加载现有知识库
        self._load_knowledge_base()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        try:
            for path in [self.text_path, self.images_path, self.mappings_path]:
                os.makedirs(path, exist_ok=True)
            logger.info("知识库目录检查完成")
            
        except Exception as e:
            logger.error(f"创建知识库目录失败: {str(e)}")
    
    def _load_knowledge_base(self):
        """加载现有知识库"""
        try:
            # 检查是否有现有的索引
            stats = self.text_retriever.get_index_stats()
            if stats.get("total_documents", 0) == 0:
                logger.info("未找到现有索引，开始构建知识库")
                self.build_knowledge_base()
            else:
                logger.info(f"加载现有知识库，包含 {stats['total_documents']} 个文档")
                
        except Exception as e:
            logger.error(f"加载知识库失败: {str(e)}")
    
    def build_knowledge_base(self) -> bool:
        """
        构建知识库
        
        Returns:
            是否成功
        """
        try:
            logger.info("开始构建知识库...")
            
            # 处理文本文档
            text_documents = self._process_text_documents()
            
            # 构建文本索引
            if text_documents:
                success = self.text_retriever.build_index(text_documents)
                if not success:
                    logger.error("文本索引构建失败")
                    return False
            
            # 处理图像（多模态检索器会自动处理）
            self._process_images()
            
            # 保存知识库元数据
            self._save_metadata()
            
            logger.info("知识库构建完成")
            return True
            
        except Exception as e:
            logger.error(f"知识库构建失败: {str(e)}")
            return False
    
    def _process_text_documents(self) -> List[Dict[str, Any]]:
        """处理文本文档"""
        try:
            if not os.path.exists(self.text_path):
                logger.warning(f"文本目录不存在: {self.text_path}")
                return []
            
            logger.info("开始处理文本文档...")
            documents = self.text_processor.batch_process(self.text_path)
            
            logger.info(f"文本文档处理完成，共 {len(documents)} 个文档块")
            return documents
            
        except Exception as e:
            logger.error(f"文本文档处理失败: {str(e)}")
            return []
    
    def _process_images(self):
        """处理图像文件"""
        try:
            if not os.path.exists(self.images_path):
                logger.warning(f"图像目录不存在: {self.images_path}")
                return
            
            logger.info("开始处理图像文件...")
            
            # 多模态检索器会自动处理图像目录
            # 这里可以添加额外的图像处理逻辑
            
            logger.info("图像文件处理完成")
            
        except Exception as e:
            logger.error(f"图像处理失败: {str(e)}")
    
    def _save_metadata(self):
        """保存知识库元数据"""
        try:
            metadata = {
                "version": "1.0",
                "created_at": datetime.now().isoformat(),
                "text_stats": self.text_retriever.get_index_stats(),
                "multimodal_stats": self.multimodal_retriever.get_stats(),
                "config": self.config
            }
            
            metadata_file = os.path.join(self.mappings_path, "knowledge_base_metadata.json")
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            logger.info(f"知识库元数据已保存: {metadata_file}")
            
        except Exception as e:
            logger.error(f"保存元数据失败: {str(e)}")
    
    def add_document(self, file_path: str = None, doc_id: str = None, title: str = None,
                  content: str = None, doc_type: str = "auto", metadata: Dict[str, Any] = None) -> bool:
        """
        添加单个文档到知识库 - 增强版本

        Args:
            file_path: 文件路径（可选）
            doc_id: 文档ID（可选）
            title: 文档标题（可选）
            content: 文档内容（可选）
            doc_type: 文档类型 (text, image, auto)
            metadata: 文档元数据（可选）

        Returns:
            是否成功
        """
        try:
            logger.info(f"添加文档: {title or doc_id or file_path}")

            # 初始化元数据
            if metadata is None:
                metadata = {}

            # 处理文件路径
            if file_path:
                file_path = Path(file_path)
                if not file_path.exists():
                    logger.error(f"文件不存在: {file_path}")
                    return False

                # 自动判断文档类型
                if doc_type == "auto":
                    extension = file_path.suffix.lower()
                    if extension in ['.txt', '.md', '.pdf', '.docx']:
                        doc_type = "text"
                    elif extension in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                        doc_type = "image"
                    else:
                        logger.warning(f"无法识别文件类型: {extension}")
                        return False

                # 基于文件路径添加文档
                if doc_type == "text":
                    return self._add_text_document(str(file_path), doc_id, title, metadata)
                elif doc_type == "image":
                    return self._add_image_document(str(file_path), doc_id, title, metadata)
                else:
                    logger.error(f"不支持的文档类型: {doc_type}")
                    return False

            # 基于内容添加文档
            elif content:
                if doc_type == "text":
                    return self._add_text_content(content, doc_id, title, metadata)
                elif doc_type == "image":
                    logger.error("图像类型必须提供文件路径")
                    return False
                else:
                    logger.error(f"不支持的文档类型: {doc_type}")
                    return False

            else:
                logger.error("必须提供文件路径或文档内容")
                return False

        except Exception as e:
            logger.error(f"添加文档失败: {str(e)}")
            return False
    
    def _add_text_document(self, file_path: str, doc_id: str = None, title: str = None, metadata: Dict[str, Any] = None) -> bool:
        """添加文本文档"""
        try:
            logger.info(f"开始处理文本文档: {file_path}")

            # 处理文档
            documents = self.text_processor.process_file(file_path)

            if not documents:
                logger.error(f"文档处理失败: {file_path}")
                return False

            logger.info(f"文档处理成功，生成 {len(documents)} 个文档块")

            # 检查文档内容
            for i, doc in enumerate(documents):
                content = doc.get('content', '')
                if not content or not content.strip():
                    logger.warning(f"文档块 {i} 内容为空")
                else:
                    logger.info(f"文档块 {i} 内容长度: {len(content)} 字符")

            # 增强文档信息
            for doc in documents:
                if doc_id:
                    doc['doc_id'] = doc_id
                if title:
                    doc['title'] = title
                if metadata:
                    doc['metadata'] = metadata
                    # 添加元数据字段到文档内容
                    if metadata.get('equipment_type'):
                        doc['equipment_type'] = metadata['equipment_type']
                    if metadata.get('voltage_level'):
                        doc['voltage_level'] = metadata['voltage_level']
                    if metadata.get('fault_type'):
                        doc['fault_type'] = metadata['fault_type']
                    if metadata.get('tags'):
                        doc['tags'] = metadata['tags']

            logger.info(f"准备添加 {len(documents)} 个文档到索引")

            # 添加到索引
            success = self.text_retriever.add_documents(documents)

            if success:
                logger.info(f"成功添加文本文档: {file_path}")
            else:
                logger.error(f"索引添加失败: {file_path}")

            return success

        except Exception as e:
            logger.error(f"添加文本文档失败: {str(e)}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False

    def _add_text_content(self, content: str, doc_id: str = None, title: str = None, metadata: Dict[str, Any] = None) -> bool:
        """添加文本内容（无文件）"""
        try:
            # 构建文档对象
            document = {
                'content': content,
                'doc_id': doc_id or f"text_{int(time.time())}",
                'title': title or "Text Document",
                'source': "direct_input",
                'metadata': metadata or {}
            }

            # 增强文档信息
            if metadata:
                if metadata.get('equipment_type'):
                    document['equipment_type'] = metadata['equipment_type']
                if metadata.get('voltage_level'):
                    document['voltage_level'] = metadata['voltage_level']
                if metadata.get('fault_type'):
                    document['fault_type'] = metadata['fault_type']
                if metadata.get('tags'):
                    document['tags'] = metadata['tags']

            # 使用文本处理器处理内容
            processed_docs = self.text_processor.process_text(content, document)

            if not processed_docs:
                logger.error("文本内容处理失败")
                return False

            # 添加到索引
            success = self.text_retriever.add_documents(processed_docs)

            if success:
                logger.info(f"成功添加文本内容: {title}")

            return success

        except Exception as e:
            logger.error(f"添加文本内容失败: {str(e)}")
            return False
    
    def _add_image_document(self, file_path: str, doc_id: str = None, title: str = None, metadata: Dict[str, Any] = None) -> bool:
        """添加图像文档"""
        try:
            # 构建图像元数据
            image_metadata = {
                'doc_id': doc_id or f"img_{int(time.time())}",
                'title': title or os.path.basename(file_path),
                'file_path': file_path,
                'metadata': metadata or {}
            }

            # 如果有额外元数据，添加到图像元数据中
            if metadata:
                image_metadata.update(metadata)

            success = self.multimodal_retriever.add_image(file_path, image_metadata)

            if success:
                logger.info(f"成功添加图像文档: {file_path}")

            return success

        except Exception as e:
            logger.error(f"添加图像文档失败: {str(e)}")
            return False

    def add_image(self, file_path: str, metadata: Dict[str, Any] = None) -> bool:
        """
        添加图像到知识库 - 兼容性方法

        Args:
            file_path: 图像文件路径
            metadata: 图像元数据

        Returns:
            是否成功
        """
        return self.add_document(file_path=file_path, doc_type="image", metadata=metadata)
    
    def search(self, query: str, search_type: str = "multimodal", top_k: int = 10) -> Dict[str, Any]:
        """
        搜索知识库
        
        Args:
            query: 查询文本
            search_type: 搜索类型 (text, image, multimodal)
            top_k: 返回结果数量
            
        Returns:
            搜索结果
        """
        try:
            if search_type == "text":
                results = self.text_retriever.search(query, top_k)
                return {"type": "text", "results": results}
            
            elif search_type == "image":
                results = self.multimodal_retriever.search_image_by_text(query, top_k)
                return {"type": "image", "results": results}
            
            elif search_type == "multimodal":
                results = self.multimodal_retriever.search_multimodal(query, top_k=top_k)
                return {"type": "multimodal", "results": results}
            
            else:
                logger.error(f"不支持的搜索类型: {search_type}")
                return {"type": "error", "results": []}
                
        except Exception as e:
            logger.error(f"搜索失败: {str(e)}")
            return {"type": "error", "results": []}
    
    def search_by_image(self, image_path: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        以图搜图
        
        Args:
            image_path: 查询图像路径
            top_k: 返回结果数量
            
        Returns:
            搜索结果
        """
        return self.multimodal_retriever.search_by_image(image_path, top_k)
    
    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        获取文档详情
        
        Args:
            doc_id: 文档ID
            
        Returns:
            文档信息
        """
        try:
            # 先尝试从文本检索器获取
            doc = self.text_retriever.get_document_by_id(doc_id)
            if doc:
                return {"type": "text", "document": doc}
            
            # 再尝试从图像检索器获取
            img_info = self.multimodal_retriever.get_image_info(doc_id)
            if img_info:
                return {"type": "image", "document": img_info}
            
            return None
            
        except Exception as e:
            logger.error(f"获取文档失败: {str(e)}")
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取知识库统计信息
        
        Returns:
            统计信息
        """
        try:
            text_stats = self.text_retriever.get_index_stats()
            multimodal_stats = self.multimodal_retriever.get_stats()
            
            stats = {
                "total_documents": text_stats.get("total_documents", 0),
                "total_images": multimodal_stats.get("images", 0),
                "total_items": text_stats.get("total_documents", 0) + multimodal_stats.get("images", 0),
                "text_stats": text_stats,
                "multimodal_stats": multimodal_stats,
                "directories": {
                    "text_path": self.text_path,
                    "images_path": self.images_path,
                    "mappings_path": self.mappings_path
                }
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}
    
    def rebuild_index(self) -> bool:
        """
        重建索引
        
        Returns:
            是否成功
        """
        try:
            logger.info("开始重建知识库索引...")
            
            # 清空现有索引
            self.text_retriever.clear_index()
            
            # 重新构建
            return self.build_knowledge_base()
            
        except Exception as e:
            logger.error(f"重建索引失败: {str(e)}")
            return False
