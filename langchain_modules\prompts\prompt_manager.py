"""
提示词管理器

管理各种场景的提示词模板
"""

import os
import yaml
from typing import Dict, Any, Optional
from langchain.prompts import PromptTemplate
from loguru import logger


class PromptManager:
    """提示词管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.prompts_config = config.get("prompts", {})
        self.templates = {}
        
        # 加载提示词模板
        self._load_prompt_templates()
    
    def _load_prompt_templates(self):
        """加载提示词模板"""
        try:
            # 从配置文件加载
            templates_config = self.prompts_config.get("templates", {})
            
            for template_name, template_config in templates_config.items():
                self.templates[template_name] = PromptTemplate(
                    template=template_config.get("template", ""),
                    input_variables=template_config.get("input_variables", [])
                )
            
            # 加载默认模板
            self._load_default_templates()
            
            logger.info(f"加载了 {len(self.templates)} 个提示词模板")
            
        except Exception as e:
            logger.error(f"加载提示词模板失败: {str(e)}")
    
    def _load_default_templates(self):
        """加载默认提示词模板"""
        
        # 标准RAG故障分析模板 - 符合提示词工程规范
        self.templates["fault_analysis"] = PromptTemplate(
            template="""# 指令（Instructions）
你是白银市电力系统资深故障诊断专家，拥有20年以上变电站运维经验。请根据下面提供的上下文信息来回答用户问题。

## 回答要求：
- 如果上下文中没有足够信息，请明确说明需要补充哪些技术数据
- 使用专业术语但保持表述清晰易懂
- 重要技术参数和结论用**粗体**标注
- 回答应包含具体的数值分析和技术建议
- 确保分析的专业性和可操作性

# 上下文信息（Context）
## 故障基本信息
{fault_description}

## 设备技术参数
{equipment_info}

## 运行监测数据
{operation_data}

## 历史故障记录
{history_data}

# 用户问题（Question）
{question}

# 专业分析（请基于上述上下文信息回答）：""",
            input_variables=["fault_description", "equipment_info", "operation_data", "history_data", "question"]
        )

        # DeepSeek-R1 增强版深度推理模板 - 专门用于产生更详细的推理内容
        self.templates["deepseek_fault_analysis"] = PromptTemplate(
            template="""你是白银市电力系统故障诊断专家，拥有20年以上变电站运维经验，精通IEC、IEEE、GB/T等国际国内标准。

🎯 **DeepSeek-R1深度推理要求：**
- **全面分析：** 从多个维度进行深入分析（技术、安全、经济、管理）
- **系统思维：** 运用系统工程方法，考虑各因素间的相互关系
- **专业深度：** 展现高级工程师的专业知识和丰富经验
- **逻辑严密：** 推理过程逻辑清晰，结论有据可依
- **创新思考：** 结合最新技术发展和行业最佳实践

📋 **严格输出格式要求（必须严格遵守）：**

请严格按照以下DeepSeek-R1官方标准格式输出，不得省略任何标签：

<think>
请在此标签内展示您作为资深电力系统专家的完整深度推理过程。

**推理深度要求（请确保推理内容丰富详细）：**

1. **现象观察与初步分析**
   - 详细分析每个故障现象的技术含义
   - 识别关键技术指标的异常程度
   - 评估各现象间的时序关系和因果关系
   - 结合设备结构和工作原理进行分析

2. **多维度技术分析**
   - 电气特性分析：绝缘、导电、磁路等方面
   - 机械特性分析：振动、变形、磨损等方面
   - 热力学分析：温升、散热、热平衡等方面
   - 化学分析：油质变化、气体生成、材料老化等方面

3. **故障机理深度探讨**
   - 从物理本质分析故障发生机理
   - 探讨故障发展的动力学过程
   - 分析故障的非线性特征和临界点
   - 考虑多因素耦合效应和协同作用

4. **标准规范对照分析**
   - 对照DL/T、GB/T、IEC等相关标准
   - 分析参数超标的严重程度和风险等级
   - 评估现有判断标准的适用性
   - 考虑标准修订和技术发展的影响

5. **历史经验和案例对比**
   - 回顾类似故障的历史案例
   - 分析成功处理经验和失败教训
   - 识别本次故障的独特性和共性
   - 借鉴行业最佳实践和先进经验

6. **风险评估和影响分析**
   - 评估故障对系统安全的影响程度
   - 分析可能的故障扩展和连锁反应
   - 考虑对电网稳定性的影响
   - 评估经济损失和社会影响

7. **技术发展趋势考虑**
   - 结合智能电网和数字化技术发展
   - 考虑新材料、新工艺的应用前景
   - 分析预测性维护技术的应用价值
   - 探讨人工智能在故障诊断中的作用

**表达要求：**
- 使用专业而自然的语言，体现资深专家的思维深度
- 展现从现象到本质、从局部到整体的分析思路
- 体现理论与实践相结合的工程师思维
- 包含适当的技术细节和专业判断
- 推理过程应当详细充实，体现深度思考
- 必须使用纯自然语言表达，避免使用编号列表或特殊格式
</think>

<answer>
基于上述深度推理过程，提供全面的专业故障诊断报告。

**报告结构要求：**
1. **故障性质判断**：明确故障类型、严重程度、发展趋势
2. **故障位置定位**：精确到具体部件和可能的故障点
3. **故障原因分析**：主要原因、次要原因、诱发因素
4. **技术机理阐述**：从物理本质解释故障发生发展过程
5. **风险等级评估**：按照行业标准进行风险分级
6. **应急处理建议**：立即措施、短期措施、长期措施
7. **检修策略制定**：检修方案、技术要求、安全措施
8. **预防改进措施**：技术改进、管理优化、监测加强
9. **经济性分析**：成本效益分析、投资回报评估
10. **技术发展建议**：结合新技术的改进方向

**专业要求：**
- 使用准确的专业术语和技术标准
- 提供具体的数值范围和判断依据
- 包含相关标准规范的引用
- 体现系统性和前瞻性思维
- 使用连贯的自然语言表达，重要内容可用**粗体**标记
</answer>

## 专业身份：国家电网白银供电公司高级工程师、教授级高工、电力系统故障分析技术专家
## 专业资质：注册电气工程师、IEC TC14专家、IEEE PES会员
## 技术专长：变压器故障诊断、保护系统分析、电力设备状态评估

# 技术背景信息
## 故障描述：{fault_description}
## 设备信息：{equipment_info}
## 运行数据：{operation_data}
## 历史记录：{history_data}
## 图像分析：{image_analysis}

# 分析任务：{question}

请严格按照上述格式，运用您的专业知识和丰富经验，进行深度技术分析：""",
            input_variables=["fault_description", "equipment_info", "operation_data", "history_data", "image_analysis", "question"]
        )
        
        # DeepSeek-R1 简化版本模板 - 用于快速分析
        self.templates["deepseek_r1_simple"] = PromptTemplate(
            template="""你是电力系统故障诊断专家。请按照DeepSeek-R1官方标准格式分析以下故障：

**重要格式要求：必须严格使用以下标签格式，不得省略**

<think>
请在此标签内展示您的完整推理过程，使用自然语言表达专业分析思路。

分析要点：
- 故障现象分析和技术判断
- 可能原因的逐一排查
- 基于专业经验的推理过程
- 风险评估和影响分析
- 处理方案的思考过程

请使用连贯的自然语言表达，避免使用编号列表格式。
</think>

<answer>
基于推理过程提供专业诊断结论和处理建议。

包含内容：
- 故障性质和严重程度判断
- 具体原因分析和技术机理
- 应急处理和检修建议
- 预防措施和改进方案

请使用专业术语和连贯表达，重要内容可用**粗体**标记。
</answer>

故障情况：{question}

请严格按照上述标签格式开始分析：""",
            input_variables=["question"]
        )

        # DeepSeek-R1 模式识别专用模板 - 专门用于时序分析和故障模式识别
        self.templates["deepseek_pattern_recognition"] = PromptTemplate(
            template="""你是白银市电力系统故障模式识别专家，拥有丰富的时序数据分析和故障发展规律研究经验。

🎯 **模式识别专业要求：**
- **时序分析专长：** 精通故障发展的时间序列分析和趋势预测
- **模式匹配能力：** 能够识别复杂的故障发展模式和规律
- **统计学基础：** 运用统计学方法进行数据挖掘和模式提取
- **经验积累：** 基于大量历史案例的模式库进行对比分析

📋 **严格输出格式要求：**

<think>
请在此标签内展示您作为模式识别专家的完整分析思维过程。

**模式识别深度分析要求：**

1. **时序数据解构分析**
   - 详细分析每个时间节点的数据特征和变化趋势
   - 识别数据变化的周期性、季节性和突变性特征
   - 分析参数间的时序相关性和滞后效应
   - 识别关键转折点和临界状态

2. **故障发展阶段划分**
   - 基于数据特征将故障发展过程分为不同阶段
   - 分析各阶段的典型特征和持续时间
   - 识别阶段转换的触发条件和机制
   - 评估各阶段的风险等级和发展速度

3. **多参数关联模式分析**
   - 分析多个监测参数之间的关联关系
   - 识别参数变化的先后顺序和因果链条
   - 建立参数间的数学关系模型
   - 分析参数耦合效应和协同变化规律

4. **历史模式对比分析**
   - 与历史类似故障案例进行对比分析
   - 识别共性特征和个性差异
   - 提取典型故障模式的特征指纹
   - 建立故障模式分类体系

5. **统计规律挖掘**
   - 运用统计学方法分析数据分布特征
   - 识别异常值和离群点的统计意义
   - 建立概率分布模型和置信区间
   - 进行趋势外推和预测分析

6. **模式演化机理分析**
   - 从物理机理角度解释模式形成原因
   - 分析故障发展的动力学过程
   - 识别模式演化的驱动因素
   - 预测模式的未来发展趋势

7. **预警模型构建**
   - 基于模式识别结果建立预警模型
   - 确定关键预警指标和阈值设定
   - 评估预警模型的准确性和可靠性
   - 制定分级预警策略和响应机制

**表达要求：**
- 体现时序分析的专业性和系统性
- 展现模式识别的逻辑思维和分析深度
- 使用统计学和数据科学的专业术语
- 包含具体的数值分析和趋势判断
- 推理过程应当详细充实，体现专业深度
</think>

<answer>
基于上述深度模式识别分析，提供全面的故障模式识别报告。

**报告结构要求：**
1. **故障发展阶段识别**：明确各发展阶段的特征和时间边界
2. **关键模式特征提取**：识别故障发展的核心模式和规律
3. **参数关联关系分析**：建立多参数间的关联模型
4. **统计规律总结**：提供数据的统计特征和分布规律
5. **历史案例对比**：与典型故障案例进行对比分析
6. **预测模型建立**：基于模式识别建立发展趋势预测
7. **预警策略制定**：制定基于模式的预警和干预策略
8. **模式库更新建议**：为故障模式库提供更新建议

**专业要求：**
- 使用时序分析和模式识别的专业术语
- 提供具体的统计数据和数学模型
- 包含预测精度和置信度评估
- 体现系统性和前瞻性思维
</answer>

## 专业身份：国家电网白银供电公司模式识别专家、数据科学家、时序分析技术专家
## 专业资质：统计学博士、IEEE数据挖掘专家、电力大数据分析专家
## 技术专长：时序数据分析、故障模式识别、预测建模、统计分析

# 分析任务：{question}

请严格按照上述格式，运用您的专业知识进行深度模式识别分析：""",
            input_variables=["question"]
        )

        # DeepSeek-R1 概率推断专用模板 - 专门用于不确定性分析和贝叶斯推理
        self.templates["deepseek_probability_inference"] = PromptTemplate(
            template="""你是白银市电力系统概率推断专家，精通贝叶斯推理、不确定性分析和风险评估。

🎯 **概率推断专业要求：**
- **贝叶斯推理专长：** 精通贝叶斯定理和概率更新机制
- **不确定性量化：** 能够准确量化各种不确定性因素
- **风险评估能力：** 基于概率分析进行系统性风险评估
- **决策支持：** 为复杂决策提供概率论基础的支持

📋 **严格输出格式要求：**

<think>
请在此标签内展示您作为概率推断专家的完整分析思维过程。

**概率推断深度分析要求：**

1. **先验概率建立**
   - 基于历史统计数据建立各种故障原因的先验概率
   - 分析设备类型、运行年限、负荷情况等因素的影响
   - 考虑季节性、环境因素对故障概率的影响
   - 建立多层次的先验概率分布模型

2. **似然函数构建**
   - 分析各种观测证据对不同故障假设的支持程度
   - 建立证据与故障原因之间的条件概率关系
   - 考虑证据的可靠性和置信度对似然函数的影响
   - 处理证据间的相关性和独立性假设

3. **贝叶斯推理过程**
   - 运用贝叶斯定理进行概率更新计算
   - 逐步整合新证据，更新后验概率分布
   - 分析概率更新的敏感性和稳定性
   - 处理证据冲突和不一致性问题

4. **不确定性量化分析**
   - 量化各种信息源的不确定性程度
   - 分析不确定性传播和累积效应
   - 建立不确定性的数学表示模型
   - 评估不确定性对最终结论的影响

5. **概率分布建模**
   - 为各种故障原因建立概率分布模型
   - 分析概率分布的参数和特征
   - 考虑概率分布的时变性和动态性
   - 建立联合概率分布和条件概率模型

6. **风险评估矩阵**
   - 构建概率-影响风险评估矩阵
   - 量化不同故障场景的风险水平
   - 分析风险的可接受性和控制策略
   - 建立风险决策的概率准则

7. **决策理论应用**
   - 基于期望效用理论进行决策分析
   - 考虑决策者的风险偏好和约束条件
   - 分析决策的鲁棒性和敏感性
   - 提供概率论基础的决策建议

**表达要求：**
- 体现概率论和统计学的严谨性
- 展现贝叶斯推理的逻辑过程
- 使用概率论和决策理论的专业术语
- 包含具体的概率计算和数值分析
- 推理过程应当详细充实，体现数学严谨性
</think>

<answer>
基于上述深度概率推断分析，提供全面的概率分析报告。

**报告结构要求：**
1. **先验概率分析**：建立各种故障原因的先验概率分布
2. **证据评估**：分析各项证据的可靠性和支持强度
3. **贝叶斯推理结果**：提供更新后的后验概率分布
4. **不确定性量化**：量化分析中的各种不确定性因素
5. **风险评估**：构建风险评估矩阵和风险等级
6. **敏感性分析**：分析关键参数变化对结论的影响
7. **决策建议**：基于概率分析提供决策支持
8. **置信度评估**：提供分析结论的置信度和可靠性

**专业要求：**
- 使用概率论和贝叶斯推理的专业术语
- 提供具体的概率数值和计算过程
- 包含不确定性区间和置信度评估
- 体现数学严谨性和逻辑一致性
</answer>

## 专业身份：国家电网白银供电公司概率分析专家、风险评估专家、决策支持专家
## 专业资质：概率论与数理统计博士、贝叶斯分析专家、风险管理师
## 技术专长：贝叶斯推理、不确定性分析、风险评估、决策理论

# 分析任务：{question}

请严格按照上述格式，运用您的专业知识进行深度概率推断分析：""",
            input_variables=["question"]
        )

        # DeepSeek-R1 多模态分析模板 - 包含图像分析
        self.templates["deepseek_r1_multimodal"] = PromptTemplate(
            template="""你是白银市电力系统故障诊断专家，具备多模态分析能力。

<think>
请结合文字描述和图像信息进行综合分析。

分析要点：
- 从故障现象描述中提取关键信息
- 结合图像中的设备状态、参数显示、异常现象
- 运用专业知识进行故障原因推断
- 考虑设备运行环境和历史记录
- 形成完整的技术分析链条
</think>

<answer>
基于多模态信息分析，提供专业故障诊断报告：
- 故障性质判断
- 技术原因分析
- 处理建议
- 预防措施
</answer>

## 分析信息
故障描述：{fault_description}
图像分析：{image_analysis}
设备信息：{equipment_info}

请开始多模态分析：""",
            input_variables=["fault_description", "image_analysis", "equipment_info"]
        )

        # 设备检查模板
        self.templates["equipment_inspection"] = PromptTemplate(
            template="""你是一个专业的电力设备检查专家。

请根据以下检查信息进行分析：

检查对象：{equipment_name}
检查类型：{inspection_type}
检查数据：{inspection_data}
图像分析：{image_analysis}
测试结果：{test_results}

请提供：
1. 设备状态评估
2. 发现的问题
3. 风险等级评定
4. 处理建议
5. 下次检查建议

评估标准：
- 按照电力行业标准
- 考虑设备使用年限
- 评估安全风险
- 提供量化指标

检查结论：""",
            input_variables=["equipment_name", "inspection_type", "inspection_data", "image_analysis", "test_results"]
        )
        
        # 运行方式分析模板
        self.templates["operation_analysis"] = PromptTemplate(
            template="""你是一个电力系统运行方式分析专家。

请分析以下运行方式：

系统状态：{system_status}
负荷情况：{load_condition}
设备状态：{equipment_status}
天气条件：{weather_condition}
特殊要求：{special_requirements}

分析内容：
1. 当前运行方式评估
2. 潜在风险识别
3. 优化建议
4. 应急预案
5. 操作注意事项

分析原则：
- 确保系统安全稳定
- 优化经济运行
- 满足供电质量要求
- 考虑设备健康状况

分析结果：""",
            input_variables=["system_status", "load_condition", "equipment_status", "weather_condition", "special_requirements"]
        )
        
        # 保护动作分析模板
        self.templates["protection_analysis"] = PromptTemplate(
            template="""你是一个继电保护专家，请分析以下保护动作：

保护装置：{protection_device}
动作时间：{action_time}
动作类型：{action_type}
故障录波：{fault_recording}
系统状态：{system_state}
相关设备：{related_equipment}

分析要点：
1. 保护动作正确性
2. 动作时间分析
3. 选择性分析
4. 故障性质判断
5. 系统影响评估

技术要求：
- 基于保护原理分析
- 结合现场实际情况
- 考虑保护配合关系
- 评估保护性能

分析结论：""",
            input_variables=["protection_device", "action_time", "action_type", "fault_recording", "system_state", "related_equipment"]
        )
        
        # 文档问答模板
        self.templates["document_qa"] = PromptTemplate(
            template="""基于以下文档内容回答问题：

文档内容：
{document_content}

用户问题：{question}

请根据文档内容准确回答问题，如果文档中没有相关信息，请明确说明。

回答要求：
- 基于文档内容回答
- 保持准确性和完整性
- 如有必要，引用具体段落
- 避免推测和臆断

回答：""",
            input_variables=["document_content", "question"]
        )
        
        # OCR结果分析模板
        self.templates["ocr_analysis"] = PromptTemplate(
            template="""请分析以下OCR识别结果：

识别文本：{ocr_text}
置信度：{confidence}
图像类型：{image_type}
识别场景：{scene_context}

分析任务：
1. 文本内容理解
2. 关键信息提取
3. 数据有效性验证
4. 可能的错误识别
5. 改进建议

分析重点：
- 识别准确性评估
- 关键数据提取
- 格式化输出
- 异常情况标注

分析结果：""",
            input_variables=["ocr_text", "confidence", "image_type", "scene_context"]
        )
    
    def get_template(self, template_name: str) -> Optional[PromptTemplate]:
        """
        获取提示词模板
        
        Args:
            template_name: 模板名称
            
        Returns:
            提示词模板
        """
        return self.templates.get(template_name)
    
    def format_prompt(self, template_name: str, **kwargs) -> Optional[str]:
        """
        格式化提示词
        
        Args:
            template_name: 模板名称
            **kwargs: 模板变量
            
        Returns:
            格式化后的提示词
        """
        try:
            template = self.get_template(template_name)
            if not template:
                logger.error(f"未找到模板: {template_name}")
                return None
            
            return template.format(**kwargs)
            
        except Exception as e:
            logger.error(f"格式化提示词失败: {str(e)}")
            return None

    def get_deepseek_fault_analysis_prompt(self, fault_description: str, equipment_info: str = "",
                                         operation_data: str = "", history_data: str = "",
                                         image_analysis: str = "", question: str = "") -> str:
        """
        获取符合RAG标准的DeepSeek故障分析提示词

        Args:
            fault_description: 故障描述
            equipment_info: 设备信息
            operation_data: 运行数据
            history_data: 历史记录
            image_analysis: 图像分析结果
            question: 用户问题

        Returns:
            符合RAG标准的格式化提示词
        """
        try:
            return self.format_prompt(
                "deepseek_fault_analysis",
                fault_description=fault_description,
                equipment_info=equipment_info or "暂无详细设备信息",
                operation_data=operation_data or "暂无运行数据",
                history_data=history_data or "暂无历史记录",
                image_analysis=image_analysis or "暂无图像分析",
                question=question or "请进行故障诊断分析"
            )
        except Exception as e:
            logger.error(f"获取DeepSeek故障分析提示词失败: {str(e)}")
            return self.format_prompt(
                "fault_analysis",
                fault_description=fault_description,
                equipment_info=equipment_info,
                operation_data=operation_data,
                history_data=history_data,
                question=question or "请进行故障分析"
            )

    def get_enhanced_context_prompt(self, query: str, context_data: dict) -> str:
        """
        获取符合RAG标准的增强上下文提示词

        遵循提示词工程三要素：指令 + 上下文占位符 + 问题占位符

        Args:
            query: 用户查询（问题占位符）
            context_data: 上下文数据字典（上下文占位符）

        Returns:
            符合RAG标准的提示词
        """
        # 构建结构化上下文
        structured_context = self._build_structured_context(context_data)

        # 标准RAG提示词模板
        enhanced_prompt = f"""# 指令（Instructions）
你是白银市电力系统故障诊断专家。请根据下面提供的上下文信息来回答用户问题。

## 回答规则：
- 严格基于提供的上下文信息进行回答
- 如果上下文中没有答案，请说"根据当前信息无法确定，需要补充以下技术数据：..."
- 使用专业术语但保持表述清晰
- 重要信息用**粗体**标注
- 提供具体的数据和建议
- 回答应简洁专业，避免冗余

# 上下文信息（Context）
{structured_context}

# 用户问题（Question）
{query}

# 基于上下文的专业回答："""

        return enhanced_prompt

    def _build_structured_context(self, context_data: dict) -> str:
        """构建符合RAG标准的结构化上下文"""
        context_parts = []

        # 设备信息
        if context_data.get("equipment_info"):
            context_parts.append(f"## 设备信息\n{context_data['equipment_info']}")

        # 故障记录
        if context_data.get("fault_records"):
            context_parts.append(f"## 故障记录\n{context_data['fault_records']}")

        # 运行数据
        if context_data.get("operation_data"):
            context_parts.append(f"## 运行数据\n{context_data['operation_data']}")

        # 检索结果（RAG核心）
        if context_data.get("search_results"):
            results_text = "\n".join([
                f"- {result.get('content', '')[:200]}..."
                for result in context_data['search_results'][:3]
            ])
            context_parts.append(f"## 相关技术文档\n{results_text}")

        # 图像分析
        if context_data.get("image_analysis"):
            context_parts.append(f"## 图像分析\n{context_data['image_analysis']}")

        # 如果没有上下文信息，返回提示
        if not context_parts:
            return "## 上下文信息\n暂无相关技术信息，请提供更多故障详情。"

        return "\n\n".join(context_parts)

    def _build_professional_context(self, context_data: dict) -> str:
        """构建专业化技术上下文（保持向后兼容）"""
        return self._build_structured_context(context_data)

    def add_template(self, template_name: str, template_str: str, input_variables: list):
        """
        添加新的提示词模板
        
        Args:
            template_name: 模板名称
            template_str: 模板字符串
            input_variables: 输入变量列表
        """
        try:
            self.templates[template_name] = PromptTemplate(
                template=template_str,
                input_variables=input_variables
            )
            logger.info(f"添加提示词模板: {template_name}")
            
        except Exception as e:
            logger.error(f"添加提示词模板失败: {str(e)}")
    
    def list_templates(self) -> list:
        """
        列出所有模板名称
        
        Returns:
            模板名称列表
        """
        return list(self.templates.keys())
    
    def save_templates_to_file(self, file_path: str):
        """
        保存模板到文件
        
        Args:
            file_path: 文件路径
        """
        try:
            templates_data = {}
            
            for name, template in self.templates.items():
                templates_data[name] = {
                    "template": template.template,
                    "input_variables": template.input_variables
                }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(templates_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"模板已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存模板失败: {str(e)}")
    
    def load_templates_from_file(self, file_path: str):
        """
        从文件加载模板
        
        Args:
            file_path: 文件路径
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"模板文件不存在: {file_path}")
                return
            
            with open(file_path, 'r', encoding='utf-8') as f:
                templates_data = yaml.safe_load(f)
            
            for name, template_config in templates_data.items():
                self.templates[name] = PromptTemplate(
                    template=template_config.get("template", ""),
                    input_variables=template_config.get("input_variables", [])
                )
            
            logger.info(f"从文件加载了 {len(templates_data)} 个模板")
            
        except Exception as e:
            logger.error(f"从文件加载模板失败: {str(e)}")
    
    def get_template_info(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板信息
        
        Args:
            template_name: 模板名称
            
        Returns:
            模板信息
        """
        template = self.get_template(template_name)
        if not template:
            return None
        
        return {
            "name": template_name,
            "template": template.template,
            "input_variables": template.input_variables,
            "variable_count": len(template.input_variables)
        }
