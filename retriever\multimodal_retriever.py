"""
多模态检索模块

支持文本和图像的联合检索
"""

import os
from typing import List, Dict, Any, Optional, Union
import numpy as np
from loguru import logger

from .text_retriever import TextRetriever
from data_processing.image_processor import ImageProcessor
from data_processing.ocr_processor import OCRProcessor


class MultimodalRetriever:
    """多模态检索器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.similarity_threshold = config.get("similarity_threshold", 0.7)
        self.top_k = config.get("top_k", 10)
        
        # 初始化各个处理器
        self.text_retriever = TextRetriever(config)
        self.image_processor = ImageProcessor(config.get("image", {}))
        self.ocr_processor = OCRProcessor(config.get("ocr", {}))
        
        # 图像索引和元数据
        self.image_metadata = []
        
        # 加载图像数据
        self._load_image_data()
    
    def _load_image_data(self):
        """加载图像数据"""
        try:
            images_path = self.config.get("images_path", "./knowledge_base/images")
            if os.path.exists(images_path):
                self._index_images(images_path)
            else:
                logger.info(f"图像目录不存在: {images_path}")
                
        except Exception as e:
            logger.error(f"加载图像数据失败: {str(e)}")
    
    def _index_images(self, images_dir: str):
        """索引图像目录"""
        try:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
            
            for root, dirs, files in os.walk(images_dir):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        image_path = os.path.join(root, file)
                        
                        # 处理图像
                        image_info = self.image_processor.process_image(image_path)
                        
                        # OCR提取文本
                        ocr_result = self.ocr_processor.extract_text(image_path)
                        
                        # 构建图像元数据
                        metadata = {
                            "path": image_path,
                            "filename": file,
                            "image_features": image_info.get("features", {}),
                            "ocr_text": ocr_result.get("full_text", ""),
                            "ocr_confidence": ocr_result.get("confidence", 0.0),
                            "text_blocks": ocr_result.get("text_blocks", []),
                            "metadata": image_info.get("metadata", {})
                        }
                        
                        self.image_metadata.append(metadata)
            
            logger.info(f"成功索引 {len(self.image_metadata)} 张图像")
            
        except Exception as e:
            logger.error(f"图像索引失败: {str(e)}")
    
    def search_text(self, query: str, top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        纯文本搜索
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            搜索结果
        """
        return self.text_retriever.search(query, top_k)
    
    def search_image_by_text(self, query: str, top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        通过文本搜索图像
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            图像搜索结果
        """
        try:
            top_k = top_k or self.top_k
            results = []
            
            # 对查询文本进行向量化
            query_vector = self.text_retriever.vector_processor.encode_single_text(query)
            
            if len(query_vector) == 0:
                return []
            
            # 搜索包含相关文本的图像
            for i, img_meta in enumerate(self.image_metadata):
                ocr_text = img_meta.get("ocr_text", "")
                
                if ocr_text.strip():
                    # 计算与OCR文本的相似度
                    ocr_vector = self.text_retriever.vector_processor.encode_single_text(ocr_text)
                    similarity = self.text_retriever.vector_processor.calculate_similarity(query_vector, ocr_vector)
                    
                    if similarity >= self.similarity_threshold:
                        result = {
                            "type": "image",
                            "rank": len(results) + 1,
                            "score": similarity,
                            "path": img_meta["path"],
                            "filename": img_meta["filename"],
                            "ocr_text": ocr_text,
                            "ocr_confidence": img_meta.get("ocr_confidence", 0.0),
                            "metadata": img_meta.get("metadata", {})
                        }
                        results.append(result)
            
            # 按相似度排序
            results.sort(key=lambda x: x["score"], reverse=True)
            
            logger.info(f"图像文本搜索完成，查询: '{query[:50]}...', 返回 {len(results[:top_k])} 个结果")
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"图像文本搜索失败: {str(e)}")
            return []
    
    def search_multimodal(self, query: str, include_text: bool = True, include_images: bool = True, 
                         top_k: Optional[int] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        多模态搜索
        
        Args:
            query: 查询文本
            include_text: 是否包含文本结果
            include_images: 是否包含图像结果
            top_k: 每种类型的返回结果数量
            
        Returns:
            分类的搜索结果
        """
        try:
            results = {
                "text": [],
                "images": [],
                "combined": []
            }
            
            top_k = top_k or self.top_k
            
            # 文本搜索
            if include_text:
                text_results = self.search_text(query, top_k)
                results["text"] = text_results
                
                # 添加到综合结果
                for result in text_results:
                    result["type"] = "text"
                    results["combined"].append(result)
            
            # 图像搜索
            if include_images:
                image_results = self.search_image_by_text(query, top_k)
                results["images"] = image_results
                
                # 添加到综合结果
                results["combined"].extend(image_results)
            
            # 对综合结果重新排序
            results["combined"].sort(key=lambda x: x["score"], reverse=True)
            
            logger.info(f"多模态搜索完成，文本结果: {len(results['text'])}, 图像结果: {len(results['images'])}")
            return results
            
        except Exception as e:
            logger.error(f"多模态搜索失败: {str(e)}")
            return {"text": [], "images": [], "combined": []}
    
    def search_by_image(self, image_path: str, top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        以图搜图（基于图像特征）
        
        Args:
            image_path: 查询图像路径
            top_k: 返回结果数量
            
        Returns:
            相似图像列表
        """
        try:
            top_k = top_k or self.top_k
            
            # 处理查询图像
            query_image_info = self.image_processor.process_image(image_path)
            query_features = query_image_info.get("features", {})
            
            if not query_features:
                logger.error("查询图像特征提取失败")
                return []
            
            results = []
            
            # 与索引中的图像进行比较
            for i, img_meta in enumerate(self.image_metadata):
                img_features = img_meta.get("image_features", {})
                
                if img_features:
                    # 计算特征相似度
                    similarity = self._calculate_image_similarity(query_features, img_features)
                    
                    if similarity >= self.similarity_threshold:
                        result = {
                            "type": "image_similarity",
                            "rank": len(results) + 1,
                            "score": similarity,
                            "path": img_meta["path"],
                            "filename": img_meta["filename"],
                            "metadata": img_meta.get("metadata", {})
                        }
                        results.append(result)
            
            # 按相似度排序
            results.sort(key=lambda x: x["score"], reverse=True)
            
            logger.info(f"以图搜图完成，返回 {len(results[:top_k])} 个结果")
            return results[:top_k]
            
        except Exception as e:
            logger.error(f"以图搜图失败: {str(e)}")
            return []
    
    def _calculate_image_similarity(self, features1: Dict[str, Any], features2: Dict[str, Any]) -> float:
        """
        计算图像特征相似度
        
        Args:
            features1: 图像1特征
            features2: 图像2特征
            
        Returns:
            相似度分数
        """
        try:
            total_similarity = 0.0
            weight_sum = 0.0
            
            # 颜色特征相似度
            if "color" in features1 and "color" in features2:
                color_sim = self._calculate_color_similarity(features1["color"], features2["color"])
                total_similarity += color_sim * 0.4
                weight_sum += 0.4
            
            # 纹理特征相似度
            if "texture" in features1 and "texture" in features2:
                texture_sim = self._calculate_texture_similarity(features1["texture"], features2["texture"])
                total_similarity += texture_sim * 0.3
                weight_sum += 0.3
            
            # 边缘特征相似度
            if "edge" in features1 and "edge" in features2:
                edge_sim = self._calculate_edge_similarity(features1["edge"], features2["edge"])
                total_similarity += edge_sim * 0.3
                weight_sum += 0.3
            
            return total_similarity / weight_sum if weight_sum > 0 else 0.0
            
        except Exception as e:
            logger.error(f"图像相似度计算失败: {str(e)}")
            return 0.0
    
    def _calculate_color_similarity(self, color1: Dict[str, Any], color2: Dict[str, Any]) -> float:
        """计算颜色特征相似度"""
        try:
            # 比较平均颜色
            mean1 = np.array(color1.get("mean_bgr", [0, 0, 0]))
            mean2 = np.array(color2.get("mean_bgr", [0, 0, 0]))
            
            # 计算欧氏距离并转换为相似度
            distance = np.linalg.norm(mean1 - mean2)
            similarity = 1.0 / (1.0 + distance / 255.0)
            
            return similarity
            
        except Exception as e:
            logger.error(f"颜色相似度计算失败: {str(e)}")
            return 0.0
    
    def _calculate_texture_similarity(self, texture1: Dict[str, Any], texture2: Dict[str, Any]) -> float:
        """计算纹理特征相似度"""
        try:
            # 比较纹理统计信息
            mean1 = texture1.get("mean", 0)
            mean2 = texture2.get("mean", 0)
            std1 = texture1.get("std", 0)
            std2 = texture2.get("std", 0)
            
            # 计算统计量的相似度
            mean_sim = 1.0 / (1.0 + abs(mean1 - mean2))
            std_sim = 1.0 / (1.0 + abs(std1 - std2))
            
            return (mean_sim + std_sim) / 2.0
            
        except Exception as e:
            logger.error(f"纹理相似度计算失败: {str(e)}")
            return 0.0
    
    def _calculate_edge_similarity(self, edge1: Dict[str, Any], edge2: Dict[str, Any]) -> float:
        """计算边缘特征相似度"""
        try:
            density1 = edge1.get("edge_density", 0)
            density2 = edge2.get("edge_density", 0)
            
            # 计算边缘密度相似度
            similarity = 1.0 / (1.0 + abs(density1 - density2))
            
            return similarity
            
        except Exception as e:
            logger.error(f"边缘相似度计算失败: {str(e)}")
            return 0.0
    
    def get_image_info(self, image_path: str) -> Optional[Dict[str, Any]]:
        """
        获取图像信息
        
        Args:
            image_path: 图像路径
            
        Returns:
            图像信息
        """
        try:
            for img_meta in self.image_metadata:
                if img_meta["path"] == image_path:
                    return img_meta
            return None
            
        except Exception as e:
            logger.error(f"获取图像信息失败: {str(e)}")
            return None
    
    def add_image(self, image_path: str, custom_metadata: Dict[str, Any] = None) -> bool:
        """
        添加新图像到索引

        Args:
            image_path: 图像路径
            custom_metadata: 自定义元数据

        Returns:
            是否成功
        """
        try:
            if not os.path.exists(image_path):
                logger.error(f"图像文件不存在: {image_path}")
                return False

            # 处理图像
            image_info = self.image_processor.process_image(image_path)

            # OCR提取文本
            ocr_result = self.ocr_processor.extract_text(image_path)

            # 构建基础元数据
            metadata = {
                "path": image_path,
                "filename": os.path.basename(image_path),
                "image_features": image_info.get("features", {}),
                "ocr_text": ocr_result.get("full_text", ""),
                "ocr_confidence": ocr_result.get("confidence", 0.0),
                "text_blocks": ocr_result.get("text_blocks", []),
                "metadata": image_info.get("metadata", {})
            }

            # 合并自定义元数据
            if custom_metadata:
                metadata.update(custom_metadata)
                # 特别处理一些字段
                if 'doc_id' in custom_metadata:
                    metadata['doc_id'] = custom_metadata['doc_id']
                if 'title' in custom_metadata:
                    metadata['title'] = custom_metadata['title']
                if 'equipment_type' in custom_metadata:
                    metadata['equipment_type'] = custom_metadata['equipment_type']
                if 'tags' in custom_metadata:
                    metadata['tags'] = custom_metadata['tags']

            self.image_metadata.append(metadata)

            logger.info(f"成功添加图像到索引: {image_path}")
            return True

        except Exception as e:
            logger.error(f"添加图像失败: {str(e)}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取检索器统计信息
        
        Returns:
            统计信息
        """
        try:
            text_stats = self.text_retriever.get_index_stats()
            
            stats = {
                "text_documents": text_stats.get("total_documents", 0),
                "images": len(self.image_metadata),
                "total_items": text_stats.get("total_documents", 0) + len(self.image_metadata),
                "text_stats": text_stats,
                "image_stats": {
                    "total_images": len(self.image_metadata),
                    "with_ocr_text": sum(1 for img in self.image_metadata if img.get("ocr_text", "").strip()),
                    "avg_ocr_confidence": np.mean([img.get("ocr_confidence", 0) for img in self.image_metadata]) if self.image_metadata else 0
                }
            }
            
            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {str(e)}")
            return {}
