#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据脱敏处理模块
符合国网数据安全要求，实现敏感信息脱敏和权限控制
"""

import re
import json
import hashlib
from typing import Dict, List, Any, Optional
from pathlib import Path
from loguru import logger
from datetime import datetime


class DataPrivacyProcessor:
    """数据脱敏处理器 - 符合国网安全标准"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # 敏感信息识别规则
        self.sensitive_patterns = {
            # 设备ID和编号
            "equipment_id": r'[A-Z]{2,4}\d{6,12}',  # 设备编号格式
            "station_id": r'\d{6}[A-Z]{2}\d{4}',    # 变电站编号
            
            # 人员信息
            "person_name": r'[\u4e00-\u9fa5]{2,4}(?=\s|$|，|。)',  # 中文姓名
            "phone_number": r'1[3-9]\d{9}',         # 手机号
            "id_card": r'\d{17}[\dXx]',             # 身份证号
            
            # 地理位置（具体地址）
            "detailed_address": r'[\u4e00-\u9fa5]+(?:市|县|区|镇|村|路|街|号)\d*号?',
            
            # IP地址和网络信息
            "ip_address": r'\b(?:\d{1,3}\.){3}\d{1,3}\b',
            "mac_address": r'([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})',
            
            # 系统账号
            "username": r'[a-zA-Z]\w{3,15}',        # 用户名格式
        }
        
        # 保留的技术术语（不脱敏）
        self.technical_terms = {
            '变压器', '断路器', '隔离开关', '电流互感器', '电压互感器',
            '避雷器', '电容器', '电抗器', '母线', '导线',
            '110kV', '220kV', '500kV', '35kV', '10kV',
            '差动保护', '距离保护', '过流保护', '零序保护',
            '短路', '接地', '过载', '绝缘', '跳闸', '合闸'
        }
        
        # 地区代码映射（保留地区信息但隐藏具体位置）
        self.region_mapping = {
            '白银': 'BY',
            '兰州': 'LZ', 
            '天水': 'TS',
            '酒泉': 'JQ',
            '张掖': 'ZY'
        }
        
        # 脱敏替换规则
        self.anonymization_rules = {
            "equipment_id": lambda x: f"EQ_{hashlib.md5(x.encode()).hexdigest()[:8].upper()}",
            "station_id": lambda x: f"ST_{hashlib.md5(x.encode()).hexdigest()[:8].upper()}",
            "person_name": lambda x: f"用户{hashlib.md5(x.encode()).hexdigest()[:4].upper()}",
            "phone_number": lambda x: f"138****{x[-4:]}",
            "id_card": lambda x: f"{x[:6]}********{x[-4:]}",
            "ip_address": lambda x: f"{'.'.join(x.split('.')[:2])}.***.***.***",
            "detailed_address": self._anonymize_address,
        }

    def _anonymize_address(self, address: str) -> str:
        """地址脱敏处理"""
        # 保留市级信息，隐藏详细地址
        for region, code in self.region_mapping.items():
            if region in address:
                return f"{region}市***区域"
        return "***地区"

    def anonymize_text(self, text: str) -> str:
        """
        对文本进行脱敏处理
        
        Args:
            text: 原始文本
            
        Returns:
            脱敏后的文本
        """
        try:
            if not text:
                return ""
            
            result = text
            
            # 应用脱敏规则
            for pattern_name, pattern in self.sensitive_patterns.items():
                if pattern_name in self.anonymization_rules:
                    anonymizer = self.anonymization_rules[pattern_name]
                    
                    def replace_func(match):
                        original = match.group(0)
                        # 检查是否为技术术语
                        if original in self.technical_terms:
                            return original
                        return anonymizer(original)
                    
                    result = re.sub(pattern, replace_func, result)
            
            logger.debug(f"文本脱敏完成，原长度: {len(text)}, 新长度: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"文本脱敏失败: {str(e)}")
            return text

    def anonymize_json_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        对JSON数据进行脱敏处理
        
        Args:
            data: 原始数据
            
        Returns:
            脱敏后的数据
        """
        try:
            if isinstance(data, dict):
                result = {}
                for key, value in data.items():
                    if isinstance(value, str):
                        result[key] = self.anonymize_text(value)
                    elif isinstance(value, (dict, list)):
                        result[key] = self.anonymize_json_data(value)
                    else:
                        result[key] = value
                return result
                
            elif isinstance(data, list):
                return [self.anonymize_json_data(item) for item in data]
                
            elif isinstance(data, str):
                return self.anonymize_text(data)
                
            else:
                return data
                
        except Exception as e:
            logger.error(f"JSON数据脱敏失败: {str(e)}")
            return data

    def process_file(self, input_path: str, output_path: str = None) -> bool:
        """
        处理文件脱敏
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            
        Returns:
            是否成功
        """
        try:
            input_path = Path(input_path)
            if not input_path.exists():
                logger.error(f"输入文件不存在: {input_path}")
                return False
            
            if output_path is None:
                output_path = input_path.parent / f"{input_path.stem}_anonymized{input_path.suffix}"
            
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 根据文件类型处理
            if input_path.suffix.lower() == '.json':
                with open(input_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                anonymized_data = self.anonymize_json_data(data)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(anonymized_data, f, ensure_ascii=False, indent=2)
                    
            elif input_path.suffix.lower() in ['.md', '.txt']:
                with open(input_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                anonymized_content = self.anonymize_text(content)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(anonymized_content)
            
            else:
                logger.warning(f"不支持的文件类型: {input_path.suffix}")
                return False
            
            logger.info(f"文件脱敏完成: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件脱敏失败: {str(e)}")
            return False

    def batch_process_directory(self, input_dir: str, output_dir: str = None) -> int:
        """
        批量处理目录下的文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            
        Returns:
            处理成功的文件数量
        """
        try:
            input_dir = Path(input_dir)
            if not input_dir.exists():
                logger.error(f"输入目录不存在: {input_dir}")
                return 0
            
            if output_dir is None:
                output_dir = input_dir.parent / f"{input_dir.name}_anonymized"
            
            output_dir = Path(output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)
            
            processed_count = 0
            supported_extensions = ['.json', '.md', '.txt']
            
            for file_path in input_dir.rglob("*"):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    # 保持相对路径结构
                    relative_path = file_path.relative_to(input_dir)
                    output_path = output_dir / relative_path
                    
                    if self.process_file(str(file_path), str(output_path)):
                        processed_count += 1
            
            logger.info(f"批量脱敏处理完成，共处理 {processed_count} 个文件")
            return processed_count
            
        except Exception as e:
            logger.error(f"批量脱敏处理失败: {str(e)}")
            return 0

    def validate_anonymization(self, original_text: str, anonymized_text: str) -> Dict[str, Any]:
        """
        验证脱敏效果
        
        Args:
            original_text: 原始文本
            anonymized_text: 脱敏后文本
            
        Returns:
            验证结果
        """
        try:
            validation_result = {
                "is_valid": True,
                "issues": [],
                "statistics": {
                    "original_length": len(original_text),
                    "anonymized_length": len(anonymized_text),
                    "reduction_ratio": 1.0 - len(anonymized_text) / len(original_text) if len(original_text) > 0 else 0
                }
            }
            
            # 检查是否还有敏感信息
            for pattern_name, pattern in self.sensitive_patterns.items():
                matches = re.findall(pattern, anonymized_text)
                if matches:
                    # 过滤掉技术术语
                    sensitive_matches = [m for m in matches if m not in self.technical_terms]
                    if sensitive_matches:
                        validation_result["is_valid"] = False
                        validation_result["issues"].append({
                            "type": pattern_name,
                            "matches": sensitive_matches
                        })
            
            return validation_result
            
        except Exception as e:
            logger.error(f"脱敏验证失败: {str(e)}")
            return {"is_valid": False, "error": str(e)}


def main():
    """测试函数"""
    processor = DataPrivacyProcessor()
    
    # 测试文本脱敏
    test_text = """
    白银供电公司110kV变电站发生故障，设备编号BYT123456789，
    值班员张三（手机：13812345678）立即启动应急预案。
    故障位置：白银市白银区工业园区35号，IP地址：*************。
    """
    
    print("原始文本:")
    print(test_text)
    print("\n脱敏后:")
    print(processor.anonymize_text(test_text))


if __name__ == "__main__":
    main()
