#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Chroma向量数据库管理器
专门用于管理白银电力系统的向量数据库
使用现代化的ChromaDB API
"""

from typing import Dict, List, Any, Optional
from loguru import logger

try:
    from .modern_chroma_manager import ModernChromaManager, get_modern_chroma_manager
    CHROMA_AVAILABLE = True
except ImportError:
    try:
        from modern_chroma_manager import ModernChromaManager, get_modern_chroma_manager
        CHROMA_AVAILABLE = True
    except ImportError:
        CHROMA_AVAILABLE = False
        logger.error("Chroma未安装，请运行: pip install chromadb")


class ChromaManager:
    """Chroma向量数据库管理器 - 白银电力系统专用（现代化版本）"""
    
    def __init__(self, config: Dict[str, Any] = None):
        if not CHROMA_AVAILABLE:
            raise ImportError("Chroma未安装，无法使用ChromaManager")
        
        # 使用现代化的Chroma管理器
        try:
            self.modern_manager = get_modern_chroma_manager()
            logger.info("✅ 使用现代化Chroma管理器")
        except Exception as e:
            logger.error(f"❌ 现代化Chroma管理器初始化失败: {e}")
            self.modern_manager = None
        
        self.config = config or {}
        
        # 为了兼容性，保留一些属性
        self.persist_directory = self.config.get("persist_directory", "./embeddings/chroma_store")
        self.collection_name = self.config.get("collection_name", "baiyin_power_fault_collection")

    def is_available(self) -> bool:
        """检查Chroma是否可用"""
        return self.modern_manager and self.modern_manager.is_available()
    
    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到集合"""
        if not self.modern_manager:
            logger.warning("现代化管理器不可用")
            return False
        return self.modern_manager.add_documents(documents)
    
    def search(self, query: str, n_results: int = 5, where: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """搜索文档"""
        if not self.modern_manager:
            logger.warning("现代化管理器不可用")
            return []
        return self.modern_manager.search(query, n_results, where)
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        if not self.modern_manager:
            return {"available": False, "error": "现代化管理器不可用"}
        return self.modern_manager.get_collection_info()
    
    # 为了兼容性，保留旧的方法名但委托给现代化管理器
    def add_documents_batch(self, documents: List[Dict[str, Any]]) -> bool:
        """批量添加文档（兼容性方法）"""
        return self.add_documents(documents)
    
    def search_documents(self, query: str, n_results: int = 5, where: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """搜索文档（兼容性方法）"""
        return self.search(query, n_results, where)
    
    def get_status(self) -> Dict[str, Any]:
        """获取状态（兼容性方法）"""
        return self.get_collection_info()


# 创建全局实例
chroma_manager = None

def get_chroma_manager(config: Dict[str, Any] = None) -> ChromaManager:
    """获取Chroma管理器实例"""
    global chroma_manager
    if chroma_manager is None:
        chroma_manager = ChromaManager(config)
    return chroma_manager


# 测试函数
def test_chroma_manager():
    """测试Chroma管理器"""
    try:
        manager = get_chroma_manager()
        print(f"🧪 测试Chroma管理器...")
        print(f"📊 可用性: {manager.is_available()}")

        if manager.is_available():
            # 测试添加文档
            test_docs = [
                {
                    "id": "test_chroma_1",
                    "content": "这是一个Chroma测试文档",
                    "metadata": {"source": "test", "type": "chroma_test"}
                }
            ]

            success = manager.add_documents(test_docs)
            print(f"📝 添加文档: {'✅ 成功' if success else '❌ 失败'}")

            # 测试搜索
            results = manager.search("测试", n_results=1)
            print(f"🔍 搜索结果: {len(results)} 个")

            # 获取集合信息
            info = manager.get_collection_info()
            print(f"📊 集合信息: {info}")

        print("🧪 测试完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_chroma_manager()
