#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强多模态检索器 - 修复图像检索问题，提升多模态检索性能
解决图像编码、OCR识别、特征提取等关键问题
"""

import os
import sys
import cv2
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import hashlib
import json
import shutil

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from PIL import Image, ImageEnhance
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    logging.warning("PIL不可用，图像处理功能受限")

try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logging.warning("jieba不可用，中文分词功能受限")


@dataclass
class ImageSearchResult:
    """图像搜索结果"""
    id: str
    path: str
    filename: str
    score: float
    match_type: str  # text_match, visual_match, metadata_match
    matched_content: str
    confidence: float
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "path": self.path,
            "filename": self.filename,
            "score": self.score,
            "match_type": self.match_type,
            "matched_content": self.matched_content,
            "confidence": self.confidence,
            "metadata": self.metadata,
            "type": "image"
        }


class EnhancedMultimodalRetriever:
    """增强多模态检索器"""
    
    def __init__(self, images_dir: str = "knowledge_base/images"):
        """初始化增强多模态检索器"""
        self.logger = logging.getLogger(__name__)
        self.images_dir = Path(images_dir)
        
        # 图像索引缓存
        self.image_index = {}
        self.index_file = Path("cache/image_index.json")
        
        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # 电力设备关键词库
        self.power_equipment_keywords = {
            "变压器": ["transformer", "主变", "配变", "所变", "变压器"],
            "断路器": ["breaker", "开关", "断路器", "CB"],
            "隔离开关": ["isolator", "隔离", "刀闸", "DS"],
            "互感器": ["CT", "PT", "电流互感器", "电压互感器"],
            "避雷器": ["arrester", "避雷器", "氧化锌"],
            "绝缘子": ["insulator", "绝缘子", "瓷瓶"],
            "导线": ["conductor", "导线", "电缆"],
            "母线": ["busbar", "母线", "汇流排"],
            "套管": ["bushing", "套管", "穿墙套管"],
            "油箱": ["tank", "油箱", "储油柜"]
        }
        
        # 故障类型关键词
        self.fault_keywords = {
            "过热": ["thermal", "热", "温度", "发热", "过热"],
            "放电": ["discharge", "放电", "电弧", "闪络"],
            "漏油": ["oil leak", "漏油", "渗油", "油位"],
            "污闪": ["pollution", "污闪", "污秽", "闪络"],
            "断股": ["broken", "断股", "断线", "损坏"],
            "腐蚀": ["corrosion", "腐蚀", "锈蚀", "老化"]
        }
        
        # 初始化
        self._ensure_cache_dir()
        self._load_image_index()
        
        self.logger.info("增强多模态检索器初始化完成")
    
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        self.index_file.parent.mkdir(parents=True, exist_ok=True)
    
    def _load_image_index(self):
        """加载图像索引"""
        try:
            if self.index_file.exists():
                with open(self.index_file, 'r', encoding='utf-8') as f:
                    self.image_index = json.load(f)
                self.logger.info(f"加载图像索引: {len(self.image_index)} 张图像")
            else:
                self.logger.info("图像索引文件不存在，将重新构建")
                self._build_image_index()
        except Exception as e:
            self.logger.error(f"加载图像索引失败: {e}")
            self._build_image_index()
    
    def _save_image_index(self):
        """保存图像索引"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(self.image_index, f, ensure_ascii=False, indent=2)
            self.logger.info(f"保存图像索引: {len(self.image_index)} 张图像")
        except Exception as e:
            self.logger.error(f"保存图像索引失败: {e}")
    
    def _build_image_index(self):
        """构建图像索引"""
        self.logger.info("开始构建图像索引...")
        
        if not self.images_dir.exists():
            self.logger.warning(f"图像目录不存在: {self.images_dir}")
            return
        
        self.image_index = {}
        processed_count = 0
        
        # 遍历所有图像文件
        for image_path in self._find_image_files():
            try:
                # 生成唯一ID
                image_id = self._generate_image_id(image_path)
                
                # 处理图像
                image_info = self._process_single_image(image_path)
                
                if image_info:
                    self.image_index[image_id] = image_info
                    processed_count += 1
                    
                    if processed_count % 10 == 0:
                        self.logger.info(f"已处理 {processed_count} 张图像")
                        
            except Exception as e:
                self.logger.error(f"处理图像失败 {image_path}: {e}")
        
        # 保存索引
        self._save_image_index()
        self.logger.info(f"图像索引构建完成: {processed_count} 张图像")
    
    def _find_image_files(self) -> List[Path]:
        """查找所有图像文件"""
        image_files = []
        
        for root, dirs, files in os.walk(self.images_dir):
            for file in files:
                file_path = Path(root) / file
                if file_path.suffix.lower() in self.supported_formats:
                    image_files.append(file_path)
        
        return image_files
    
    def _generate_image_id(self, image_path: Path) -> str:
        """生成图像唯一ID"""
        # 使用相对路径和文件大小生成ID
        relative_path = image_path.relative_to(self.images_dir)
        try:
            file_size = image_path.stat().st_size
            content = f"{relative_path}_{file_size}"
        except:
            content = str(relative_path)
        
        return hashlib.md5(content.encode()).hexdigest()
    
    def _process_single_image(self, image_path: Path) -> Optional[Dict[str, Any]]:
        """处理单张图像"""
        try:
            # 基本信息
            relative_path = image_path.relative_to(self.images_dir)
            filename = image_path.name
            
            # 从文件名提取信息
            filename_info = self._extract_filename_info(filename)
            
            # 尝试读取图像（解决编码问题）
            image_data = self._safe_read_image(image_path)
            
            # 构建图像信息
            image_info = {
                "path": str(relative_path),
                "filename": filename,
                "absolute_path": str(image_path),
                "size": image_path.stat().st_size if image_path.exists() else 0,
                "equipment_type": filename_info.get("equipment_type", ""),
                "fault_type": filename_info.get("fault_type", ""),
                "keywords": filename_info.get("keywords", []),
                "category": self._categorize_image(relative_path),
                "readable": image_data is not None,
                "features": {}
            }
            
            # 如果图像可读，提取特征
            if image_data is not None:
                features = self._extract_basic_features(image_data)
                image_info["features"] = features
            
            return image_info
            
        except Exception as e:
            self.logger.error(f"处理图像失败 {image_path}: {e}")
            return None
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            "total_images": len(self.image_index),
            "readable_images": sum(1 for img in self.image_index.values() if img.get("readable", False)),
            "categories": self._get_category_stats(),
            "equipment_types": self._get_equipment_stats(),
            "index_file_exists": self.index_file.exists(),
            "images_dir_exists": self.images_dir.exists()
        }

    def _safe_read_image(self, image_path: Path) -> Optional[np.ndarray]:
        """安全读取图像（解决中文路径问题）"""
        try:
            # 方法1: 使用PIL读取
            if PIL_AVAILABLE:
                with Image.open(image_path) as img:
                    # 转换为RGB
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    # 转换为numpy数组
                    img_array = np.array(img)
                    # 转换为BGR格式（OpenCV格式）
                    return cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # 方法2: 使用numpy读取字节流
            with open(image_path, 'rb') as f:
                image_bytes = f.read()

            # 将字节转换为numpy数组
            nparr = np.frombuffer(image_bytes, np.uint8)

            # 使用OpenCV解码
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            return image

        except Exception as e:
            self.logger.warning(f"无法读取图像 {image_path}: {e}")
            return None

    def _extract_filename_info(self, filename: str) -> Dict[str, Any]:
        """从文件名提取信息"""
        filename_lower = filename.lower()
        info = {
            "equipment_type": "",
            "fault_type": "",
            "keywords": []
        }

        # 检测设备类型
        for equipment, keywords in self.power_equipment_keywords.items():
            for keyword in keywords:
                if keyword.lower() in filename_lower:
                    info["equipment_type"] = equipment
                    info["keywords"].append(keyword)
                    break

        # 检测故障类型
        for fault, keywords in self.fault_keywords.items():
            for keyword in keywords:
                if keyword.lower() in filename_lower:
                    info["fault_type"] = fault
                    info["keywords"].append(keyword)
                    break

        return info

    def _categorize_image(self, relative_path: Path) -> str:
        """根据路径分类图像"""
        path_str = str(relative_path).lower()

        if "fault" in path_str or "故障" in path_str:
            return "故障案例"
        elif "equipment" in path_str or "设备" in path_str:
            return "设备图片"
        elif "inspection" in path_str or "巡检" in path_str:
            return "巡检记录"
        elif "maintenance" in path_str or "检修" in path_str:
            return "检修记录"
        else:
            return "其他"

    def _extract_basic_features(self, image: np.ndarray) -> Dict[str, Any]:
        """提取基本图像特征"""
        try:
            features = {}

            # 图像尺寸
            height, width = image.shape[:2]
            features["dimensions"] = {"width": width, "height": height}

            # 颜色特征
            features["color"] = self._extract_color_features(image)

            # 亮度特征
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            features["brightness"] = {
                "mean": float(np.mean(gray)),
                "std": float(np.std(gray))
            }

            # 边缘密度
            edges = cv2.Canny(gray, 50, 150)
            edge_density = np.sum(edges > 0) / (width * height)
            features["edge_density"] = float(edge_density)

            return features

        except Exception as e:
            self.logger.error(f"特征提取失败: {e}")
            return {}

    def _extract_color_features(self, image: np.ndarray) -> Dict[str, Any]:
        """提取颜色特征"""
        try:
            # 计算颜色直方图
            hist_b = cv2.calcHist([image], [0], None, [256], [0, 256])
            hist_g = cv2.calcHist([image], [1], None, [256], [0, 256])
            hist_r = cv2.calcHist([image], [2], None, [256], [0, 256])

            # 计算主要颜色
            mean_color = np.mean(image, axis=(0, 1))

            return {
                "mean_bgr": [float(mean_color[0]), float(mean_color[1]), float(mean_color[2])],
                "dominant_color": self._get_dominant_color(image)
            }

        except Exception as e:
            self.logger.error(f"颜色特征提取失败: {e}")
            return {}

    def _get_dominant_color(self, image: np.ndarray) -> str:
        """获取主要颜色"""
        try:
            # 简化的主要颜色检测
            mean_color = np.mean(image, axis=(0, 1))
            b, g, r = mean_color

            if r > g and r > b:
                return "红色系"
            elif g > r and g > b:
                return "绿色系"
            elif b > r and b > g:
                return "蓝色系"
            elif r > 200 and g > 200 and b > 200:
                return "白色系"
            elif r < 50 and g < 50 and b < 50:
                return "黑色系"
            else:
                return "混合色"

        except:
            return "未知"

    def search_images(self, query: str, limit: int = 10) -> List[ImageSearchResult]:
        """搜索图像"""
        if not self.image_index:
            self.logger.warning("图像索引为空")
            return []

        results = []
        query_lower = query.lower()

        # 分词处理
        if JIEBA_AVAILABLE:
            query_words = jieba.lcut(query_lower)
        else:
            query_words = query_lower.split()

        # 搜索每张图像
        for image_id, image_info in self.image_index.items():
            score = self._calculate_image_relevance(query_lower, query_words, image_info)

            if score > 0.1:  # 最低相关性阈值
                match_info = self._get_match_info(query_lower, query_words, image_info)

                result = ImageSearchResult(
                    id=image_id,
                    path=image_info["path"],
                    filename=image_info["filename"],
                    score=score,
                    match_type=match_info["type"],
                    matched_content=match_info["content"],
                    confidence=match_info["confidence"],
                    metadata=image_info
                )
                results.append(result)

        # 按分数排序
        results.sort(key=lambda x: x.score, reverse=True)

        return results[:limit]

    def _calculate_image_relevance(self, query: str, query_words: List[str], image_info: Dict[str, Any]) -> float:
        """计算图像相关性分数"""
        score = 0.0

        # 文件名匹配（权重最高）
        filename = image_info.get("filename", "").lower()
        for word in query_words:
            if word in filename:
                score += 0.4

        # 设备类型匹配
        equipment_type = image_info.get("equipment_type", "").lower()
        if equipment_type and equipment_type in query:
            score += 0.3

        # 故障类型匹配
        fault_type = image_info.get("fault_type", "").lower()
        if fault_type and fault_type in query:
            score += 0.3

        # 关键词匹配
        keywords = image_info.get("keywords", [])
        for keyword in keywords:
            if keyword.lower() in query:
                score += 0.2

        # 分类匹配
        category = image_info.get("category", "").lower()
        if category and any(word in category for word in query_words):
            score += 0.1

        # 路径匹配
        path = image_info.get("path", "").lower()
        for word in query_words:
            if word in path:
                score += 0.1

        return min(1.0, score)  # 限制最大分数为1.0

    def _get_match_info(self, query: str, query_words: List[str], image_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取匹配信息"""
        match_info = {
            "type": "metadata_match",
            "content": "",
            "confidence": 0.5
        }

        # 检查文件名匹配
        filename = image_info.get("filename", "").lower()
        filename_matches = [word for word in query_words if word in filename]
        if filename_matches:
            match_info["type"] = "filename_match"
            match_info["content"] = f"文件名包含: {', '.join(filename_matches)}"
            match_info["confidence"] = 0.9
            return match_info

        # 检查设备类型匹配
        equipment_type = image_info.get("equipment_type", "")
        if equipment_type and equipment_type.lower() in query:
            match_info["type"] = "equipment_match"
            match_info["content"] = f"设备类型: {equipment_type}"
            match_info["confidence"] = 0.8
            return match_info

        # 检查故障类型匹配
        fault_type = image_info.get("fault_type", "")
        if fault_type and fault_type.lower() in query:
            match_info["type"] = "fault_match"
            match_info["content"] = f"故障类型: {fault_type}"
            match_info["confidence"] = 0.8
            return match_info

        # 检查关键词匹配
        keywords = image_info.get("keywords", [])
        matched_keywords = [kw for kw in keywords if kw.lower() in query]
        if matched_keywords:
            match_info["type"] = "keyword_match"
            match_info["content"] = f"关键词: {', '.join(matched_keywords)}"
            match_info["confidence"] = 0.7
            return match_info

        # 默认路径匹配
        match_info["content"] = f"路径匹配: {image_info.get('category', '未分类')}"
        return match_info

    def _get_category_stats(self) -> Dict[str, int]:
        """获取分类统计"""
        stats = {}
        for image_info in self.image_index.values():
            category = image_info.get("category", "未分类")
            stats[category] = stats.get(category, 0) + 1
        return stats

    def _get_equipment_stats(self) -> Dict[str, int]:
        """获取设备类型统计"""
        stats = {}
        for image_info in self.image_index.values():
            equipment = image_info.get("equipment_type", "未知设备")
            if equipment:
                stats[equipment] = stats.get(equipment, 0) + 1
        return stats

    def rebuild_index(self) -> bool:
        """重建图像索引"""
        try:
            self.logger.info("开始重建图像索引...")
            self._build_image_index()
            return True
        except Exception as e:
            self.logger.error(f"重建索引失败: {e}")
            return False

    def add_image(self, image_path: str) -> bool:
        """添加单张图像到索引"""
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                self.logger.error(f"图像文件不存在: {image_path}")
                return False

            # 生成ID
            image_id = self._generate_image_id(image_path)

            # 处理图像
            image_info = self._process_single_image(image_path)

            if image_info:
                self.image_index[image_id] = image_info
                self._save_image_index()
                self.logger.info(f"成功添加图像: {image_path}")
                return True
            else:
                self.logger.error(f"处理图像失败: {image_path}")
                return False

        except Exception as e:
            self.logger.error(f"添加图像失败: {e}")
            return False

    def get_image_info(self, image_id: str) -> Optional[Dict[str, Any]]:
        """获取图像信息"""
        return self.image_index.get(image_id)

    def search_by_category(self, category: str, limit: int = 10) -> List[ImageSearchResult]:
        """按分类搜索图像"""
        results = []

        for image_id, image_info in self.image_index.items():
            if image_info.get("category", "").lower() == category.lower():
                result = ImageSearchResult(
                    id=image_id,
                    path=image_info["path"],
                    filename=image_info["filename"],
                    score=1.0,
                    match_type="category_match",
                    matched_content=f"分类: {category}",
                    confidence=1.0,
                    metadata=image_info
                )
                results.append(result)

        return results[:limit]

    def search_by_equipment(self, equipment_type: str, limit: int = 10) -> List[ImageSearchResult]:
        """按设备类型搜索图像"""
        results = []

        for image_id, image_info in self.image_index.items():
            if image_info.get("equipment_type", "").lower() == equipment_type.lower():
                result = ImageSearchResult(
                    id=image_id,
                    path=image_info["path"],
                    filename=image_info["filename"],
                    score=1.0,
                    match_type="equipment_match",
                    matched_content=f"设备: {equipment_type}",
                    confidence=1.0,
                    metadata=image_info
                )
                results.append(result)

        return results[:limit]


# 全局实例
_enhanced_multimodal_retriever = None

def get_enhanced_multimodal_retriever() -> EnhancedMultimodalRetriever:
    """获取增强多模态检索器实例（单例模式）"""
    global _enhanced_multimodal_retriever
    if _enhanced_multimodal_retriever is None:
        _enhanced_multimodal_retriever = EnhancedMultimodalRetriever()
    return _enhanced_multimodal_retriever
