"""
DeepSeek LLM集成模块
为LangChain提供DeepSeek模型支持
"""

from typing import Any, Dict, List, Optional, Union
from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun
from pydantic import Field
import requests
import json
from loguru import logger


class DeepSeek(LLM):
    """DeepSeek LLM包装器，兼容LangChain"""
    
    api_key: str = Field(..., description="DeepSeek API密钥")
    base_url: str = Field(default="https://api.deepseek.com/v1", description="API基础URL")
    model: str = Field(default="deepseek-chat", description="模型名称")
    temperature: float = Field(default=0.7, description="温度参数")
    max_tokens: int = Field(default=4096, description="最大token数")
    
    @property
    def _llm_type(self) -> str:
        """返回LLM类型"""
        return "deepseek"
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """调用DeepSeek API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "stream": False
            }
            
            # 添加stop参数
            if stop:
                data["stop"] = stop
            
            # 合并额外参数
            data.update(kwargs)
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    logger.error(f"DeepSeek API响应格式错误: {result}")
                    return "API响应格式错误"
            else:
                logger.error(f"DeepSeek API调用失败: {response.status_code} - {response.text}")
                return f"API调用失败: {response.status_code}"
                
        except Exception as e:
            logger.error(f"DeepSeek LLM调用异常: {e}")
            return f"调用异常: {str(e)}"
    
    @property
    def _identifying_params(self) -> Dict[str, Any]:
        """返回识别参数"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "base_url": self.base_url
        }


class DeepSeekR1(DeepSeek):
    """DeepSeek R1推理模型"""
    
    model: str = Field(default="deepseek-reasoner", description="R1推理模型")
    
    def _call(
        self,
        prompt: str,
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> str:
        """调用DeepSeek R1 API，支持推理模式"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": self.temperature,
                "max_tokens": self.max_tokens,
                "stream": False,
                "reasoning": True  # 启用推理模式
            }
            
            # 添加stop参数
            if stop:
                data["stop"] = stop
            
            # 合并额外参数
            data.update(kwargs)
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=120  # R1模型可能需要更长时间
            )
            
            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    choice = result["choices"][0]
                    message = choice["message"]
                    
                    # 处理推理过程
                    if "reasoning_content" in message:
                        reasoning = message["reasoning_content"]
                        content = message["content"]
                        return f"<thinking>\n{reasoning}\n</thinking>\n\n<answer>\n{content}\n</answer>"
                    else:
                        return message["content"]
                else:
                    logger.error(f"DeepSeek R1 API响应格式错误: {result}")
                    return "API响应格式错误"
            else:
                logger.error(f"DeepSeek R1 API调用失败: {response.status_code} - {response.text}")
                return f"API调用失败: {response.status_code}"
                
        except Exception as e:
            logger.error(f"DeepSeek R1 LLM调用异常: {e}")
            return f"调用异常: {str(e)}"


# 为了兼容性，创建别名
DeepSeekChat = DeepSeek
DeepSeekReasoner = DeepSeekR1


def create_deepseek_llm(
    api_key: str,
    model: str = "deepseek-chat",
    base_url: str = "https://api.deepseek.com/v1",
    **kwargs
) -> DeepSeek:
    """创建DeepSeek LLM实例的便捷函数"""
    if "reasoner" in model.lower() or "r1" in model.lower():
        return DeepSeekR1(
            api_key=api_key,
            model=model,
            base_url=base_url,
            **kwargs
        )
    else:
        return DeepSeek(
            api_key=api_key,
            model=model,
            base_url=base_url,
            **kwargs
        )


# 测试函数
def test_deepseek_llm():
    """测试DeepSeek LLM"""
    try:
        # 这里需要实际的API密钥
        llm = DeepSeek(
            api_key="test_key",
            model="deepseek-chat"
        )
        
        response = llm("你好，请简单介绍一下你自己。")
        print(f"DeepSeek响应: {response}")
        
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    test_deepseek_llm()
