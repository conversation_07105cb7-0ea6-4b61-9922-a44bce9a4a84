"""
知识库API路由
"""

from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException, Depends, Query, UploadFile, File, BackgroundTasks, Request
from loguru import logger
import os
import shutil
import json
from datetime import datetime

from ..models import (
    KnowledgeSearchRequest,
    KnowledgeSearchResponse,
    DocumentQARequest,
    DocumentQAResponse,
    BaseResponse
)

# 异步处理相关导入
try:
    from core.async_task_manager import get_task_manager, TaskStatus
    from core.async_file_processor import get_file_processor
    ASYNC_PROCESSING_AVAILABLE = True
except ImportError as e:
    logger.warning(f"异步处理模块导入失败: {e}")
    ASYNC_PROCESSING_AVAILABLE = False

# 安全中间件导入
try:
    from core.security_middleware import get_security_middleware
    SECURITY_MIDDLEWARE_AVAILABLE = True
except ImportError as e:
    logger.warning(f"安全中间件导入失败: {e}")
    SECURITY_MIDDLEWARE_AVAILABLE = False


router = APIRouter()


@router.get("/csrf-token", response_model=BaseResponse)
async def get_csrf_token(request: Request):
    """
    获取CSRF token
    """
    try:
        if not SECURITY_MIDDLEWARE_AVAILABLE:
            return BaseResponse(
                success=True,
                message="CSRF保护未启用",
                data={'csrf_token': None}
            )

        security = get_security_middleware()
        session_id = security.get_client_id(request)
        csrf_token = security.generate_csrf_token(session_id)

        return BaseResponse(
            success=True,
            message="CSRF token生成成功",
            data={'csrf_token': csrf_token}
        )

    except Exception as e:
        logger.error(f"生成CSRF token失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"生成CSRF token失败: {str(e)}")


# ==================== 分步骤处理API ====================

@router.post("/files/upload", response_model=BaseResponse)
async def upload_file_step1(
    request: Request,
    file: UploadFile = File(...),
    file_type: str = Query(..., description="文件类型: document 或 image")
):
    """
    步骤1: 文件上传 - 临时存储文件
    """
    try:
        # 安全检查
        if SECURITY_MIDDLEWARE_AVAILABLE:
            security = get_security_middleware()
            await security.check_rate_limit(request)

        logger.info(f"步骤1 - 上传文件: {file.filename} (类型: {file_type})")

        # 使用安全文件上传器
        from core.security_utils import validate_and_save_file
        file_info = validate_and_save_file(file, file_type, 'temp')

        # 生成临时会话ID
        import uuid
        session_id = str(uuid.uuid4())

        return BaseResponse(
            success=True,
            message=f"文件上传成功，准备进行标注",
            data={
                'session_id': session_id,
                'file_info': file_info,
                'next_step': 'annotation',
                'step': 1
            }
        )

    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@router.post("/files/annotate", response_model=BaseResponse)
async def annotate_file_step2(
    request: Request,
    session_id: str = Query(..., description="会话ID"),
    annotations: str = Query(..., description="标注数据 (JSON格式)")
):
    """
    步骤2: 数据标注 - 添加标注信息
    """
    try:
        # 安全检查
        if SECURITY_MIDDLEWARE_AVAILABLE:
            security = get_security_middleware()
            await security.check_rate_limit(request)

        logger.info(f"步骤2 - 数据标注: {session_id}")

        # 解析标注数据
        try:
            annotation_data = json.loads(annotations)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="无效的标注数据格式")

        # 这里应该保存标注数据到临时存储
        # 实际实现中可以使用Redis或数据库
        temp_data = {
            'session_id': session_id,
            'annotations': annotation_data,
            'step': 2,
            'timestamp': datetime.now().isoformat()
        }

        return BaseResponse(
            success=True,
            message="标注数据保存成功，准备进行清洗",
            data={
                'session_id': session_id,
                'annotation_count': len(annotation_data.get('items', [])),
                'next_step': 'cleaning',
                'step': 2
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据标注失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据标注失败: {str(e)}")


@router.post("/files/clean", response_model=BaseResponse)
async def clean_file_step3(
    request: Request,
    session_id: str = Query(..., description="会话ID"),
    cleaning_config: str = Query(..., description="清洗配置 (JSON格式)")
):
    """
    步骤3: 数据清洗 - 清洗和标准化数据
    """
    try:
        # 安全检查
        if SECURITY_MIDDLEWARE_AVAILABLE:
            security = get_security_middleware()
            await security.check_rate_limit(request)

        logger.info(f"步骤3 - 数据清洗: {session_id}")

        # 解析清洗配置
        try:
            config = json.loads(cleaning_config)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="无效的清洗配置格式")

        # 这里应该执行实际的数据清洗
        # 可以调用现有的数据清洗器
        try:
            from data_processing.data_cleaner import DataCleaner
            cleaner = DataCleaner()

            # 模拟清洗结果
            cleaning_result = {
                'cleaned_records': 1,
                'normalized_texts': 1 if config.get('normalize_text') else 0,
                'extracted_keywords': 1 if config.get('extract_keywords') else 0,
                'quality_score': 0.85
            }

        except ImportError:
            # 如果清洗器不可用，返回模拟结果
            cleaning_result = {
                'cleaned_records': 1,
                'quality_score': 0.80
            }

        return BaseResponse(
            success=True,
            message="数据清洗完成，准备添加到知识库",
            data={
                'session_id': session_id,
                'cleaning_result': cleaning_result,
                'next_step': 'save_to_knowledge_base',
                'step': 3
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"数据清洗失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据清洗失败: {str(e)}")


@router.post("/files/save", response_model=BaseResponse)
async def save_to_knowledge_base_step4(
    request: Request,
    session_id: str = Query(..., description="会话ID"),
    final_metadata: str = Query(..., description="最终元数据 (JSON格式)"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    步骤4: 保存到知识库 - 最终保存处理后的数据
    """
    try:
        # 安全检查
        if SECURITY_MIDDLEWARE_AVAILABLE:
            security = get_security_middleware()
            await security.check_rate_limit(request)

        logger.info(f"步骤4 - 保存到知识库: {session_id}")

        # 解析最终元数据
        try:
            metadata = json.loads(final_metadata)
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="无效的元数据格式")

        # 这里应该：
        # 1. 从临时存储获取文件信息
        # 2. 从临时存储获取标注数据
        # 3. 从临时存储获取清洗结果
        # 4. 合并所有数据并保存到知识库

        # 模拟保存过程
        success = True  # 实际应该调用 knowledge_base.add_document 或 add_image

        if success:
            return BaseResponse(
                success=True,
                message="文件已成功添加到知识库",
                data={
                    'session_id': session_id,
                    'final_metadata': metadata,
                    'step': 4,
                    'status': 'completed'
                }
            )
        else:
            raise HTTPException(status_code=500, detail="保存到知识库失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存到知识库失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存到知识库失败: {str(e)}")


@router.get("/files/session/{session_id}", response_model=BaseResponse)
async def get_session_data(session_id: str):
    """
    获取会话数据 - 用于在步骤间传递数据
    """
    try:
        # 这里应该从临时存储（Redis/数据库）获取会话数据
        # 目前返回模拟数据
        session_data = {
            'session_id': session_id,
            'current_step': 1,
            'file_info': {},
            'annotations': {},
            'cleaning_result': {}
        }

        return BaseResponse(
            success=True,
            message="会话数据获取成功",
            data=session_data
        )

    except Exception as e:
        logger.error(f"获取会话数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取会话数据失败: {str(e)}")


def get_knowledge_base():
    """获取知识库依赖"""
    from ..main import app
    return app.get_system_component("knowledge_base")


@router.post("/search", response_model=KnowledgeSearchResponse)
async def search_knowledge(
    request: KnowledgeSearchRequest,
    knowledge_base=Depends(get_knowledge_base)
):
    """
    知识库搜索接口
    
    在知识库中搜索相关文档和图像
    """
    try:
        logger.info(f"知识库搜索: {request.query}, 类型: {request.search_type}")
        
        # 执行搜索
        results = knowledge_base.search(
            query=request.query,
            search_type=request.search_type,
            top_k=request.top_k
        )
        
        # 计算总结果数
        total_results = 0
        if results.get("results"):
            search_results = results["results"]
            if isinstance(search_results, dict):
                total_results = len(search_results.get("text", [])) + len(search_results.get("images", []))
            elif isinstance(search_results, list):
                total_results = len(search_results)
        
        return KnowledgeSearchResponse(
            success=True,
            message=f"搜索完成，找到 {total_results} 个结果",
            results=results,
            query=request.query,
            search_type=request.search_type,
            total_results=total_results
        )
        
    except Exception as e:
        logger.error(f"知识库搜索错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"知识库搜索失败: {str(e)}")


@router.get("/search", response_model=KnowledgeSearchResponse)
async def search_knowledge_get(
    query: str = Query(..., description="搜索查询"),
    search_type: str = Query("multimodal", description="搜索类型"),
    top_k: int = Query(5, description="返回结果数量"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    知识库搜索接口 (GET方法)
    
    使用GET方法进行简单的知识库搜索
    """
    try:
        logger.info(f"知识库搜索 (GET): {query}, 类型: {search_type}")
        
        # 执行搜索
        results = knowledge_base.search(
            query=query,
            search_type=search_type,
            top_k=top_k
        )
        
        # 计算总结果数
        total_results = 0
        if results.get("results"):
            search_results = results["results"]
            if isinstance(search_results, dict):
                total_results = len(search_results.get("text", [])) + len(search_results.get("images", []))
            elif isinstance(search_results, list):
                total_results = len(search_results)
        
        return KnowledgeSearchResponse(
            success=True,
            message=f"搜索完成，找到 {total_results} 个结果",
            results=results,
            query=query,
            search_type=search_type,
            total_results=total_results
        )
        
    except Exception as e:
        logger.error(f"知识库搜索错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"知识库搜索失败: {str(e)}")


@router.post("/qa", response_model=DocumentQAResponse)
async def knowledge_qa(
    request: DocumentQARequest,
    knowledge_base=Depends(get_knowledge_base)
):
    """
    知识库问答接口
    
    基于知识库内容回答用户问题
    """
    try:
        logger.info(f"知识库问答: {request.question}")
        
        # 首先搜索相关文档
        search_results = knowledge_base.search(
            query=request.question,
            search_type=request.search_type,
            top_k=request.context_limit
        )
        
        # 提取文档内容用于回答
        context_documents = []
        if search_results.get("results"):
            results = search_results["results"]
            
            # 处理文本结果
            text_results = results.get("text", [])
            for result in text_results:
                context_documents.append({
                    "type": "text",
                    "content": result.get("content", ""),
                    "source": result.get("metadata", {}).get("source", "unknown"),
                    "score": result.get("score", 0)
                })
            
            # 处理图像结果
            image_results = results.get("images", [])
            for result in image_results:
                if result.get("ocr_text"):
                    context_documents.append({
                        "type": "image",
                        "content": result.get("ocr_text", ""),
                        "source": result.get("filename", "unknown"),
                        "score": result.get("score", 0)
                    })
        
        # 生成回答（这里需要集成LLM）
        if context_documents:
            # 构建上下文
            context = "\n\n".join([
                f"[来源: {doc['source']}]\n{doc['content'][:500]}..."
                for doc in context_documents[:3]
            ])
            
            # 简化的回答生成（实际应该使用LLM）
            answer = f"基于知识库中的相关文档，针对您的问题：{request.question}\n\n"
            answer += "相关信息如下：\n" + context[:1000] + "..."
            
            # 提取引用
            references = [
                {
                    "type": doc["type"],
                    "source": doc["source"],
                    "score": doc["score"],
                    "content_preview": doc["content"][:100] + "..."
                }
                for doc in context_documents
            ]
        else:
            answer = "抱歉，我在知识库中没有找到与您的问题相关的信息。"
            references = []
        
        return DocumentQAResponse(
            success=True,
            message="问答完成",
            question=request.question,
            answer=answer,
            references=references,
            confidence=0.8 if context_documents else 0.1
        )
        
    except Exception as e:
        logger.error(f"知识库问答错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"知识库问答失败: {str(e)}")


@router.get("/stats", response_model=Dict[str, Any])
async def get_knowledge_stats(
    knowledge_base=Depends(get_knowledge_base)
):
    """
    获取知识库统计信息
    
    返回知识库的统计数据
    """
    try:
        logger.info("获取知识库统计信息")
        
        # 获取统计信息
        stats = knowledge_base.get_stats()
        
        return {
            "success": True,
            "message": "统计信息获取成功",
            "stats": stats
        }
        
    except Exception as e:
        logger.error(f"获取知识库统计信息错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取知识库统计信息失败: {str(e)}")


@router.post("/documents/add", response_model=BaseResponse)
async def add_document(
    request: Request,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    metadata: str = Query(None, description="文档元数据 (JSON格式)"),
    async_processing: bool = Query(True, description="是否使用异步处理"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    添加文档到知识库

    上传并添加新文档到知识库 - 支持异步处理和安全防护
    """
    try:
        # 安全检查
        if SECURITY_MIDDLEWARE_AVAILABLE:
            security = get_security_middleware()

            # 速率限制检查
            await security.check_rate_limit(request)

            # CSRF保护（使用客户端IP作为会话ID）
            session_id = security.get_client_id(request)
            await security.verify_csrf_token(request, session_id)

        logger.info(f"添加文档到知识库: {file.filename} (异步: {async_processing})")

        # 使用统一的安全文件上传器
        from core.security_utils import validate_and_save_file
        file_info = validate_and_save_file(file, 'document', 'documents')

        # 解析和清理元数据
        doc_metadata = {}
        if metadata:
            try:
                raw_metadata = json.loads(metadata)

                # XSS防护 - 清理元数据
                if SECURITY_MIDDLEWARE_AVAILABLE:
                    security = get_security_middleware()
                    doc_metadata = security.sanitize_request_data(raw_metadata)
                else:
                    doc_metadata = raw_metadata

            except json.JSONDecodeError:
                logger.warning(f"无效的元数据格式: {metadata}")
                raise HTTPException(status_code=400, detail="无效的元数据格式")

        # 添加文件信息到元数据
        doc_metadata.update({
            'file_info': file_info,
            'original_filename': file_info['original_filename'],
            'file_size': file_info['file_size'],
            'file_type': file_info['file_type'],
            'file_hash': file_info['file_hash']
        })

        # 检查是否使用异步处理
        if async_processing and ASYNC_PROCESSING_AVAILABLE:
            # 异步处理
            task_manager = await get_task_manager()
            file_processor = get_file_processor()

            # 创建异步任务
            task_id = task_manager.create_task(
                task_type="document_processing",
                metadata={
                    'filename': file.filename,
                    'file_path': file_info['file_path'],
                    'file_size': file_info['file_size']
                }
            )

            # 启动异步处理
            await task_manager.execute_task(
                task_id,
                file_processor.process_document_sync,
                file_info['file_path'],
                doc_metadata
            )

            return BaseResponse(
                success=True,
                message=f"文档 {file.filename} 已开始异步处理",
                data={
                    'task_id': task_id,
                    'file_info': file_info,
                    'metadata': doc_metadata,
                    'processing_status': 'async'
                }
            )
        else:
            # 同步处理（原有逻辑）
            success = knowledge_base.add_document(file_info['file_path'], metadata=doc_metadata)

            if success:
                return BaseResponse(
                    success=True,
                    message=f"文档 {file.filename} 已成功添加到知识库",
                    data={
                        'file_info': file_info,
                        'metadata': doc_metadata,
                        'processing_status': 'sync'
                    }
                )
            else:
                # 删除上传的文件
                if os.path.exists(file_info['file_path']):
                    os.remove(file_info['file_path'])
            raise HTTPException(status_code=400, detail="文档添加失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加文档到知识库错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加文档失败: {str(e)}")


@router.post("/images/add", response_model=BaseResponse)
async def add_image(
    file: UploadFile = File(...),
    metadata: str = Query(None, description="图像元数据 (JSON格式)"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    添加图像到知识库
    
    上传并添加新图像到知识库
    """
    try:
        logger.info(f"添加图像到知识库: {file.filename}")
        
        # 使用统一的安全文件上传器
        from core.security_utils import validate_and_save_file
        file_info = validate_and_save_file(file, 'image', 'images')
        
        # 解析元数据
        img_metadata = {}
        if metadata:
            import json
            try:
                img_metadata = json.loads(metadata)
            except json.JSONDecodeError:
                logger.warning(f"无效的元数据格式: {metadata}")

        # 添加文件信息到元数据
        img_metadata.update({
            'file_info': file_info,
            'original_filename': file_info['original_filename'],
            'file_size': file_info['file_size'],
            'file_type': file_info['file_type'],
            'file_hash': file_info['file_hash']
        })

        # 添加图像到知识库
        success = knowledge_base.add_image(file_info['file_path'], img_metadata)
        
        if success:
            return BaseResponse(
                success=True,
                message=f"图像 {file.filename} 已成功添加到知识库",
                data={
                    'file_info': file_info,
                    'metadata': img_metadata
                }
            )
        else:
            # 删除上传的文件
            if os.path.exists(file_info['file_path']):
                os.remove(file_info['file_path'])
            raise HTTPException(status_code=400, detail="图像添加失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加图像到知识库错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加图像失败: {str(e)}")


@router.post("/rebuild", response_model=BaseResponse)
async def rebuild_knowledge_base(
    knowledge_base=Depends(get_knowledge_base)
):
    """
    重建知识库索引
    
    重新构建知识库的向量索引
    """
    try:
        logger.info("开始重建知识库索引")
        
        # 重建知识库
        success = knowledge_base.build_knowledge_base()
        
        if success:
            return BaseResponse(
                success=True,
                message="知识库索引重建完成"
            )
        else:
            raise HTTPException(status_code=500, detail="知识库索引重建失败")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重建知识库索引错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重建知识库索引失败: {str(e)}")


@router.get("/tasks/{task_id}", response_model=BaseResponse)
async def get_task_status(task_id: str):
    """
    获取异步任务状态
    """
    try:
        if not ASYNC_PROCESSING_AVAILABLE:
            raise HTTPException(status_code=501, detail="异步处理功能不可用")

        task_manager = await get_task_manager()
        task_info = task_manager.get_task_info(task_id)

        if not task_info:
            raise HTTPException(status_code=404, detail="任务不存在")

        return BaseResponse(
            success=True,
            message="任务状态获取成功",
            data=task_info.to_dict()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")


@router.get("/tasks", response_model=BaseResponse)
async def list_tasks(
    status: str = Query(None, description="任务状态过滤"),
    task_type: str = Query(None, description="任务类型过滤"),
    limit: int = Query(20, description="返回数量限制")
):
    """
    获取任务列表
    """
    try:
        if not ASYNC_PROCESSING_AVAILABLE:
            raise HTTPException(status_code=501, detail="异步处理功能不可用")

        task_manager = await get_task_manager()

        # 转换状态参数
        status_filter = None
        if status:
            try:
                status_filter = TaskStatus(status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"无效的任务状态: {status}")

        tasks = task_manager.list_tasks(status=status_filter, task_type=task_type)

        # 限制返回数量
        tasks = tasks[:limit]

        return BaseResponse(
            success=True,
            message=f"获取到 {len(tasks)} 个任务",
            data={
                'tasks': [task.to_dict() for task in tasks],
                'total': len(tasks)
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.delete("/tasks/{task_id}", response_model=BaseResponse)
async def cancel_task(task_id: str):
    """
    取消异步任务
    """
    try:
        if not ASYNC_PROCESSING_AVAILABLE:
            raise HTTPException(status_code=501, detail="异步处理功能不可用")

        task_manager = await get_task_manager()
        success = task_manager.cancel_task(task_id)

        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")

        return BaseResponse(
            success=True,
            message="任务已取消",
            data={'task_id': task_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/documents/list", response_model=Dict[str, Any])
async def list_documents(
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    获取文档列表
    
    返回知识库中的文档列表
    """
    try:
        logger.info(f"获取文档列表，limit: {limit}, offset: {offset}")
        
        # 这里需要实现文档列表功能
        # 目前返回示例数据
        documents = [
            {
                "id": "doc_001",
                "filename": "设备手册.pdf",
                "path": "data/documents/设备手册.pdf",
                "size": 1024000,
                "upload_time": "2024-01-01T00:00:00Z",
                "metadata": {"type": "manual", "category": "equipment"}
            },
            {
                "id": "doc_002", 
                "filename": "故障案例.docx",
                "path": "data/documents/故障案例.docx",
                "size": 512000,
                "upload_time": "2024-01-02T00:00:00Z",
                "metadata": {"type": "case", "category": "fault"}
            }
        ]
        
        # 分页
        total_count = len(documents)
        documents = documents[offset:offset + limit]
        
        return {
            "success": True,
            "message": f"找到 {total_count} 个文档",
            "documents": documents,
            "total_count": total_count
        }
        
    except Exception as e:
        logger.error(f"获取文档列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")


@router.get("/images/list", response_model=Dict[str, Any])
async def list_images(
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    knowledge_base=Depends(get_knowledge_base)
):
    """
    获取图像列表
    
    返回知识库中的图像列表
    """
    try:
        logger.info(f"获取图像列表，limit: {limit}, offset: {offset}")
        
        # 这里需要实现图像列表功能
        # 目前返回示例数据
        images = [
            {
                "id": "img_001",
                "filename": "设备照片1.jpg",
                "path": "data/images/设备照片1.jpg",
                "size": 2048000,
                "upload_time": "2024-01-01T00:00:00Z",
                "ocr_text": "变压器铭牌信息...",
                "metadata": {"type": "equipment", "location": "主变区域"}
            },
            {
                "id": "img_002",
                "filename": "故障现象.png", 
                "path": "data/images/故障现象.png",
                "size": 1536000,
                "upload_time": "2024-01-02T00:00:00Z",
                "ocr_text": "故障录波图...",
                "metadata": {"type": "fault", "equipment": "断路器"}
            }
        ]
        
        # 分页
        total_count = len(images)
        images = images[offset:offset + limit]
        
        return {
            "success": True,
            "message": f"找到 {total_count} 个图像",
            "images": images,
            "total_count": total_count
        }
        
    except Exception as e:
        logger.error(f"获取图像列表错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取图像列表失败: {str(e)}")
