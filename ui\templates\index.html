<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障分析智能助手</title>
    <!-- 缓存破坏器 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/main.css" rel="stylesheet">
    <link href="/static/css/main-simplified.css" rel="stylesheet">
    <link href="/static/css/enhanced.css?v=20250721" rel="stylesheet">
    <style>
        /* Bootstrap基础样式 - 内联版本 */
        .container-fluid { width: 100%; padding-right: 15px; padding-left: 15px; margin-right: auto; margin-left: auto; }
        .navbar { position: relative; display: flex; flex-wrap: wrap; align-items: center; justify-content: space-between; padding: 0.5rem 1rem; }
        .navbar-dark { background-color: #0d6efd !important; }
        .navbar-brand { padding-top: 0.3125rem; padding-bottom: 0.3125rem; margin-right: 1rem; font-size: 1.25rem; text-decoration: none; white-space: nowrap; color: white; }
        .navbar-nav { display: flex; flex-direction: row; padding-left: 0; margin-bottom: 0; list-style: none; }
        .nav-link { display: block; padding: 0.5rem 1rem; color: rgba(255,255,255,.75); text-decoration: none; }
        .nav-link:hover, .nav-link.active { color: white; }
        .btn { display: inline-block; font-weight: 400; line-height: 1.5; color: #212529; text-align: center; text-decoration: none; vertical-align: middle; cursor: pointer; border: 1px solid transparent; padding: 0.375rem 0.75rem; font-size: 1rem; border-radius: 0.25rem; }
        .btn-primary { color: #fff; background-color: #0d6efd; border-color: #0d6efd; }
        .btn-success { color: #fff; background-color: #198754; border-color: #198754; }
        .btn-warning { color: #000; background-color: #ffc107; border-color: #ffc107; }
        .btn-danger { color: #fff; background-color: #dc3545; border-color: #dc3545; }
        .card { position: relative; display: flex; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #fff; background-clip: border-box; border: 1px solid rgba(0,0,0,.125); border-radius: 0.25rem; }
        .card-header { padding: 0.5rem 1rem; margin-bottom: 0; background-color: rgba(0,0,0,.03); border-bottom: 1px solid rgba(0,0,0,.125); }
        .card-body { flex: 1 1 auto; padding: 1rem; }
        .form-control { display: block; width: 100%; padding: 0.375rem 0.75rem; font-size: 1rem; font-weight: 400; line-height: 1.5; color: #212529; background-color: #fff; background-image: none; border: 1px solid #ced4da; border-radius: 0.25rem; }
        .form-label { margin-bottom: 0.5rem; font-weight: 500; }
        .mb-3 { margin-bottom: 1rem !important; }
        .mt-3 { margin-top: 1rem !important; }
        .text-success { color: #198754 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }
        .d-none { display: none !important; }
        .spinner-border { display: inline-block; width: 2rem; height: 2rem; vertical-align: text-bottom; border: 0.25em solid currentColor; border-right-color: transparent; border-radius: 50%; animation: spinner-border .75s linear infinite; }
        @keyframes spinner-border { to { transform: rotate(360deg); } }
        .tab-content { display: block; }
        .tab-pane { display: none; }
        .tab-pane.show { display: block; }
        .tab-pane.active { display: block; }
        .tab-pane.show.active { display: block; }
        .main-tab-content { display: none; }
        .main-tab-content.active { display: block; }
        .navbar-text { color: rgba(255,255,255,.75); }
        .me-auto { margin-right: auto !important; }
        .nav-item { list-style: none; }
        /* 生产环境模态框样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1050;
            display: none;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: rgba(0,0,0,0.6);
        }

        /* 标签页样式 */
        .nav-tabs {
            display: flex;
            flex-wrap: wrap;
            padding-left: 0;
            margin-bottom: 0;
            list-style: none;
            border-bottom: 1px solid #dee2e6;
        }
        .nav-tabs .nav-item {
            margin-bottom: -1px;
        }
        .nav-tabs .nav-link {
            display: block;
            padding: 0.5rem 1rem;
            margin-right: 2px;
            text-decoration: none;
            background: none;
            border: 1px solid transparent;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
            color: #495057;
            cursor: pointer;
        }
        .nav-tabs .nav-link:hover {
            border-color: #e9ecef #e9ecef #dee2e6;
            isolation: isolate;
        }
        .nav-tabs .nav-link.active {
            color: #495057;
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }

        /* 工作流程指示器样式 */
        .workflow-steps {
            padding: 20px 0;
        }
        .workflow-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #e9ecef;
            z-index: 1;
        }
        .workflow-step {
            position: relative;
            z-index: 2;
            text-align: center;
            background: white;
            padding: 0 10px;
        }
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 8px;
            transition: all 0.3s ease;
        }
        .workflow-step.active .step-circle {
            background: #0d6efd;
            color: white;
        }
        .workflow-step.completed .step-circle {
            background: #198754;
            color: white;
        }
        .step-label {
            font-size: 12px;
            color: #6c757d;
            font-weight: 500;
        }
        .workflow-step.active .step-label {
            color: #0d6efd;
            font-weight: 600;
        }

        /* 智能处理指示器样式 */
        #smart-processing-indicator {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .processing-steps {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .step-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .step-item.active {
            border-color: #007bff;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: translateX(5px);
        }

        .step-item.completed {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        }

        .step-item.error {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        }

        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.2rem;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .step-item.active .step-icon {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            animation: stepPulse 2s infinite;
        }

        .step-item.completed .step-icon {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .step-item.error .step-icon {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }

        .step-status {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .step-item.active .step-status {
            color: #007bff;
            font-weight: 500;
        }

        .step-item.completed .step-status {
            color: #28a745;
            font-weight: 500;
        }

        .step-item.error .step-status {
            color: #dc3545;
            font-weight: 500;
        }

        .step-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dee2e6;
            transition: all 0.3s ease;
        }

        .step-item.active .step-indicator {
            background: #007bff;
            animation: stepPulse 2s infinite;
        }

        .step-item.completed .step-indicator {
            background: #28a745;
        }

        .step-item.error .step-indicator {
            background: #dc3545;
        }

        .bg-gradient-primary {
            background: linear-gradient(45deg, #007bff, #0056b3) !important;
        }

        .bg-gradient-success {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
        }

        @keyframes stepPulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
            }
        }
        .modal.show { display: block; }
        .modal-dialog {
            position: relative;
            width: auto;
            margin: 1rem auto;
            pointer-events: none;
            max-height: calc(100vh - 2rem);
        }
        .modal-dialog-scrollable {
            height: calc(100vh - 2rem);
        }
        .modal-dialog-scrollable .modal-content {
            max-height: 100%;
            overflow: hidden;
        }
        .modal-dialog-scrollable .modal-body {
            overflow-y: auto;
            min-height: 400px;
        }
        .modal-lg { max-width: 800px; }
        .modal-xl { max-width: 1140px; }
        .modal-content {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 100%;
            pointer-events: auto;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid rgba(0,0,0,.2);
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
        }
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #dee2e6;
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
        }
        .modal-title { margin-bottom: 0; line-height: 1.5; font-size: 1.25rem; }
        .modal-body {
            position: relative;
            flex: 1 1 auto;
            padding: 1.5rem;
        }

        /* 知识库模态框专用样式 */
        .knowledge-upload-area {
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            cursor: pointer;
        }
        .knowledge-upload-area:hover {
            border-color: #0d6efd !important;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
        }
        .modal-footer {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
            border-bottom-right-radius: 0.5rem;
            border-bottom-left-radius: 0.5rem;
            background-color: #f8f9fa;
        }
        .modal-footer > * { margin: 0.25rem; }
        .btn-close {
            box-sizing: content-box;
            width: 1em;
            height: 1em;
            padding: 0.25em;
            color: #fff;
            background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m.235.867 8.832 8.832m-8.832 0L8.067.867'/%3e%3c/svg%3e") center/1em auto no-repeat;
            border: 0;
            border-radius: 0.25rem;
            opacity: 0.8;
            cursor: pointer;
        }
        .btn-close:hover { opacity: 1; }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .modal-dialog {
                margin: 0.5rem;
                max-width: calc(100vw - 1rem);
            }
            .modal-lg {
                max-width: 100%;
            }
            .modal-header {
                padding: 0.75rem 1rem;
            }
            .modal-body {
                padding: 1rem;
            }
            .modal-footer {
                padding: 0.75rem 1rem;
                flex-direction: column-reverse;
                gap: 0.5rem;
            }
            .modal-footer .btn {
                width: 100%;
                margin: 0;
            }
            .modal-title {
                font-size: 1.1rem;
            }
        }

        @media (max-width: 576px) {
            .modal-dialog {
                margin: 0.25rem;
                max-width: calc(100vw - 0.5rem);
                height: calc(100vh - 0.5rem);
            }
            .modal-dialog-scrollable {
                height: calc(100vh - 0.5rem);
            }
        }
        .collapse { display: none; }
        .collapse.show { display: block; }
        /* 图标替代 */
        .bi::before { content: "●"; margin-right: 0.5rem; }
        .bi-lightning-charge::before { content: "⚡"; }
        .bi-search::before { content: "🔍"; }
        .bi-gear::before { content: "⚙️"; }
        .bi-book::before { content: "📚"; }
        .bi-upload::before { content: "📤"; }
        .bi-circle-fill::before { content: "●"; }
        .bi-file-text::before { content: "📄"; }
        .bi-image::before { content: "🖼️"; }
        .bi-cloud-upload::before { content: "☁️"; }
        .bi-funnel::before { content: "🔽"; }
        /* 模型选择按钮样式 */
        .model-btn {
            text-align: center;
            padding: 0.75rem;
            border: 2px solid #dee2e6;
            transition: all 0.3s ease;
        }
        .model-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .model-btn.active {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .model-btn.active[data-model="r1"] {
            border-color: #198754;
            background-color: #e8f5e8;
        }
        .d-flex { display: flex; }
        .gap-2 { gap: 0.5rem; }
        .flex-fill { flex: 1 1 auto; }

        /* 推理过程容器样式 */
        .reasoning-content {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #667eea;
            padding: 1.2rem;
            margin: 0.5rem 0;
            border-radius: 0 12px 12px 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.7;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .reasoning-paragraph {
            margin-bottom: 1rem;
            text-align: justify;
            color: #2c3e50;
        }

        .reasoning-paragraph:last-child {
            margin-bottom: 0;
        }

        /* 最终分析样式 */
        .analysis-paragraph {
            margin-bottom: 1rem;
            line-height: 1.8;
            text-align: justify;
            color: #2c3e50;
        }

        .key-finding {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 2px 8px;
            border-radius: 6px;
            font-weight: 600;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .section-highlight {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
            padding: 3px 8px;
            border-radius: 6px;
            font-weight: 600;
            margin-right: 8px;
        }

        /* 优化的界面样式 */
        .knowledge-upload-area {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .knowledge-upload-area:hover {
            border-color: #0d6efd !important;
            background-color: #f8f9ff;
        }

        /* 渐变背景 */
        .bg-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }
        .bg-gradient-success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
        }

        /* 上传区域悬停效果 */
        .upload-zone:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
        }

        /* 卡片悬停效果 */
        .card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
        }

        /* 按钮组样式 */
        .nav-pills .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border: none !important;
        }

        /* 进度条动画 */
        .progress-bar {
            transition: width 0.6s ease;
        }

        /* 表单浮动标签优化 */
        .form-floating > label {
            color: #6c757d;
        }
        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: #0d6efd;
        }

        /* 模态框动画 */
        .modal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
            transform: translate(0, -50px);
        }
        .modal.show .modal-dialog {
            transform: none;
        }

        /* 标签样式 */
        .badge {
            transition: all 0.2s ease;
        }
        .badge:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-lightning-charge"></i>
                故障分析智能助手
            </a>
            <button class="navbar-toggler" type="button" onclick="toggleNavbar()">
                <span class="navbar-toggler-icon">☰</span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#fault-analysis" data-tab="fault-analysis">
                            <i class="bi bi-search"></i> 故障分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#equipment-management" data-tab="equipment-management">
                            <i class="bi bi-gear"></i> 设备管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#knowledge-base" data-tab="knowledge-base">
                            <i class="bi bi-book"></i> 知识库
                        </a>
                    </li>


                </ul>
                <div class="navbar-nav">
                    <div class="nav-item">
                        <span class="navbar-text" id="system-status">
                            <i class="bi bi-circle-fill text-success"></i> 系统正常
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="container-fluid mt-3">
        <!-- 故障分析页面 -->
        <div id="fault-analysis" class="main-tab-content active">
            <!-- AI智能检索框 -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-primary">
                        <div class="card-header bg-gradient" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                            <h5 class="mb-0"><i class="bi bi-robot"></i> AI故障分析助手</h5>
                            <small>基于DeepSeek大模型的智能故障诊断系统</small>
                        </div>
                        <div class="card-body">
                            <div class="input-group mb-3">
                                <span class="input-group-text bg-primary text-white">
                                    <i class="bi bi-magic"></i>
                                </span>
                                <input type="text" class="form-control form-control-lg" id="ai-search-input"
                                       placeholder="描述故障现象，AI将为您提供专业分析建议...例如：变压器差动保护动作，现场有异响和油温升高">
                                <button class="btn btn-primary btn-lg px-4" type="button" id="ai-search-btn">
                                    <i class="bi bi-robot"></i> DeepSeek分析
                                </button>
                            </div>

                            <!-- AI模型选择 -->
                            <div class="row mt-3">
                                <div class="col-12">
                                    <label class="form-label">选择AI模型</label>
                                    <div class="d-flex gap-2">
                                        <button type="button" class="btn btn-outline-primary model-btn flex-fill" id="deepseekV3Btn" data-model="v3">
                                            <strong>DeepSeek-V3</strong><br>
                                            <small>多模态大模型</small>
                                        </button>
                                        <button type="button" class="btn btn-outline-success model-btn flex-fill" id="deepseekR1Btn" data-model="r1">
                                            <strong>DeepSeek-R1</strong><br>
                                            <small>纯文本大语言模型</small>
                                        </button>
                                    </div>
                                    <small class="text-muted mt-1">DeepSeek-V3提供结构化分析，DeepSeek-R1展示完整思考过程</small>
                                </div>
                            </div>

                            <!-- AI分析结果区域 -->
                            <div id="ai-search-results" class="mt-3" style="display: none;">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <i class="bi bi-lightbulb"></i> AI分析结果
                                    </div>
                                    <div class="card-body" id="ai-analysis-content">
                                        <!-- AI分析内容将在这里显示 -->
                                    </div>
                                </div>
                            </div>

                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle"></i>
                                <strong>您好！我是您的电力得力助手</strong>，专注于诊断电力系统方面的问题。
                                您可以直接描述故障现象获取AI分析，或按照以下步骤进行详细故障分析：
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 故障分析步骤 -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-pills" id="fault-analysis-tabs">
                                <li class="nav-item">
                                    <a class="nav-link active" onclick="showStep('step1')" href="#step1">1. 运行方式</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step2')" href="#step2">2. 设备信息</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step3')" href="#step3">3. 现场检查</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step4')" href="#step4">4. 保护动作</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step5')" href="#step5">5. 解体检查</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step6')" href="#step6">6. 原因分析</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" onclick="showStep('step7')" href="#step7">7. 后续工作</a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="fault-analysis-steps">
                                <!-- 步骤1: 故障前运行方式 -->
                                <div class="tab-pane fade show active" id="step1">
                                    <h6 class="text-primary"><i class="bi bi-diagram-3"></i> 故障前运行方式</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">系统运行方式</label>
                                                <select class="form-select" id="system-mode">
                                                    <option value="">请选择运行方式</option>
                                                    <option value="normal">正常运行方式</option>
                                                    <option value="maintenance">检修运行方式</option>
                                                    <option value="emergency">事故运行方式</option>
                                                    <option value="special">特殊运行方式</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">负荷情况</label>
                                                <input type="text" class="form-control" id="load-condition"
                                                       placeholder="如：轻载、重载、满载等">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">天气条件</label>
                                                <select class="form-select" id="weather-condition">
                                                    <option value="">请选择天气条件</option>
                                                    <option value="clear">晴朗</option>
                                                    <option value="rain">雨天</option>
                                                    <option value="thunder">雷雨</option>
                                                    <option value="fog">雾天</option>
                                                    <option value="wind">大风</option>
                                                    <option value="snow">雪天</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">运行时间</label>
                                                <input type="text" class="form-control" id="operation-time"
                                                       placeholder="设备连续运行时间">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">详细描述</label>
                                        <textarea class="form-control" id="operation-description" rows="3"
                                                  placeholder="请详细描述故障前的系统运行状态、网络结构、潮流分布等..."></textarea>
                                    </div>
                                    <div class="text-end">
                                        <button type="button" class="btn btn-primary" onclick="nextStep(2)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤2: 设备基本信息 -->
                                <div class="tab-pane fade" id="step2">
                                    <h6 class="text-primary"><i class="bi bi-gear"></i> 设备基本信息</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">设备类型</label>
                                                <select class="form-select" id="fault-equipment-type">
                                                    <option value="">请选择设备类型</option>
                                                    <option value="transformer">变压器</option>
                                                    <option value="breaker">断路器</option>
                                                    <option value="line">输电线路</option>
                                                    <option value="generator">发电机</option>
                                                    <option value="reactor">电抗器</option>
                                                    <option value="capacitor">电容器</option>
                                                    <option value="busbar">母线</option>
                                                    <option value="other">其他</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">设备编号</label>
                                                <input type="text" class="form-control" id="equipment-number"
                                                       placeholder="设备编号或名称">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">额定电压</label>
                                                <input type="text" class="form-control" id="rated-voltage"
                                                       placeholder="如：220kV、110kV等">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">制造厂家</label>
                                                <input type="text" class="form-control" id="manufacturer"
                                                       placeholder="设备制造厂家">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">投运时间</label>
                                                <input type="date" class="form-control" id="commissioning-date">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">最近检修时间</label>
                                                <input type="date" class="form-control" id="last-maintenance">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">设备技术参数</label>
                                        <textarea class="form-control" id="equipment-parameters" rows="3"
                                                  placeholder="请填写设备的主要技术参数，如容量、型号、绝缘等级等..."></textarea>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(1)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(3)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤3: 现场设备检查情况 -->
                                <div class="tab-pane fade" id="step3">
                                    <h6 class="text-primary"><i class="bi bi-search"></i> 现场设备检查情况</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">外观检查</label>
                                                <textarea class="form-control" id="visual-inspection" rows="3"
                                                          placeholder="设备外观是否有明显损伤、变形、烧损等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">声音异常</label>
                                                <textarea class="form-control" id="sound-check" rows="2"
                                                          placeholder="是否有异常声音，如放电声、机械振动等..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">气味检查</label>
                                                <textarea class="form-control" id="smell-check" rows="2"
                                                          placeholder="是否有焦糊味、臭氧味等异常气味..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">温度检查</label>
                                                <textarea class="form-control" id="temperature-check" rows="3"
                                                          placeholder="设备温度是否正常，有无局部过热现象..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">其他现场情况</label>
                                        <textarea class="form-control" id="other-site-conditions" rows="3"
                                                  placeholder="其他现场发现的异常情况..."></textarea>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(2)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(4)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤4: 保护装置动作及故障录波情况 -->
                                <div class="tab-pane fade" id="step4">
                                    <h6 class="text-primary"><i class="bi bi-shield-check"></i> 保护装置动作及故障录波情况</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">保护动作情况</label>
                                                <textarea class="form-control" id="protection-action" rows="3"
                                                          placeholder="哪些保护装置动作，动作时间、动作值等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">故障录波数据</label>
                                                <textarea class="form-control" id="fault-recording" rows="3"
                                                          placeholder="故障录波器记录的电流、电压波形特征..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">继电保护报告</label>
                                                <textarea class="form-control" id="relay-report" rows="3"
                                                          placeholder="继电保护装置的报告信息..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">测量数据</label>
                                                <textarea class="form-control" id="measurement-data" rows="3"
                                                          placeholder="故障时的电流、电压、功率等测量值..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(3)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(5)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤5: 现场解体检查 -->
                                <div class="tab-pane fade" id="step5">
                                    <h6 class="text-primary"><i class="bi bi-tools"></i> 现场解体检查</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">解体检查结果</label>
                                                <textarea class="form-control" id="disassembly-results" rows="4"
                                                          placeholder="解体后发现的问题，如绝缘损坏、接触不良、机械磨损等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">内部检查情况</label>
                                                <textarea class="form-control" id="internal-inspection" rows="3"
                                                          placeholder="内部结构检查情况，如油质分析、绝缘测试等..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">测试数据</label>
                                                <textarea class="form-control" id="test-data" rows="3"
                                                          placeholder="相关测试数据，如绝缘电阻、介损、直流电阻等..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">照片记录</label>
                                                <input type="file" class="form-control" id="inspection-photos" multiple accept="image/*">
                                                <small class="form-text text-muted">可上传现场检查照片</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(4)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(6)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤6: 故障原因分析 -->
                                <div class="tab-pane fade" id="step6">
                                    <h6 class="text-primary"><i class="bi bi-exclamation-triangle"></i> 故障原因分析</h6>
                                    <div class="mb-3">
                                        <label class="form-label">初步分析结论</label>
                                        <textarea class="form-control" id="preliminary-analysis" rows="4"
                                                  placeholder="基于以上信息的初步故障原因分析..."></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">可能原因排序</label>
                                                <textarea class="form-control" id="cause-ranking" rows="4"
                                                          placeholder="按可能性大小排列的故障原因..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">影响因素分析</label>
                                                <textarea class="form-control" id="factor-analysis" rows="4"
                                                          placeholder="环境、运行、维护等因素对故障的影响..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(5)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep(7)">
                                            下一步 <i class="bi bi-arrow-right"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 步骤7: 下一步重点工作 -->
                                <div class="tab-pane fade" id="step7">
                                    <h6 class="text-primary"><i class="bi bi-list-check"></i> 下一步重点工作</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">应急处理措施</label>
                                                <textarea class="form-control" id="emergency-measures" rows="4"
                                                          placeholder="需要立即采取的应急处理措施..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">修复方案</label>
                                                <textarea class="form-control" id="repair-plan" rows="3"
                                                          placeholder="设备修复的具体方案和步骤..."></textarea>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">预防措施</label>
                                                <textarea class="form-control" id="prevention-measures" rows="3"
                                                          placeholder="防止类似故障再次发生的措施..."></textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">后续监控</label>
                                                <textarea class="form-control" id="monitoring-plan" rows="4"
                                                          placeholder="需要重点监控的项目和周期..."></textarea>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep(6)">
                                            <i class="bi bi-arrow-left"></i> 上一步
                                        </button>
                                        <button type="button" class="btn btn-success" onclick="generateAnalysisReport()">
                                            <i class="bi bi-cpu"></i> 生成分析报告
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析结果显示区域 -->
            <div class="row mt-4" id="analysis-result-section" style="display: none;">
                <div class="col-12">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="bi bi-clipboard-data"></i> AI智能分析结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="analysis-loading" class="text-center d-none">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">AI分析中...</span>
                                </div>
                                <p class="mt-2">正在进行智能分析，请稍候...</p>
                            </div>

                            <div id="analysis-results">
                                <!-- AI分析结果将在这里显示 -->
                            </div>

                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-primary me-2" onclick="exportAnalysisReport()">
                                    <i class="bi bi-download"></i> 导出报告
                                </button>
                                <button type="button" class="btn btn-info me-2" onclick="saveAnalysisResult()">
                                    <i class="bi bi-save"></i> 保存结果
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="resetAnalysis()">
                                    <i class="bi bi-arrow-clockwise"></i> 重新分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 设备管理页面 -->
        <div id="equipment-management" class="main-tab-content">
            <div class="row">
                <!-- 左侧：添加设备表单 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-plus-circle"></i> 添加设备</h5>
                        </div>
                        <div class="card-body">
                            <form id="equipment-form">
                                <div class="mb-3">
                                    <label class="form-label">设备名称</label>
                                    <input type="text" class="form-control" id="equipment-name" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">设备类型</label>
                                    <select class="form-select" id="equipment-type" required>
                                        <option value="">请选择设备类型</option>
                                        <option value="transformer">变压器</option>
                                        <option value="breaker">断路器</option>
                                        <option value="switch">开关</option>
                                        <option value="capacitor">电容器</option>
                                        <option value="arrester">避雷器</option>
                                        <option value="cable">电缆</option>
                                        <option value="busbar">母线</option>
                                        <option value="reactor">电抗器</option>
                                        <option value="generator">发电机</option>
                                        <option value="motor">电机</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">安装位置</label>
                                    <input type="text" class="form-control" id="equipment-location" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">运行状态</label>
                                    <select class="form-select" id="equipment-status" required>
                                        <option value="">请选择状态</option>
                                        <option value="running">运行中</option>
                                        <option value="standby">备用</option>
                                        <option value="maintenance">检修</option>
                                        <option value="fault">故障</option>
                                        <option value="offline">离线</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div id="equipment-enhanced-search-status" class="alert alert-info d-none">
                                        <strong>🚀 增强搜索已启用</strong>
                                        <br><small>使用720+专业文档的高级检索算法</small>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div id="enhanced-search-status" class="alert alert-info d-none">
                                        <strong>🚀 增强搜索已启用</strong>
                                        <br><small>使用720+专业文档的高级检索算法</small>
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-plus"></i> 添加设备
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- 设备统计卡片 -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6><i class="bi bi-bar-chart"></i> 设备统计</h6>
                        </div>
                        <div class="card-body">
                            <div id="equipment-statistics">
                                <div class="row text-center">
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-number text-primary" id="total-equipment">0</div>
                                            <div class="stat-label">总设备</div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-item">
                                            <div class="stat-number text-success" id="running-equipment">0</div>
                                            <div class="stat-label">运行中</div>
                                        </div>
                                    </div>
                                    <div class="col-6 mt-2">
                                        <div class="stat-item">
                                            <div class="stat-number text-warning" id="maintenance-equipment">0</div>
                                            <div class="stat-label">检修中</div>
                                        </div> 
                                    </div>
                                    <div class="col-6 mt-2">
                                        <div class="stat-item">
                                            <div class="stat-number text-danger" id="fault-equipment">0</div>
                                            <div class="stat-label">故障</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：设备列表 -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5><i class="bi bi-list"></i> 设备列表</h5>
                            <div class="d-flex gap-2">
                                <!-- 搜索框 -->
                                <div class="input-group" style="width: 250px;">
                                    <input type="text" class="form-control" id="equipment-search"
                                           placeholder="搜索设备名称或位置...">
                                    <button class="btn btn-outline-secondary" type="button" id="search-equipment-btn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                                <!-- 筛选按钮 -->
                                <button class="btn btn-outline-primary" type="button" onclick="toggleFilters()">
                                    <i class="bi bi-funnel"></i> 筛选
                                </button>
                            </div>
                        </div>

                        <!-- 筛选器面板 -->
                        <div class="collapse" id="equipment-filters">
                            <div class="card-body border-bottom">
                                <div class="row">
                                    <div class="col-md-4">
                                        <label class="form-label">设备类型</label>
                                        <select class="form-select" id="filter-type">
                                            <option value="">全部类型</option>
                                            <option value="transformer">变压器</option>
                                            <option value="breaker">断路器</option>
                                            <option value="switch">开关</option>
                                            <option value="capacitor">电容器</option>
                                            <option value="arrester">避雷器</option>
                                            <option value="cable">电缆</option>
                                            <option value="busbar">母线</option>
                                            <option value="reactor">电抗器</option>
                                            <option value="generator">发电机</option>
                                            <option value="motor">电机</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">运行状态</label>
                                        <select class="form-select" id="filter-status">
                                            <option value="">全部状态</option>
                                            <option value="running">运行中</option>
                                            <option value="standby">备用</option>
                                            <option value="maintenance">检修</option>
                                            <option value="fault">故障</option>
                                            <option value="offline">离线</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">安装位置</label>
                                        <input type="text" class="form-control" id="filter-location"
                                               placeholder="输入位置关键词">
                                    </div>
                                </div>
                                <div class="row mt-3">
                                    <div class="col-12">
                                        <button class="btn btn-primary me-2" onclick="applyEquipmentFilters()">
                                            <i class="bi bi-check"></i> 应用筛选
                                        </button>
                                        <button class="btn btn-outline-secondary" onclick="clearEquipmentFilters()">
                                            <i class="bi bi-x"></i> 清除筛选
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-body">
                            <!-- 搜索结果统计 -->
                            <div id="equipment-search-stats" class="mb-3 text-muted small"></div>

                            <!-- 设备表格 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>设备ID</th>
                                            <th>设备名称</th>
                                            <th>类型</th>
                                            <th>安装位置</th>
                                            <th>运行状态</th>
                                            <th>最后更新</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="equipment-table-body">
                                        <tr>
                                            <td colspan="7" class="text-center text-muted">
                                                <div class="py-4">
                                                    <i class="bi bi-hourglass-split fs-1"></i>
                                                    <div>正在加载设备数据...</div>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识库页面 -->
        <div id="knowledge-base" class="main-tab-content">
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-search"></i> 知识库搜索</h5>
                        </div>
                        <div class="card-body">
                            <form id="knowledge-search-form">
                                <div class="mb-3">
                                    <label for="search-query" class="form-label">搜索内容</label>
                                    <input type="text" class="form-control" id="search-query"
                                           placeholder="输入关键词搜索知识库...">
                                </div>
                                <div class="mb-3">
                                    <label for="search-type" class="form-label">搜索类型</label>
                                    <select class="form-select" id="search-type">
                                        <option value="text">文本搜索</option>
                                        <option value="enhanced" selected>🚀 增强搜索</option>
                                        <option value="multimodal">多模态搜索</option>
                                        <option value="semantic">语义搜索</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <div id="knowledge-enhanced-search-status" class="alert alert-info d-none">
                                        <strong>🚀 增强搜索已启用</strong>
                                        <br><small>使用720+专业文档的高级检索算法</small>
                                    </div>
                                </div>


                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="bi bi-search"></i> 搜索
                                </button>


                            </form>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header">
                            <h6><i class="bi bi-plus-square"></i> 添加知识</h6>
                        </div>
                        <div class="card-body">
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="showAddDocumentModal()">
                                    <i class="bi bi-file-text"></i> 添加文档
                                </button>
                                <button type="button" class="btn btn-outline-primary" onclick="showAddImageModal()">
                                    <i class="bi bi-image"></i> 添加图片
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-list-ul"></i> 搜索结果</h5>
                        </div>
                        <div class="card-body">
                            <div id="knowledge-search-results">
                                <!-- 增强搜索结果显示区域 -->
                                <div id="enhanced-search-results" class="d-none">
                                    <div class="alert alert-success">
                                        <strong>增强搜索结果</strong>
                                        <span id="enhanced-results-count" class="badge bg-primary ms-2">0</span>
                                    </div>
                                    <div id="enhanced-intent-analysis" class="alert alert-info d-none">
                                        <strong>智能分析:</strong>
                                        <span id="intent-details"></span>
                                    </div>
                                    <div id="enhanced-results-list"></div>
                                </div>

                                <!-- 文本搜索结果显示区域 -->
                                <div id="text-search-results" class="d-none">
                                    <div class="alert alert-primary">
                                        <strong>文本搜索结果</strong>
                                        <span id="text-results-count" class="badge bg-secondary ms-2">0</span>
                                    </div>
                                    <div id="text-results-list"></div>
                                </div>

                                <!-- 多模态搜索结果显示区域 -->
                                <div id="multimodal-search-results" class="d-none">
                                    <div class="alert alert-warning">
                                        <strong>多模态搜索结果</strong>
                                        <span id="multimodal-results-count" class="badge bg-info ms-2">0</span>
                                    </div>
                                    <div id="multimodal-analysis" class="alert alert-info d-none">
                                        <strong>多模态分析:</strong>
                                        <span id="multimodal-details"></span>
                                    </div>
                                    <div id="multimodal-results-list"></div>
                                </div>

                                <!-- 语义搜索结果显示区域 -->
                                <div id="semantic-search-results" class="d-none">
                                    <div class="alert alert-info">
                                        <strong>语义搜索结果</strong>
                                        <span id="semantic-results-count" class="badge bg-success ms-2">0</span>
                                    </div>
                                    <div id="semantic-analysis" class="alert alert-light d-none">
                                        <strong>语义分析:</strong>
                                        <span id="semantic-details"></span>
                                    </div>
                                    <div id="semantic-results-list"></div>
                                </div>

                                <!-- 默认搜索结果显示区域 -->
                                <div id="default-search-results">
                                    <div class="text-center text-muted">
                                        <i class="bi bi-search" style="font-size: 3rem;"></i>
                                        <p class="mt-2">请输入搜索内容</p>
                                        <small>💡 建议使用"🚀 增强搜索"获得更好的结果</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 知识库搜索统计 -->
            <div class="row mt-2">
                <div class="col-12">
                    <small id="knowledge-search-stats" class="text-muted"></small>
                </div>
            </div>
        </div>


    </div>

    <!-- 模态框 -->
    <!-- 添加文档模态框 - 简化版本 -->
    <div class="modal fade" id="addDocumentModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-file-text me-2"></i>添加文档
                    </h5>
                    <button type="button" class="btn-close" onclick="forceHideModal('addDocumentModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 文件上传区域 -->
                    <div class="upload-area mb-3">
                        <i class="bi bi-cloud-upload"></i>
                        <p>点击选择文档文件</p>
                        <small>支持：PDF、DOCX、TXT、MD、CSV、XLSX (最大10MB)</small>
                    </div>
                    <input type="file" id="doc-file" hidden accept=".pdf,.docx,.txt,.md,.csv,.xlsx">

                    <!-- 文件信息显示 -->
                    <div id="doc-file-info" class="d-none mb-3">
                        <div class="alert alert-success">
                            <i class="bi bi-file-earmark-check me-2"></i>
                            <span id="doc-file-name"></span>
                            <small class="d-block text-muted">大小: <span id="doc-file-size"></span></small>
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="mb-3">
                        <label for="doc-title" class="form-label">文档标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="doc-title" placeholder="请输入文档标题" required>
                    </div>

                    <div class="mb-3">
                        <label for="doc-category" class="form-label">文档类别 <span class="text-danger">*</span></label>
                        <select class="form-select" id="doc-category" required>
                            <option value="">请选择类别</option>
                            <option value="故障案例">故障案例</option>
                            <option value="技术手册">技术手册</option>
                            <option value="操作规程">操作规程</option>
                            <option value="维护记录">维护记录</option>
                            <option value="检修报告">检修报告</option>
                            <option value="技术标准">技术标准</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="doc-description" class="form-label">文档描述</label>
                        <textarea class="form-control" id="doc-description" rows="3" placeholder="请输入文档描述（可选）"></textarea>
                    </div>

                    <!-- 简单的进度显示 -->
                    <div id="doc-upload-progress" class="d-none mb-3">
                        <div class="progress">
                            <div class="progress-bar" id="doc-progress-bar" style="width: 0%"></div>
                        </div>
                        <small id="doc-progress-text" class="text-muted">准备上传...</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="forceHideModal('addDocumentModal')">取消</button>
                    <button type="button" class="btn btn-primary" onclick="uploadDocument()" id="doc-upload-btn">
                        <i class="bi bi-cloud-upload me-1"></i>上传文档
                    </button>
                </div>
            </div>
        </div>
    </div>
            </div>
        </div>
    </div>

    <!-- 添加图片模态框 - 简化版本 -->
    <div class="modal fade" id="addImageModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-image me-2"></i>添加图片
                    </h5>
                    <button type="button" class="btn-close" onclick="forceHideModal('addImageModal')" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- 图片上传区域 -->
                    <div class="upload-area mb-3">
                        <i class="bi bi-image"></i>
                        <p>点击选择图片文件</p>
                        <small>支持：JPG、PNG、GIF、BMP (最大10MB，可批量上传)</small>
                    </div>
                    <input type="file" id="img-file" hidden accept="image/*" multiple>

                    <!-- 图片预览区域 -->
                    <div id="img-preview-container" class="d-none mb-3">
                        <div class="alert alert-success">
                            <i class="bi bi-images me-2"></i>
                            <span id="img-file-count"></span>
                            <div id="img-preview-list" class="mt-2"></div>
                        </div>
                    </div>

                    <!-- 基本信息 -->
                    <div class="mb-3">
                        <label for="img-title" class="form-label">图片标题 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="img-title" placeholder="请输入图片标题" required>
                    </div>

                    <div class="mb-3">
                        <label for="img-category" class="form-label">图片类别 <span class="text-danger">*</span></label>
                        <select class="form-select" id="img-category" required>
                            <option value="">请选择类别</option>
                            <option value="设备照片">设备照片</option>
                            <option value="热成像图">热成像图</option>
                            <option value="缺陷图片">缺陷图片</option>
                            <option value="技术图纸">技术图纸</option>
                            <option value="维护记录">维护记录</option>
                            <option value="巡检照片">巡检照片</option>
                            <option value="故障现场">故障现场</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="img-description" class="form-label">图片描述</label>
                        <textarea class="form-control" id="img-description" rows="3" placeholder="请输入图片描述（可选）"></textarea>
                    </div>

                    <!-- 简单的进度显示 -->
                    <div id="img-upload-progress" class="d-none mb-3">
                        <div class="progress">
                            <div class="progress-bar" id="img-progress-bar" style="width: 0%"></div>
                        </div>
                        <small id="img-progress-text" class="text-muted">准备上传...</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="forceHideModal('addImageModal')">取消</button>
                    <button type="button" class="btn btn-success" onclick="uploadImages()" id="img-upload-btn">
                        <i class="bi bi-cloud-upload me-1"></i>上传图片
                    </button>
                </div>
            </div>
        </div>
    </div>




            </div>
        </div>
    </div>

    <!-- 设备详情模态框 -->
    <div class="modal fade" id="equipmentDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="bi bi-gear"></i> 设备详情</h5>
                    <button type="button" class="btn-close" onclick="closeModal('equipmentDetailModal')"></button>
                </div>
                <div class="modal-body">
                    <div id="equipment-detail-content">
                        <!-- 设备详情内容将在这里动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('equipmentDetailModal')">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="editEquipment()">编辑设备</button>
                </div>
            </div>
        </div>
    </div>



    <!-- 模态框样式和函数定义 -->
    <style>
        /* 强制模态框隐藏样式 */
        .modal {
            z-index: 1050;
        }

        .modal-backdrop {
            z-index: 1040;
        }

        /* 强制隐藏模态框的CSS类 */
        .force-hidden {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            transform: translateY(-100vh) !important;
            pointer-events: none !important;
        }

        /* 移除body的modal-open类时的样式 */
        body.modal-closed {
            overflow: auto !important;
            padding-right: 0 !important;
        }
    </style>



    <!-- 自定义JS -->
    <script src="/static/js/simple-error-handler.js?v=20250715-stable"></script>
    <!-- 临时禁用复杂的错误处理器 -->
    <!-- <script src="/static/js/error-handler.js?v=20250715"></script> -->
    <!-- <script src="/static/js/syntax-checker.js?v=20250715"></script> -->
    <script src="/static/js/utils.js?v=20250714"></script>
    <script src="/static/js/modal-functions.js?v=20250717"></script>
    <script src="/static/js/simple-upload.js?v=20250718-v2"></script>

    <!-- 强制模态框关闭功能 -->
    <script>
        // 强制隐藏模态框函数 - 使用纯CSS样式
        window.forceHideModal = function(modalId) {
            console.log('🔧 强制隐藏模态框:', modalId);

            try {
                // 1. 获取模态框元素
                const modal = document.getElementById(modalId);
                if (!modal) {
                    console.error('❌ 找不到模态框:', modalId);
                    return;
                }

                // 2. 添加强制隐藏CSS类
                modal.classList.add('force-hidden');
                modal.classList.remove('show', 'fade');

                // 3. 设置内联样式确保隐藏
                modal.style.display = 'none';
                modal.style.visibility = 'hidden';
                modal.style.opacity = '0';
                modal.style.transform = 'translateY(-100vh)';
                modal.style.pointerEvents = 'none';

                // 4. 设置ARIA属性
                modal.setAttribute('aria-hidden', 'true');
                modal.removeAttribute('aria-modal');

                // 5. 移除body的modal相关类和样式
                document.body.classList.remove('modal-open');
                document.body.classList.add('modal-closed');
                document.body.style.overflow = 'auto';
                document.body.style.paddingRight = '0';

                // 6. 移除所有背景遮罩
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => {
                    backdrop.remove();
                });

                // 7. 重置表单
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                }

                // 8. 清理文件输入
                const fileInputs = modal.querySelectorAll('input[type="file"]');
                fileInputs.forEach(input => {
                    input.value = '';
                });

                // 9. 隐藏相关的信息显示元素
                if (modalId === 'addDocumentModal') {
                    const fileInfo = document.getElementById('doc-file-info');
                    const uploadProgress = document.getElementById('doc-upload-progress');
                    if (fileInfo) fileInfo.classList.add('d-none');
                    if (uploadProgress) uploadProgress.classList.add('d-none');
                } else if (modalId === 'addImageModal') {
                    const previewContainer = document.getElementById('img-preview-container');
                    const uploadProgress = document.getElementById('img-upload-progress');
                    if (previewContainer) previewContainer.classList.add('d-none');
                    if (uploadProgress) uploadProgress.classList.add('d-none');
                }

                console.log('✅ 模态框强制隐藏完成:', modalId);

            } catch (error) {
                console.error('❌ 强制隐藏模态框失败:', error);
            }
        };

        console.log('✅ forceHideModal函数已定义');
    </script>

    <!-- 预定义全局函数 -->
    <script>
        // 预定义知识库搜索函数，避免enhanced.js引用错误
        window.performEnhancedKnowledgeSearch = function(query) {
            console.log('知识库搜索功能将在页面完全加载后可用');
        };

        // 预定义其他可能缺失的函数
        window.showKnowledgeDetail = window.showKnowledgeDetail || function(index) {
            console.log('知识详情功能将在页面完全加载后可用');
        };

        window.copyFullContent = window.copyFullContent || function() {
            console.log('复制功能将在页面完全加载后可用');
        };

        window.downloadDocument = window.downloadDocument || function() {
            console.log('下载功能将在页面完全加载后可用');
        };

        window.loadEquipmentList = window.loadEquipmentList || function() {
            console.log('设备列表加载功能将在页面完全加载后可用');
        };

        // 预定义知识库管理函数 - 使用Bootstrap标准方式
        window.showAddDocumentModal = window.showAddDocumentModal || function() {
            console.log('🔧 显示添加文档模态框');
            const modalElement = document.getElementById('addDocumentModal');
            if (!modalElement) {
                console.error('❌ 找不到addDocumentModal元素');
                alert('错误：找不到文档添加模态框');
                return;
            }

            try {
                // 重置表单
                const form = document.getElementById('add-document-form');
                if (form) form.reset();

                // 使用Bootstrap Modal API显示模态框
                const modal = new bootstrap.Modal(modalElement);
                modal.show();

                // 重新初始化标签页状态
                setTimeout(() => {
                    reinitializeTabs();
                    switchDocTab('doc-upload');
                }, 100);

                console.log('✅ 文档模态框显示成功');
            } catch (error) {
                console.error('❌ 显示文档模态框失败:', error);
                alert('错误：无法显示文档添加模态框 - ' + error.message);
            }
        };

        window.showAddImageModal = window.showAddImageModal || function() {
            console.log('🔧 显示添加图片模态框');
            const modalElement = document.getElementById('addImageModal');
            if (!modalElement) {
                console.error('❌ 找不到addImageModal元素');
                alert('错误：找不到图片添加模态框');
                return;
            }

            try {
                // 重置表单
                const form = document.getElementById('add-image-form');
                if (form) form.reset();

                // 使用Bootstrap Modal API显示模态框
                const modal = new bootstrap.Modal(modalElement);
                modal.show();

                // 重新初始化标签页状态
                setTimeout(() => {
                    reinitializeTabs();
                    switchImgTab('img-upload');
                }, 100);

                console.log('✅ 图片模态框显示成功');
            } catch (error) {
                console.error('❌ 显示图片模态框失败:', error);
                alert('错误：无法显示图片添加模态框 - ' + error.message);
            }
        };

        // closeModal函数已移至页面底部，避免被其他代码覆盖

        // 确保全局变量存在
        window.currentTab = window.currentTab || 'fault-analysis';
        window.analysisHistory = window.analysisHistory || [];
        window.equipmentList = window.equipmentList || [];

        // CSRF Token管理器
        class CSRFTokenManager {
            constructor() {
                this.token = null;
                this.tokenExpiry = null;
            }

            async getToken() {
                // 如果token存在且未过期，直接返回
                if (this.token && this.tokenExpiry && Date.now() < this.tokenExpiry) {
                    return this.token;
                }

                try {
                    const response = await fetch('/api/v1/knowledge/csrf-token');
                    const result = await response.json();

                    if (result.success && result.data.csrf_token) {
                        this.token = result.data.csrf_token;
                        // 设置过期时间为50分钟后（服务器端是60分钟）
                        this.tokenExpiry = Date.now() + 50 * 60 * 1000;
                        return this.token;
                    }
                } catch (error) {
                    console.warn('获取CSRF token失败:', error);
                }

                return null;
            }

            async addTokenToHeaders(headers = {}) {
                const token = await this.getToken();
                if (token) {
                    headers['X-CSRF-Token'] = token;
                }
                return headers;
            }

            async addTokenToFormData(formData) {
                const token = await this.getToken();
                if (token) {
                    formData.append('csrf_token', token);
                }
                return formData;
            }
        }

        // 全局CSRF token管理器
        const csrfManager = new CSRFTokenManager();

        // 输入验证和清理工具
        class InputValidator {
            static sanitizeString(input) {
                if (typeof input !== 'string') return '';

                return input
                    .replace(/[<>]/g, '') // 移除尖括号
                    .replace(/javascript:/gi, '') // 移除javascript:协议
                    .replace(/on\w+=/gi, '') // 移除事件处理器
                    .trim();
            }

            static validateFileSize(file, maxSizeMB = 50) {
                if (!file) return { valid: false, error: '没有选择文件' };

                const maxSize = maxSizeMB * 1024 * 1024;
                if (file.size > maxSize) {
                    return {
                        valid: false,
                        error: `文件大小不能超过 ${maxSizeMB}MB，当前文件大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`
                    };
                }

                return { valid: true };
            }

            static validateFileType(file, allowedTypes) {
                if (!file) return { valid: false, error: '没有选择文件' };

                const fileType = file.type.toLowerCase();
                const fileName = file.name.toLowerCase();
                const fileExt = fileName.substring(fileName.lastIndexOf('.'));

                const isTypeAllowed = allowedTypes.some(type =>
                    fileType.includes(type.toLowerCase()) ||
                    fileExt === type.toLowerCase()
                );

                if (!isTypeAllowed) {
                    return {
                        valid: false,
                        error: `不支持的文件类型。支持的类型: ${allowedTypes.join(', ')}`
                    };
                }

                return { valid: true };
            }

            static validateRequiredFields(fields) {
                const errors = [];

                for (const [fieldName, value] of Object.entries(fields)) {
                    if (!value || value.trim() === '') {
                        errors.push(`${fieldName} 不能为空`);
                    }
                }

                return {
                    valid: errors.length === 0,
                    errors: errors
                };
            }

            static sanitizeMetadata(metadata) {
                const sanitized = {};

                for (const [key, value] of Object.entries(metadata)) {
                    if (typeof value === 'string') {
                        sanitized[key] = this.sanitizeString(value);
                    } else if (Array.isArray(value)) {
                        sanitized[key] = value.map(item =>
                            typeof item === 'string' ? this.sanitizeString(item) : item
                        );
                    } else {
                        sanitized[key] = value;
                    }
                }

                return sanitized;
            }
        }

        // 上传进度管理器
        class UploadProgressManager {
            constructor(progressContainerId, progressBarId, progressTextId, statusTextId) {
                this.progressContainer = document.getElementById(progressContainerId);
                this.progressBar = document.getElementById(progressBarId);
                this.progressText = document.getElementById(progressTextId);
                this.statusText = document.getElementById(statusTextId);
            }

            show() {
                if (this.progressContainer) {
                    this.progressContainer.style.display = 'block';
                }
            }

            hide() {
                if (this.progressContainer) {
                    this.progressContainer.style.display = 'none';
                }
            }

            updateProgress(progress, status = '') {
                const percentage = Math.round(progress * 100);

                if (this.progressBar) {
                    this.progressBar.style.width = `${percentage}%`;
                    this.progressBar.setAttribute('aria-valuenow', percentage);
                }

                if (this.progressText) {
                    this.progressText.textContent = `${percentage}%`;
                }

                if (this.statusText && status) {
                    this.statusText.textContent = status;
                }
            }

            setComplete(message = '上传完成') {
                this.updateProgress(1.0, message);
                if (this.progressBar) {
                    this.progressBar.classList.remove('progress-bar-animated');
                    this.progressBar.classList.add('bg-success');
                }
            }

            setError(message = '上传失败') {
                if (this.statusText) {
                    this.statusText.textContent = message;
                    this.statusText.classList.add('text-danger');
                }
                if (this.progressBar) {
                    this.progressBar.classList.remove('progress-bar-animated');
                    this.progressBar.classList.add('bg-danger');
                }
            }
        }

        // 任务状态监控器
        class TaskStatusMonitor {
            constructor(containerId, progressBarId, progressTextId, statusTextId, taskIdTextId) {
                this.container = document.getElementById(containerId);
                this.progressBar = document.getElementById(progressBarId);
                this.progressText = document.getElementById(progressTextId);
                this.statusText = document.getElementById(statusTextId);
                this.taskIdText = document.getElementById(taskIdTextId);
                this.taskIdDisplay = document.getElementById(taskIdTextId.replace('-text', '-display'));
                this.pollInterval = null;
            }

            show(taskId) {
                if (this.container) {
                    this.container.style.display = 'block';
                }
                if (this.taskIdText && taskId) {
                    this.taskIdText.textContent = taskId;
                    if (this.taskIdDisplay) {
                        this.taskIdDisplay.style.display = 'block';
                    }
                }
                this.startPolling(taskId);
            }

            hide() {
                if (this.container) {
                    this.container.style.display = 'none';
                }
                this.stopPolling();
            }

            updateProgress(progress, status = '') {
                const percentage = Math.round(progress * 100);

                if (this.progressBar) {
                    this.progressBar.style.width = `${percentage}%`;
                    this.progressBar.setAttribute('aria-valuenow', percentage);
                }

                if (this.progressText) {
                    this.progressText.textContent = `${percentage}%`;
                }

                if (this.statusText && status) {
                    this.statusText.textContent = status;
                }
            }

            async startPolling(taskId) {
                if (!taskId) return;

                this.stopPolling();
                this.pollInterval = setInterval(async () => {
                    try {
                        const response = await fetch(`/api/v1/knowledge/tasks/${taskId}`);
                        const result = await response.json();

                        if (result.success && result.data) {
                            const taskInfo = result.data;
                            this.updateProgress(taskInfo.progress || 0, taskInfo.message || '');

                            // 如果任务完成或失败，停止轮询
                            if (taskInfo.status === 'completed' || taskInfo.status === 'failed' || taskInfo.status === 'cancelled') {
                                this.stopPolling();

                                if (taskInfo.status === 'completed') {
                                    this.setComplete(taskInfo.message || '处理完成');
                                } else if (taskInfo.status === 'failed') {
                                    this.setError(taskInfo.error || '处理失败');
                                } else {
                                    this.setError('任务已取消');
                                }
                            }
                        }
                    } catch (error) {
                        console.error('轮询任务状态失败:', error);
                    }
                }, 1000); // 每秒轮询一次
            }

            stopPolling() {
                if (this.pollInterval) {
                    clearInterval(this.pollInterval);
                    this.pollInterval = null;
                }
            }

            setComplete(message = '处理完成') {
                this.updateProgress(1.0, message);
                if (this.progressBar) {
                    this.progressBar.classList.add('bg-success');
                }
            }

            setError(message = '处理失败') {
                if (this.statusText) {
                    this.statusText.textContent = message;
                    this.statusText.classList.add('text-danger');
                }
                if (this.progressBar) {
                    this.progressBar.classList.add('bg-danger');
                }
            }
        }

        // 简化的文件上传处理器
        class SimpleFileUploader {
            constructor(fileType = 'document') {
                this.fileType = fileType; // 'document' 或 'image'
                this.isProcessing = false;
            }

            async startUpload() {
                if (this.isProcessing) {
                    console.log('正在处理中，请稍候...');
                    return;
                }

                console.log(`📤 开始上传${this.fileType === 'document' ? '文档' : '图片'}`);

                try {
                    this.isProcessing = true;
                    this.showLoadingState();

                    // 收集用户输入的基本信息
                    const basicInfo = this.collectBasicInfo();
                    console.log('收集的基本信息:', basicInfo);

                    // 验证输入
                    const validation = this.validateInput(basicInfo);
                    if (!validation.valid) {
                        throw new Error(validation.error);
                    }

                    // 直接上传到后端
                    const result = await this.uploadToBackend(basicInfo);
                    this.showSuccess(result);

                } catch (error) {
                    console.error('上传失败:', error);
                    this.showError(error.message);
                } finally {
                    this.isProcessing = false;
                    this.hideLoadingState();
                }
            }

            showLoadingState() {
                const submitBtn = document.getElementById(this.fileType === 'document' ? 'save-document-btn' : 'save-image-btn');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = `<i class="bi bi-hourglass-split me-2"></i>上传中...`;
                }

                // 显示简单的加载提示
                const loadingHtml = `
                    <div class="alert alert-info d-flex align-items-center" id="upload-loading">
                        <div class="spinner-border spinner-border-sm me-3" role="status"></div>
                        <div>正在上传${this.fileType === 'document' ? '文档' : '图片'}，请稍候...</div>
                    </div>
                `;

                const container = document.querySelector(`#${this.fileType === 'document' ? 'add-document-form' : 'add-image-form'}`);
                if (container) {
                    container.insertAdjacentHTML('afterbegin', loadingHtml);
                }
            }

            hideLoadingState() {
                const submitBtn = document.getElementById(this.fileType === 'document' ? 'save-document-btn' : 'save-image-btn');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = `<i class="bi bi-save me-2"></i>保存${this.fileType === 'document' ? '文档' : '图片'}`;
                }

                // 移除加载提示
                const loadingElement = document.getElementById('upload-loading');
                if (loadingElement) {
                    loadingElement.remove();
                }
            }

            // 简化的上传方法 - 直接调用后端API
            async uploadToBackend(basicInfo) {
                console.log('开始上传到后端:', basicInfo.fileInput.files[0].name);

                const file = basicInfo.fileInput.files[0];
                const formData = new FormData();

                // 根据文件类型构建FormData
                if (this.fileType === 'document') {
                    // 文档API期望的格式
                    formData.append('file', file);
                    formData.append('metadata', JSON.stringify({
                        title: basicInfo.title,
                        category: basicInfo.category,
                        content: basicInfo.content,
                        description: basicInfo.description,
                        tags: basicInfo.tags,
                        equipment: basicInfo.equipment,
                        equipment_type: basicInfo.equipmentType,
                        voltage: basicInfo.voltage,
                        fault_type: basicInfo.faultType,
                        location: basicInfo.location
                    }));
                } else {
                    // 图片API期望的格式
                    formData.append('files', file);
                    formData.append('title', basicInfo.title);
                    formData.append('category', basicInfo.category);
                    formData.append('equipment_type', basicInfo.equipmentType);
                    formData.append('equipment', basicInfo.equipment);
                    formData.append('location', basicInfo.location);
                    formData.append('description', basicInfo.description);
                    formData.append('tags', basicInfo.tags);
                    formData.append('annotations', JSON.stringify([]));
                }

                // 选择正确的API端点
                const apiEndpoint = this.fileType === 'document'
                    ? '/api/v1/knowledge/documents/add'
                    : '/api/v1/knowledge/images/add';

                console.log('发送请求到:', apiEndpoint);
                const response = await fetch(apiEndpoint, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`上传失败: ${response.status} - ${errorText}`);
                }

                const result = await response.json();
                console.log('上传结果:', result);

                if (!result.success) {
                    throw new Error(result.message || result.error || '上传失败');
                }

                return result;
            }

            collectBasicInfo() {
                const prefix = this.fileType === 'document' ? 'doc' : 'img';
                console.log(`收集${this.fileType}基本信息，前缀: ${prefix}`);

                const basicInfo = {
                    title: document.getElementById(`${prefix}-title`)?.value || '',
                    category: document.getElementById(`${prefix}-category`)?.value || '',
                    content: document.getElementById(`${prefix}-content`)?.value || '',
                    description: document.getElementById(`${prefix}-description`)?.value || '',
                    tags: document.getElementById(`${prefix}-tags`)?.value || '',
                    equipment: document.getElementById(`${prefix}-equipment`)?.value || '',
                    equipmentType: document.getElementById(`${prefix}-equipment-type`)?.value || '',
                    voltage: document.getElementById(`${prefix}-voltage`)?.value || '',
                    faultType: document.getElementById(`${prefix}-fault-type`)?.value || '',
                    location: document.getElementById(`${prefix}-location`)?.value || '',
                    fileInput: document.getElementById(`${prefix}-file`)
                };

                console.log('收集到的基本信息:', basicInfo);
                console.log('文件输入元素:', basicInfo.fileInput);
                console.log('选择的文件:', basicInfo.fileInput?.files);

                return basicInfo;
            }

            validateInput(basicInfo) {
                // 必填字段验证
                const requiredFields = { '标题': basicInfo.title, '类别': basicInfo.category };
                const fieldValidation = InputValidator.validateRequiredFields(requiredFields);

                if (!fieldValidation.valid) {
                    return { valid: false, error: '请填写必填字段:\n' + fieldValidation.errors.join('\n') };
                }

                // 文件验证
                if (!basicInfo.fileInput || !basicInfo.fileInput.files || basicInfo.fileInput.files.length === 0) {
                    return { valid: false, error: `请选择要上传的${this.fileType === 'document' ? '文档' : '图片'}` };
                }

                const file = basicInfo.fileInput.files[0];

                // 文件大小验证
                const maxSize = this.fileType === 'document' ? 50 : 20; // 文档50MB，图片20MB
                const sizeValidation = InputValidator.validateFileSize(file, maxSize);
                if (!sizeValidation.valid) {
                    return { valid: false, error: sizeValidation.error };
                }

                // 文件类型验证
                const allowedTypes = this.fileType === 'document'
                    ? ['.pdf', '.doc', '.docx', '.txt', '.md', '.json', '.csv']
                    : ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'];

                const typeValidation = InputValidator.validateFileType(file, allowedTypes);
                if (!typeValidation.valid) {
                    return { valid: false, error: typeValidation.error };
                }

                return { valid: true };
            }

            // 删除复杂的多步骤处理流程，已简化为直接上传

            // 删除重复的处理方法，已整合到uploadToBackend中

            showSuccess(result) {
                const fileTypeName = this.fileType === 'document' ? '文档' : '图片';
                const message = `${fileTypeName}已成功添加到知识库！`;

                // 显示成功提示
                const successHtml = `
                    <div class="alert alert-success d-flex align-items-center" id="upload-success">
                        <i class="bi bi-check-circle-fill me-3"></i>
                        <div>${message}</div>
                    </div>
                `;

                const container = document.querySelector(`#${this.fileType === 'document' ? 'add-document-form' : 'add-image-form'}`);
                if (container) {
                    container.insertAdjacentHTML('afterbegin', successHtml);
                }

                // 3秒后关闭模态框并重置表单
                setTimeout(() => {
                    const modalId = this.fileType === 'document' ? 'addDocumentModal' : 'addImageModal';
                    window.closeModal(modalId);

                    const formId = this.fileType === 'document' ? 'add-document-form' : 'add-image-form';
                    const form = document.getElementById(formId);
                    if (form) form.reset();

                    // 移除成功提示
                    const successElement = document.getElementById('upload-success');
                    if (successElement) successElement.remove();

                    // 刷新知识库列表
                    if (window.loadKnowledgeBase) {
                        window.loadKnowledgeBase();
                    }
                }, 3000);
            }

            showError(errorMessage) {
                // 显示错误提示
                const errorHtml = `
                    <div class="alert alert-danger d-flex align-items-center" id="upload-error">
                        <i class="bi bi-exclamation-triangle-fill me-3"></i>
                        <div>上传失败: ${errorMessage}</div>
                    </div>
                `;

                const container = document.querySelector(`#${this.fileType === 'document' ? 'add-document-form' : 'add-image-form'}`);
                if (container) {
                    container.insertAdjacentHTML('afterbegin', errorHtml);
                }

                // 5秒后移除错误提示
                setTimeout(() => {
                    const errorElement = document.getElementById('upload-error');
                    if (errorElement) errorElement.remove();
                }, 5000);
            }
        }

        // 更新文档和图片处理函数以使用新的统一处理器
        window.addDocumentWithAnnotation = window.addDocumentWithAnnotation || async function() {
            console.log('📤 启动文档上传');
            try {
                const uploader = new SimpleFileUploader('document');
                await uploader.startUpload();
            } catch (error) {
                console.error('文档上传失败:', error);
                alert('文档上传失败: ' + error.message);
            }
        };

        window.addImageWithAnnotation = window.addImageWithAnnotation || async function() {
            console.log('📤 启动图片上传');
            try {
                const uploader = new SimpleFileUploader('image');
                await uploader.startUpload();
            } catch (error) {
                console.error('图片上传失败:', error);
                alert('图片上传失败: ' + error.message);
            }
        };

        // 数据清洗预览功能
        window.cleanDocumentData = window.cleanDocumentData || async function() {
            console.log('🧹 开始数据清洗预览');

            const content = document.getElementById('doc-content')?.value || '';
            if (!content.trim()) {
                alert('请先输入文档内容');
                return;
            }

            const cleaningConfig = {
                normalize_text: document.getElementById('clean-format')?.checked || false,
                standardize_terms: document.getElementById('clean-terminology')?.checked || false,
                standardize_units: document.getElementById('clean-units')?.checked || false,
                remove_duplicates: document.getElementById('remove-duplicates')?.checked || false,
                output_format: document.getElementById('output-format')?.value || 'json'
            };

            try {
                const response = await fetch('/api/v1/knowledge/data/clean-preview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: content,
                        config: cleaningConfig
                    })
                });

                const result = await response.json();

                if (result.success) {
                    // 显示清洗预览
                    const previewDiv = document.getElementById('cleaning-preview');
                    if (previewDiv) {
                        previewDiv.innerHTML = `
                            <div class="mb-3">
                                <h6>清洗前:</h6>
                                <div class="bg-light p-2 rounded small">${content.substring(0, 200)}...</div>
                            </div>
                            <div class="mb-3">
                                <h6>清洗后:</h6>
                                <div class="bg-success bg-opacity-10 p-2 rounded small">${result.cleaned_content.substring(0, 200)}...</div>
                            </div>
                        `;
                    }

                    // 显示清洗统计
                    const statsDiv = document.getElementById('cleaning-stats');
                    if (statsDiv && result.stats) {
                        statsDiv.innerHTML = `
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="text-primary fw-bold">${result.stats.terms_standardized || 0}</div>
                                    <div class="small">术语标准化</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-success fw-bold">${result.stats.units_standardized || 0}</div>
                                    <div class="small">单位标准化</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-warning fw-bold">${result.stats.duplicates_removed || 0}</div>
                                    <div class="small">重复移除</div>
                                </div>
                                <div class="col-3">
                                    <div class="text-info fw-bold">${result.stats.quality_score || 0}%</div>
                                    <div class="small">质量评分</div>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    alert('清洗预览失败: ' + result.error);
                }
            } catch (error) {
                console.error('清洗预览失败:', error);
                alert('清洗预览失败: ' + error.message);
            }
        };

        window.addImageWithAnnotation = window.addImageWithAnnotation || async function() {
            console.log('🔧 开始添加图片（增强版本）');

            const title = document.getElementById('img-title')?.value || '';
            const category = document.getElementById('img-category')?.value || '';
            const equipmentType = document.getElementById('img-equipment-type')?.value || '';
            const equipment = document.getElementById('img-equipment')?.value || '';
            const location = document.getElementById('img-location')?.value || '';
            const description = document.getElementById('img-description')?.value || '';
            const tags = document.getElementById('img-tags')?.value || '';
            const fileInput = document.getElementById('img-file');

            // 输入验证
            const requiredFields = { '标题': title, '类别': category };
            const fieldValidation = InputValidator.validateRequiredFields(requiredFields);

            if (!fieldValidation.valid) {
                alert('请填写必填字段:\n' + fieldValidation.errors.join('\n'));
                return;
            }

            // 文件验证
            if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                alert('请选择要上传的图片');
                return;
            }

            const file = fileInput.files[0];

            // 文件大小验证
            const sizeValidation = InputValidator.validateFileSize(file, 20); // 图片限制20MB
            if (!sizeValidation.valid) {
                alert(sizeValidation.error);
                return;
            }

            // 文件类型验证
            const allowedTypes = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'];
            const typeValidation = InputValidator.validateFileType(file, allowedTypes);
            if (!typeValidation.valid) {
                alert(typeValidation.error);
                return;
            }

            // 初始化进度管理器
            const uploadProgress = new UploadProgressManager(
                'image-upload-progress-container',
                'image-upload-progress-bar',
                'image-upload-progress-text',
                'image-upload-status-text'
            );

            const taskMonitor = new TaskStatusMonitor(
                'image-task-status-container',
                'image-task-progress-bar',
                'image-task-progress-text',
                'image-task-status-text',
                'image-task-id-text'
            );

            try {
                uploadProgress.show();
                uploadProgress.updateProgress(0.1, '准备上传图片...');

                const formData = new FormData();
                formData.append('image', file);

                // 构建和清理元数据
                const rawMetadata = {
                    title: title,
                    category: category,
                    equipment_type: equipmentType,
                    equipment: equipment,
                    location: location,
                    description: description,
                    tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
                    annotations: [], // 获取标注数据
                    ocr_enabled: true,
                    defect_detection_enabled: true,
                    image_enhancement_enabled: true
                };

                // 清理元数据以防止XSS攻击
                const metadata = InputValidator.sanitizeMetadata(rawMetadata);

                formData.append('metadata', JSON.stringify(metadata));
                formData.append('async_processing', 'true'); // 启用异步处理

                // 添加CSRF token
                await csrfManager.addTokenToFormData(formData);

                uploadProgress.updateProgress(0.3, '开始上传图片...');

                // 使用XMLHttpRequest以支持上传进度
                const xhr = new XMLHttpRequest();

                const uploadPromise = new Promise((resolve, reject) => {
                    xhr.upload.addEventListener('progress', (e) => {
                        if (e.lengthComputable) {
                            const progress = 0.3 + (e.loaded / e.total) * 0.4; // 30%-70%为上传进度
                            uploadProgress.updateProgress(progress, `上传中... ${Math.round((e.loaded / e.total) * 100)}%`);
                        }
                    });

                    xhr.addEventListener('load', () => {
                        if (xhr.status === 200) {
                            uploadProgress.updateProgress(0.8, '上传完成，开始处理...');
                            resolve(JSON.parse(xhr.responseText));
                        } else {
                            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                        }
                    });

                    xhr.addEventListener('error', () => {
                        reject(new Error('网络错误'));
                    });

                    xhr.open('POST', '/api/v1/knowledge/images/add');
                    xhr.send(formData);
                });

                const result = await uploadPromise;

                if (result.success) {
                    uploadProgress.setComplete('上传完成');

                    // 如果是异步处理，显示任务监控
                    if (result.data && result.data.task_id) {
                        taskMonitor.show(result.data.task_id);

                        let message = '图片已开始异步处理！';
                        if (result.data.processing_status === 'async') {
                            message += `\n任务ID: ${result.data.task_id}`;
                        }
                        alert(message);
                    } else {
                        // 同步处理完成
                        alert('图片添加成功！已启用智能标注和图像处理');
                        window.closeModal('addImageModal');
                        // 重置表单
                        const form = document.getElementById('add-image-form');
                        if (form) form.reset();
                    }
                } else {
                    uploadProgress.setError('上传失败');
                    taskMonitor.hide();
                    alert('添加失败: ' + result.error);
                }
            } catch (error) {
                console.error('添加图片失败:', error);
                uploadProgress.setError('上传失败: ' + error.message);
                taskMonitor.hide();
                alert('添加图片失败: ' + error.message);
            } finally {
                // 确保在完成后隐藏进度条（延迟隐藏以便用户看到结果）
                setTimeout(() => {
                    uploadProgress.hide();
                }, 3000);
            }
        };

        // 🔧 添加缺失的 showAlert 函数
        window.showAlert = window.showAlert || function(message, type = 'info') {
            console.log(`🔔 Alert [${type}]: ${message}`);

            // 创建alert元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;

            // 添加到页面
            document.body.appendChild(alertDiv);

            // 3秒后自动移除
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.remove();
                }
            }, 3000);
        };



        console.log('✅ 全局函数预定义完成');
    </script>

    <!-- 使用稳定的最小化版本 -->
    <script src="/static/js/main-minimal.js?v=20250715-stable"></script>
    <script src="/static/js/enhanced.js?v=20250721"></script>
    <!-- <script src="/static/js/main.js?v=20250715-comprehensive"></script> -->

    <!-- 简化的标签页功能和页面加载完成提示 -->
    <script>
        // 备用函数定义 - 确保函数在全局作用域中可用
        window.showKnowledgeDetail = window.showKnowledgeDetail || function(index) {
            if (!window.currentSearchResults || !window.currentSearchResults[index]) {
                alert('无法获取详情信息');
                return;
            }

            const result = window.currentSearchResults[index];
            const content = result.content || result.text || '无内容';
            const title = result.title || '知识库详情';

            // 创建简单的模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 9999; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; padding: 20px; border-radius: 8px; max-width: 80%; max-height: 80%; overflow-y: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h5>${title}</h5>
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove()" style="border: none; background: none; font-size: 20px; cursor: pointer;">&times;</button>
                    </div>
                    <div style="white-space: pre-wrap; font-family: monospace; font-size: 14px; line-height: 1.5;">
                        ${content}
                    </div>
                    <div style="margin-top: 15px; text-align: center;">
                        <button onclick="navigator.clipboard.writeText('${content.replace(/'/g, '\\\'')}')" style="padding: 5px 10px; margin-right: 10px;">复制内容</button>
                        <button onclick="this.closest('[style*=\"position: fixed\"]').remove()" style="padding: 5px 10px;">关闭</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        };

        window.copyFullContent = window.copyFullContent || function() {
            const contentElement = document.querySelector('#knowledgeDetailModal pre');
            if (contentElement) {
                navigator.clipboard.writeText(contentElement.textContent).then(() => {
                    alert('内容已复制到剪贴板');
                }).catch(() => {
                    alert('复制失败');
                });
            } else {
                alert('无法找到内容');
            }
        };

        window.downloadDocument = window.downloadDocument || function(source, title) {
            alert('下载功能开发中');
        };
    </script>
    <script>
        // 简单的标签页切换功能（不依赖Bootstrap）
        function showTab(tabId) {
            // 隐藏所有主标签页内容（不影响故障分析步骤的tab-content）
            const allTabs = document.querySelectorAll('.main-tab-content');
            allTabs.forEach(tab => tab.classList.remove('active'));

            // 移除所有导航链接的active类
            const allNavLinks = document.querySelectorAll('.nav-link');
            allNavLinks.forEach(link => link.classList.remove('active'));

            // 显示选中的主标签页
            const targetTab = document.getElementById(tabId);
            if (targetTab && targetTab.classList.contains('main-tab-content')) {
                targetTab.classList.add('active');
            }

            // 激活对应的导航链接
            const activeLink = document.querySelector(`[data-tab="${tabId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
            }
        }

        // 步骤标签页切换功能
        function showStep(stepId) {
            // 隐藏所有步骤内容
            const allSteps = document.querySelectorAll('.tab-pane');
            allSteps.forEach(step => step.classList.remove('active'));

            // 移除所有步骤链接的active类
            const allStepLinks = document.querySelectorAll('#fault-analysis-tabs .nav-link');
            allStepLinks.forEach(link => link.classList.remove('active'));

            // 显示选中的步骤
            const targetStep = document.getElementById(stepId);
            if (targetStep) {
                targetStep.classList.add('active');
            }

            // 激活对应的步骤链接
            const activeStepLink = document.querySelector(`[href="#${stepId}"]`);
            if (activeStepLink) {
                activeStepLink.classList.add('active');
            }
        }

        // 模态框功能
        function showModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('show');
            }
        }



        // 导航栏切换功能
        function toggleNavbar() {
            const navbar = document.getElementById('navbarNav');
            if (navbar) {
                navbar.classList.toggle('show');
            }
        }

        // 筛选器切换功能
        function toggleFilters() {
            const filters = document.getElementById('equipment-filters');
            if (filters) {
                filters.classList.toggle('show');
            }
        }

        // AI分析功能
        let selectedModel = 'r1'; // 默认选择DeepSeek-R1模型

        // 模型选择按钮处理
        function initModelSelection() {
            // 默认选中DeepSeek-R1按钮
            const defaultBtn = document.getElementById('deepseekR1Btn');
            if (defaultBtn) {
                defaultBtn.classList.add('active');
            }

            // 设置默认分析按钮文本
            const analyzeBtn = document.getElementById('ai-search-btn');
            if (analyzeBtn) {
                analyzeBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-R1 推理';
            }

            document.querySelectorAll('.model-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有按钮的active状态
                    document.querySelectorAll('.model-btn').forEach(b => b.classList.remove('active'));

                    // 添加当前按钮的active状态
                    this.classList.add('active');

                    // 设置选中的模型
                    selectedModel = this.dataset.model;

                    // 更新分析按钮文本
                    const analyzeBtn = document.getElementById('ai-search-btn');
                    if (selectedModel === 'v3') {
                        analyzeBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-V3 分析';
                    } else if (selectedModel === 'r1') {
                        analyzeBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-R1 推理';
                    }
                });
            });
        }

        // AI分析处理
        function initAIAnalysis() {
            const aiSearchBtn = document.getElementById('ai-search-btn');
            const aiSearchInput = document.getElementById('ai-search-input');
            const aiSearchResults = document.getElementById('ai-search-results');
            const aiAnalysisContent = document.getElementById('ai-analysis-content');

            if (aiSearchBtn) {
                aiSearchBtn.addEventListener('click', async function() {
                    const query = aiSearchInput.value.trim();

                    if (!query) {
                        alert('请输入故障描述');
                        return;
                    }

                    if (!selectedModel) {
                        alert('请先选择AI模型');
                        return;
                    }

                    const isThinkingMode = selectedModel === 'r1';

                    // 显示加载状态
                    aiSearchBtn.disabled = true;
                    aiSearchBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> ' + (isThinkingMode ? '深度推理中...' : '分析中...');

                    try {
                        if (isThinkingMode) {
                            // DeepSeek-R1 使用流式响应
                            await performStreamingAnalysis(query, aiAnalysisContent);
                            aiSearchResults.style.display = 'block';
                        } else {
                            // DeepSeek-V3 也使用流式响应，但不显示思考过程
                            await performStreamingAnalysis(query, aiAnalysisContent, false);
                            aiSearchResults.style.display = 'block';
                        }

                    } catch (error) {
                        console.error('AI分析请求失败:', error);
                        alert('分析请求失败，请检查网络连接');
                    } finally {
                        // 恢复按钮状态
                        aiSearchBtn.disabled = false;
                        if (selectedModel === 'v3') {
                            aiSearchBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-V3 分析';
                        } else if (selectedModel === 'r1') {
                            aiSearchBtn.innerHTML = '<i class="bi bi-robot"></i> DeepSeek-R1 推理';
                        }
                    }
                });
            }
        }



        // 流式分析函数 - 支持DeepSeek-R1和DeepSeek-V3
        async function performStreamingAnalysis(query, aiAnalysisContent, showThinking = true) {
            try {
                // 显示结果区域
                const aiSearchResults = document.getElementById('ai-search-results');
                if (aiSearchResults) {
                    aiSearchResults.style.display = 'block';
                }

                // 清空之前的内容 - 根据模型类型调整界面
                if (showThinking) {
                    // DeepSeek-R1 模式
                    aiAnalysisContent.innerHTML = `
                        <div class="mb-3">
                            <strong>🤖 模型:</strong> DeepSeek-R1 (深度推理模式)
                        </div>
                    <div class="mb-3">
                        <strong>🧠 专家推理过程:</strong>
                        <div class="text-muted small mb-2">正在展现电力系统专家的完整思维过程...</div>
                        <div id="reasoning-stream" class="reasoning-process" style="
                            margin-top: 0.5rem;
                            padding: 1.2rem;
                            background-color: #f8f9fa;
                            border-left: 4px solid #0d6efd;
                            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                            white-space: normal;
                            min-height: 200px;
                            max-height: 800px;
                            font-size: 14px;
                            line-height: 1.8;
                            border-radius: 0 4px 4px 0;
                            overflow-y: auto;
                            color: #495057;
                            border: 1px solid #dee2e6;
                            word-wrap: break-word;
                            overflow-wrap: break-word;
                            display: block;
                            visibility: visible;
                        ">
                            <span class="text-muted">🔍 正在连接DeepSeek-R1，准备开始深度推理...</span>
                        </div>
                    </div>
                    <div class="mb-3" id="final-analysis-container" style="display: none;">
                        <strong>📋 基于推理的详细诊断报告:</strong>
                        <div class="text-muted small mb-2">基于上述推理过程，生成更加详细和具体的故障诊断结果</div>
                        <div id="final-stream-r1" style="
                            margin-top: 0.5rem;
                            padding: 1.2rem;
                            border-radius: 0.25rem;
                            background-color: #e8f5e8;
                            border-left: 4px solid #198754;
                            font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                            white-space: pre-wrap;
                            min-height: 100px;
                            max-height: 600px;
                            font-size: 14px;
                            line-height: 1.6;
                            overflow-y: auto;
                            color: #212529;
                            word-wrap: break-word;
                            overflow-wrap: break-word;
                        ">
                        </div>
                    </div>
                `;
                } else {
                    // DeepSeek-V3 模式
                    aiAnalysisContent.innerHTML = `
                        <div class="mb-3">
                            <strong>🤖 模型:</strong> DeepSeek-V3 (实时分析模式)
                        </div>
                        <div class="mb-3">
                            <strong>🔍 实时分析结果:</strong>
                            <div class="text-muted small mb-2">正在进行专业故障分析，实时显示分析结果</div>
                            <div id="final-stream-v3" style="
                                margin-top: 0.5rem;
                                padding: 1.2rem;
                                border-radius: 0.25rem;
                                background-color: #e7f3ff;
                                border-left: 4px solid #0d6efd;
                                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                                white-space: pre-wrap;
                                min-height: 200px;
                                max-height: 800px;
                                font-size: 14px;
                                line-height: 1.6;
                                overflow-y: auto;
                                color: #212529;
                                word-wrap: break-word;
                                overflow-wrap: break-word;
                            ">
                                <span class="text-muted">🔍 正在连接DeepSeek-V3，准备开始分析...</span>
                            </div>
                        </div>
                    `;
                }

                // 等待DOM更新后再获取元素引用
                await new Promise(resolve => setTimeout(resolve, 100));

                // 在设置HTML后重新获取元素引用
                let reasoningDiv = document.getElementById('reasoning-stream');
                let finalDiv = document.getElementById(showThinking ? 'final-stream-r1' : 'final-stream-v3');
                let finalContainer = document.getElementById('final-analysis-container');

                // 调试信息：检查元素是否存在
                console.log('🔍 元素检查:', {
                    showThinking: showThinking,
                    reasoningDiv: !!reasoningDiv,
                    finalDiv: !!finalDiv,
                    finalContainer: !!finalContainer
                });

                // 如果元素仍然不存在，再次尝试获取
                if (!finalDiv) {
                    console.log('⚠️ finalDiv不存在，尝试重新获取...');
                    await new Promise(resolve => setTimeout(resolve, 200));
                    finalDiv = document.getElementById(showThinking ? 'final-stream-r1' : 'final-stream-v3');
                    console.log('🔍 重新获取finalDiv:', !!finalDiv);
                }

                // 清空之前的内容 - 添加null检查
                if (showThinking && reasoningDiv) {
                    reasoningDiv.innerHTML = '<span class="text-muted">正在连接DeepSeek-R1...</span>';
                    if (finalDiv) {
                        finalDiv.innerHTML = '';
                    }
                    if (finalContainer) {
                        finalContainer.style.display = 'none';
                    }
                } else if (finalDiv) {
                    finalDiv.innerHTML = '<span class="text-muted">🔍 正在连接DeepSeek-V3，准备开始分析...</span>';
                }

                console.log('🌊 开始流式分析请求:', query);

                // 使用fetch的流式处理（EventSource不支持POST）
                const response = await fetch('/api/v1/analyze_stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        thinking_mode: showThinking
                    })
                });

                console.log('🌊 流式响应状态:', response.status, response.statusText);

                if (!response.ok) {
                    throw new Error(`流式请求失败: ${response.status} ${response.statusText}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let chunkCount = 0;

                // 只在DeepSeek-R1模式下清空推理区域
                if (showThinking && reasoningDiv) {
                    reasoningDiv.innerHTML = '';
                }

                // 重置最终答案区域
                window.finalAnswerDiv = null;
                const existingFinalDiv = document.getElementById('final-answer-stream');
                if (existingFinalDiv) {
                    existingFinalDiv.remove();
                }

                console.log('🌊 开始读取流式数据...');

                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        console.log('🌊 流式读取完成，总共处理', chunkCount, '个数据块');
                        break;
                    }

                    chunkCount++;
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留不完整的行

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            try {
                                const data = JSON.parse(line.slice(6));
                                console.log('🌊 收到数据:', data.type, '长度:', data.content ? data.content.length : 0);

                                if (data.type === 'reasoning' && showThinking) {
                                    // DeepSeek-R1推理过程处理 - 官方标准格式
                                    let cleanContent = data.content || '';

                                    // 添加详细调试信息
                                    console.log('🧠 推理过程调试:', {
                                        原始内容: data.content,
                                        内容长度: data.content ? data.content.length : 0,
                                        showThinking: showThinking,
                                        reasoningDiv存在: !!reasoningDiv,
                                        reasoningDiv元素: reasoningDiv
                                    });

                                    // 安全检查：确保content存在
                                    if (!data.content) {
                                        console.log('⚠️ 推理内容为空，跳过处理');
                                        continue;
                                    }

                                    // 清理官方标准标签，保持推理内容的自然表达
                                    try {
                                        cleanContent = cleanContent
                                            .replace(/<think>/gi, '')
                                            .replace(/<\/think>/gi, '')
                                            .replace(/<thinking>/gi, '')
                                            .replace(/<\/thinking>/gi, '')
                                            .replace(/<answer>/gi, '')
                                            .replace(/<\/answer>/gi, '')
                                            .trim();
                                    } catch (error) {
                                        console.error('❌ 清理推理内容时出错:', error);
                                        cleanContent = data.content || '';
                                    }

                                    // 格式化为自然文本，避免段落分割问题
                                    const formatNaturalText = (text) => {
                                        if (!text || typeof text !== 'string') {
                                            return '';
                                        }

                                        try {
                                            // 流式输出时保持简单格式，避免段落分割
                                            return text
                                                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')  // 保留粗体
                                                .replace(/\n/g, ' ');  // 将换行转为空格，保持流畅
                                        } catch (error) {
                                            console.error('❌ 格式化推理文本时出错:', error);
                                            return text;
                                        }
                                    };

                                    let formattedContent = '';
                                    try {
                                        formattedContent = formatNaturalText(cleanContent);
                                    } catch (error) {
                                        console.error('❌ 调用formatNaturalText时出错:', error);
                                        formattedContent = cleanContent;
                                    }

                                    if (reasoningDiv && cleanContent && cleanContent.length > 0) {
                                        console.log('✅ 正在添加推理内容到DOM:', {
                                            清理后内容: cleanContent,
                                            格式化内容: formattedContent,
                                            当前HTML长度: reasoningDiv.innerHTML.length
                                        });

                                        try {
                                            // 确保推理容器可见
                                            reasoningDiv.style.display = 'block';
                                            reasoningDiv.style.visibility = 'visible';

                                            // 添加到推理过程容器 - 修复段落问题
                                            if (reasoningDiv.innerHTML.trim() === '' || reasoningDiv.innerHTML.includes('正在连接DeepSeek-R1')) {
                                                // 初始化推理容器，使用单个段落
                                                reasoningDiv.innerHTML = '<div class="reasoning-content"><div class="reasoning-text" style="line-height: 1.6; white-space: pre-wrap; word-wrap: break-word;">' + formattedContent + '</div></div>';
                                                console.log('🆕 创建新的推理内容容器');
                                            } else {
                                                // 追加到现有内容 - 直接追加到文本容器，不创建新段落
                                                const reasoningText = reasoningDiv.querySelector('.reasoning-content .reasoning-text');
                                                if (reasoningText) {
                                                    reasoningText.innerHTML += formattedContent;
                                                    console.log('➕ 追加到现有文本');
                                                } else {
                                                    // 如果找不到文本容器，创建一个
                                                    const reasoningContent = reasoningDiv.querySelector('.reasoning-content');
                                                    if (reasoningContent) {
                                                        reasoningContent.innerHTML += '<div class="reasoning-text" style="line-height: 1.6; white-space: pre-wrap; word-wrap: break-word;">' + formattedContent + '</div>';
                                                    } else {
                                                        reasoningDiv.innerHTML += '<div class="reasoning-content"><div class="reasoning-text" style="line-height: 1.6; white-space: pre-wrap; word-wrap: break-word;">' + formattedContent + '</div></div>';
                                                    }
                                                    console.log('➕ 创建新的文本容器');
                                                }
                                            }
                                            reasoningDiv.scrollTop = reasoningDiv.scrollHeight;

                                            console.log('✅ 推理内容已添加，当前HTML长度:', reasoningDiv.innerHTML.length);
                                            console.log('✅ 推理容器状态:', {
                                                display: reasoningDiv.style.display,
                                                visibility: reasoningDiv.style.visibility,
                                                offsetHeight: reasoningDiv.offsetHeight,
                                                scrollHeight: reasoningDiv.scrollHeight
                                            });
                                        } catch (error) {
                                            console.error('❌ 添加推理内容到DOM时出错:', error);
                                        }
                                    } else {
                                        console.log('❌ 推理内容未添加:', {
                                            reasoningDiv存在: !!reasoningDiv,
                                            cleanContent存在: !!cleanContent,
                                            cleanContent长度: cleanContent ? cleanContent.length : 0,
                                            cleanContent内容: cleanContent
                                        });
                                    }
                                } else if (data.type === 'final') {
                                    // 处理最终答案内容
                                    if (showThinking) {
                                        // DeepSeek-R1模式：显示在最终分析区域
                                        if (finalContainer && (!finalContainer.style.display || finalContainer.style.display === 'none')) {
                                            finalContainer.style.display = 'block';
                                        }
                                    }

                                    // 最终分析内容 - 保持原始格式，最小化处理
                                    let cleanFinalContent = data.content;

                                    // 只移除明显的标签，保持内容结构
                                    cleanFinalContent = cleanFinalContent
                                        .replace(/<answer>/gi, '')  // 移除answer标签
                                        .replace(/<\/answer>/gi, '')
                                        .replace(/<think>.*?<\/think>/gs, '')  // 移除think标签
                                        .replace(/<thinking>.*?<\/thinking>/gs, '')  // 移除thinking标签
                                        .trim();

                                    // 简单格式化，保持原始结构
                                    const formatFinalAnalysis = (text) => {
                                        // 保持原始段落结构，只做基本HTML转换
                                        return text
                                            .replace(/\n\n+/g, '</p><p class="analysis-paragraph">')  // 段落分隔
                                            .replace(/\n/g, '<br>')  // 保持换行
                                            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');  // 保留粗体
                                    };

                                    const formattedFinal = formatFinalAnalysis(cleanFinalContent);

                                    if (finalDiv && cleanFinalContent.length > 0) {
                                        // 如果是第一次添加内容，包装在段落标签中
                                        if (finalDiv.innerHTML.trim() === '') {
                                            finalDiv.innerHTML = '<p>' + formattedFinal + '</p>';
                                        } else {
                                            finalDiv.innerHTML += formattedFinal;
                                        }
                                        finalDiv.scrollTop = finalDiv.scrollHeight;
                                    } else if (!finalDiv) {
                                        // 如果finalDiv不存在，尝试重新获取
                                        console.log('⚠️ finalDiv为null，尝试重新获取...');
                                        finalDiv = document.getElementById(showThinking ? 'final-stream-r1' : 'final-stream-v3');
                                        if (finalDiv && cleanFinalContent.length > 0) {
                                            finalDiv.innerHTML = '<p>' + formattedFinal + '</p>';
                                            finalDiv.scrollTop = finalDiv.scrollHeight;
                                            console.log('✅ 重新获取finalDiv成功');
                                        } else {
                                            console.error('❌ 无法获取finalDiv元素，DeepSeek-V3显示失败');
                                        }
                                    }
                                } else if (data.type === 'complete') {
                                    console.log('🏁 流式分析完成，应用专业报告格式');

                                    // 使用格式化的专业报告内容替换原始内容
                                    if (data.final && finalDiv) {
                                        console.log('📋 应用格式化的最终分析报告');
                                        finalDiv.innerHTML = `<div class="analysis-content">${data.final}</div>`;
                                    }

                                    if (data.reasoning && showThinking && reasoningDiv) {
                                        console.log('🧠 应用格式化的推理过程');
                                        reasoningDiv.innerHTML = `<div class="reasoning-content">${data.reasoning}</div>`;
                                    }

                                    // 确保所有内容都已显示
                                    if (showThinking && reasoningDiv) {
                                        reasoningDiv.scrollTop = reasoningDiv.scrollHeight;
                                        if (finalDiv && finalContainer && finalContainer.style.display !== 'none') {
                                            finalDiv.scrollTop = finalDiv.scrollHeight;
                                        }
                                    } else if (finalDiv) {
                                        finalDiv.scrollTop = finalDiv.scrollHeight;
                                    }
                                    break;
                                } else if (data.type === 'error') {
                                    console.error('❌ 流式错误:', data.message);
                                    const errorMsg = `<br><br><span style="color: red;">❌ 错误: ${data.message}</span>`;
                                    if (showThinking && reasoningDiv) {
                                        reasoningDiv.innerHTML += errorMsg;
                                    } else if (finalDiv) {
                                        finalDiv.innerHTML += errorMsg;
                                    }
                                    break;
                                }
                            } catch (e) {
                                console.error('❌ 解析流式数据失败:', e, '原始数据:', line);
                            }
                        }
                    }
                }

            } catch (error) {
                console.error('流式分析失败:', error);
                aiAnalysisContent.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ 流式分析失败:</strong> ${error.message}
                        <br><small>请尝试使用DeepSeek-V3模式或检查网络连接</small>
                    </div>
                `;
            }
        }

        // 工具函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 推理过程展开/收起功能
        function toggleReasoningExpand(button) {
            const reasoningDiv = button.closest('.mb-3').querySelector('.reasoning-process');
            const icon = button.querySelector('i');

            if (reasoningDiv.style.maxHeight === 'none') {
                reasoningDiv.style.maxHeight = '400px';
                icon.className = 'fas fa-expand';
            } else {
                reasoningDiv.style.maxHeight = 'none';
                icon.className = 'fas fa-compress';
            }
        }

        // 复制推理过程
        function copyReasoningProcess(text) {
            navigator.clipboard.writeText(text).then(() => {
                // 显示复制成功提示
                const toast = document.createElement('div');
                toast.className = 'alert alert-success position-fixed';
                toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; opacity: 0.9;';
                toast.innerHTML = '<i class="fas fa-check"></i> 思考过程已复制到剪贴板';
                document.body.appendChild(toast);

                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动选择文本复制');
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，初始化标签页功能');

            // 初始化AI分析功能
            initModelSelection();
            initAIAnalysis();

            // 为导航链接添加点击事件
            const navLinks = document.querySelectorAll('[data-tab]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tabId = this.getAttribute('data-tab');
                    console.log('点击标签页:', tabId);
                    showTab(tabId);

                    // 如果切换到知识库标签，重新初始化搜索功能
                    if (tabId === 'knowledge-base') {
                        setTimeout(() => {
                            console.log('🔄 重新初始化知识库搜索功能');
                            const knowledgeTab = document.getElementById('knowledge-base');
                            console.log('🔍 知识库标签页状态:', {
                                exists: !!knowledgeTab,
                                visible: knowledgeTab ? knowledgeTab.style.display !== 'none' : false,
                                hasActiveClass: knowledgeTab ? knowledgeTab.classList.contains('active') : false
                            });

                            // 强制重建DOM结构
                            console.log('🔧 强制重建搜索结果DOM结构');
                            window.recreateSearchResultsDOM();

                            // 强制初始化，不依赖active类
                            const success = initKnowledgeSearch();
                            if (!success) {
                                console.warn('知识库搜索初始化失败，尝试强制初始化');
                                // 强制初始化搜索功能
                                window.forceInitKnowledgeSearch();
                            }
                        }, 300); // 增加延迟确保DOM完全显示
                    }

                    // 🔧 如果切换到设备管理标签，初始化设备管理功能
                    if (tabId === 'equipment-management') {
                        setTimeout(() => {
                            console.log('🔄 初始化设备管理功能');

                            // 加载设备列表
                            if (typeof loadEquipmentList === 'function') {
                                loadEquipmentList();
                            } else {
                                console.warn('loadEquipmentList函数不存在，尝试从main.js加载');
                                // 如果main.js中的函数存在，调用它
                                if (window.loadEquipmentList) {
                                    window.loadEquipmentList();
                                }
                            }

                            // 初始化设备搜索
                            const equipmentSearch = document.getElementById('equipment-search');
                            if (equipmentSearch && !equipmentSearch.hasAttribute('data-initialized')) {
                                equipmentSearch.addEventListener('input', function(e) {
                                    const searchTerm = e.target.value.toLowerCase();
                                    console.log('🔍 设备搜索:', searchTerm);

                                    // 调用搜索处理函数
                                    if (typeof handleEquipmentSearch === 'function') {
                                        handleEquipmentSearch(e);
                                    } else if (window.handleEquipmentSearch) {
                                        window.handleEquipmentSearch(e);
                                    }
                                });
                                equipmentSearch.setAttribute('data-initialized', 'true');
                            }
                        }, 200);
                    }
                });
            });

            // 默认显示第一个标签页
            showTab('fault-analysis');

            console.log('标签页初始化完成，共', navLinks.length, '个标签页');

            // 🔧 修复设备表单提交处理
            const equipmentForm = document.getElementById('equipment-form');
            if (equipmentForm) {
                equipmentForm.addEventListener('submit', async function(e) {
                    e.preventDefault();
                    console.log('🔧 处理设备表单提交');

                    try {
                        // 获取表单数据
                        const formData = {
                            name: document.getElementById('equipment-name').value,
                            type: document.getElementById('equipment-type').value,
                            location: document.getElementById('equipment-location').value,
                            status: document.getElementById('equipment-status').value
                        };

                        // 验证必填字段
                        if (!formData.name || !formData.type || !formData.location || !formData.status) {
                            showAlert('请填写所有必填字段', 'warning');
                            return;
                        }

                        // 显示加载状态
                        showAlert('正在添加设备...', 'info');

                        // 发送请求
                        const response = await fetch('/api/v1/equipment', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(formData)
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const result = await response.json();

                        if (result.success) {
                            showAlert('设备添加成功', 'success');

                            // 清空表单
                            equipmentForm.reset();

                            // 重新加载设备列表
                            if (typeof loadEquipmentList === 'function') {
                                loadEquipmentList();
                            }
                        } else {
                            showAlert(result.error || '添加设备失败', 'danger');
                        }

                    } catch (error) {
                        console.error('添加设备失败:', error);
                        showAlert('添加设备失败，请稍后重试', 'danger');
                    }
                });
            }


            // DOM元素检查函数（宽松版本）
            function checkKnowledgeBaseDOM() {
                // 只检查最基本的必需元素
                const coreElements = [
                    'knowledge-search-form',
                    'search-query',
                    'search-type',
                    'knowledge-search-results'
                ];

                // 检查知识库标签页是否存在
                const knowledgeTab = document.getElementById('knowledge-base');
                if (!knowledgeTab) {
                    console.warn('知识库标签页不存在');
                    return false;
                }

                // 检查核心DOM元素
                const missingElements = [];
                for (const id of coreElements) {
                    if (!document.getElementById(id)) {
                        missingElements.push(id);
                    }
                }

                if (missingElements.length > 0) {
                    console.warn('缺少DOM元素:', missingElements);
                    return false;
                }

                return true;
            }

            // 知识库搜索初始化函数
            function initKnowledgeSearch() {
                try {
                    // 检查知识库标签页是否存在和可见
                    const knowledgeTab = document.getElementById('knowledge-base');
                    if (!knowledgeTab) {
                        console.warn('知识库标签页不存在');
                        return false;
                    }

                    // 检查标签页是否可见（不检查active类，因为可能还没设置）
                    const isVisible = knowledgeTab.style.display !== 'none' &&
                                     !knowledgeTab.classList.contains('d-none');

                    if (!isVisible) {
                        console.log('知识库标签页不可见，跳过初始化');
                        return false;
                    }

                    // 检查基本DOM元素（放宽检查条件）
                    const searchForm = document.getElementById('knowledge-search-form');
                    const searchQuery = document.getElementById('search-query');
                    const searchResults = document.getElementById('knowledge-search-results');

                    if (!searchForm || !searchQuery || !searchResults) {
                        console.warn('基本DOM元素缺失，延迟初始化');
                        return false;
                    }

                    const knowledgeSearchForm = document.getElementById('knowledge-search-form');
                    if (!knowledgeSearchForm) {
                        return false;
                    }
                    // 监听搜索类型变化
                    const searchTypeSelect = document.getElementById('search-type');
                    if (searchTypeSelect) {
                        searchTypeSelect.addEventListener('change', function() {
                            const selectedType = this.value;
                            const statusDiv = document.getElementById('knowledge-enhanced-search-status');
                            if (statusDiv) {
                                if (selectedType === 'enhanced') {
                                    statusDiv.classList.remove('d-none');
                                } else {
                                    statusDiv.classList.add('d-none');
                                }
                            }
                        });
                    }

                    knowledgeSearchForm.addEventListener('submit', function(e) {
                        e.preventDefault();

                        const query = document.getElementById('search-query').value.trim();
                        const searchType = document.getElementById('search-type').value;

                        if (!query) {
                            alert('请输入搜索内容');
                            return;
                        }

                        // 根据搜索类型调用不同的搜索函数
                        switch(searchType) {
                            case 'enhanced':
                                window.performEnhancedKnowledgeSearch(query);
                                break;
                            case 'text':
                                window.performTextKnowledgeSearch(query);
                                break;
                            case 'multimodal':
                                window.performMultimodalKnowledgeSearch(query);
                                break;
                            case 'semantic':
                                window.performSemanticKnowledgeSearch(query);
                                break;
                            default:
                                window.performDefaultKnowledgeSearch(query, searchType);
                        }
                    });

                    return true;
                } catch (error) {
                    return false;
                }
            }

            // 强制初始化知识库搜索功能
            window.forceInitKnowledgeSearch = function() {
                try {
                    console.log('🔧 强制初始化知识库搜索功能');

                    // 确保DOM结构存在
                    const resultsContainer = document.getElementById('knowledge-search-results');
                    if (!resultsContainer) {
                        console.error('搜索结果容器不存在');
                        return false;
                    }

                    // 重新创建DOM结构
                    window.recreateSearchResultsDOM();

                    // 绑定搜索表单事件
                    const searchForm = document.getElementById('knowledge-search-form');
                    if (searchForm) {
                        // 移除旧的事件监听器
                        const newForm = searchForm.cloneNode(true);
                        searchForm.parentNode.replaceChild(newForm, searchForm);

                        // 添加新的事件监听器
                        newForm.addEventListener('submit', function(e) {
                            e.preventDefault();

                            const query = document.getElementById('search-query').value.trim();
                            const searchType = document.getElementById('search-type').value;

                            if (!query) {
                                alert('请输入搜索内容');
                                return;
                            }

                            // 根据搜索类型调用不同的搜索函数
                            switch(searchType) {
                                case 'enhanced':
                                    window.performEnhancedKnowledgeSearch(query);
                                    break;
                                case 'text':
                                    window.performTextKnowledgeSearch(query);
                                    break;
                                case 'multimodal':
                                    window.performMultimodalKnowledgeSearch(query);
                                    break;
                                case 'semantic':
                                    window.performSemanticKnowledgeSearch(query);
                                    break;
                                default:
                                    window.performDefaultKnowledgeSearch(query, searchType);
                            }
                        });

                        console.log('✅ 搜索表单事件已重新绑定');
                    }

                    // 绑定搜索类型变化事件
                    const searchTypeSelect = document.getElementById('search-type');
                    if (searchTypeSelect) {
                        searchTypeSelect.addEventListener('change', function() {
                            const selectedType = this.value;
                            const statusDiv = document.getElementById('knowledge-enhanced-search-status');
                            if (statusDiv) {
                                if (selectedType === 'enhanced') {
                                    statusDiv.classList.remove('d-none');
                                } else {
                                    statusDiv.classList.add('d-none');
                                }
                            }
                        });

                        // 触发一次变化事件以设置初始状态
                        searchTypeSelect.dispatchEvent(new Event('change'));
                    }

                    console.log('✅ 知识库搜索功能强制初始化完成');
                    return true;
                } catch (error) {
                    console.error('强制初始化失败:', error);
                    return false;
                }
            }

            // 增强知识库搜索功能 - 在DOMContentLoaded时调用
            try {
                // 先重建DOM结构，确保所有元素都存在
                console.log('🔧 页面加载时重建搜索结果DOM结构');
                window.recreateSearchResultsDOM();

                // 然后尝试初始化
                const success = initKnowledgeSearch();
                if (!success) {
                    console.log('初始化失败，将在切换到知识库标签页时重新初始化');
                }
            } catch (error) {
                console.warn('知识库搜索初始化错误:', error);
            }

            // 增强知识库搜索函数 - 设为全局函数
            window.performEnhancedKnowledgeSearch = function(query) {
                // 确保知识库标签页是激活的
                const knowledgeTab = document.getElementById('knowledge-base');
                if (!knowledgeTab || !knowledgeTab.classList.contains('active')) {
                    window.displayKnowledgeSearchError('请先切换到知识库标签页');
                    return;
                }

                // 显示加载状态
                window.showKnowledgeSearchLoading();

                fetch('/api/v1/knowledge/search/enhanced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        limit: 10,
                        search_type: 'advanced'
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    window.hideKnowledgeSearchLoading();

                    if (data && data.success) {
                        // 保存搜索结果到全局变量
                        window.currentSearchResults = data.results || [];
                        window.displayEnhancedKnowledgeResults(data);
                        const responseTime = (data.response_time && typeof data.response_time === 'number') ? `，用时 ${data.response_time.toFixed(3)}秒` : '';
                        const searchMode = (data.fallback === true) ? '降级搜索' : '增强搜索';
                        window.updateKnowledgeSearchStats(`${searchMode}找到 ${data.total || 0} 个结果${responseTime}`);

                        // 如果是降级搜索，显示提示
                        if (data.fallback === true) {
                            console.warn('使用降级搜索模式:', data.reason || '未知原因');
                        }
                    } else {
                        if (data && data.fallback === true) {
                            // 降级搜索也失败了
                            window.displayKnowledgeSearchError(`搜索失败: ${data.error || '未知错误'}。原因: ${data.reason || '未知'}`);

                            // 建议用户尝试其他搜索类型
                            const searchTypeSelect = document.getElementById('search-type');
                            if (searchTypeSelect && searchTypeSelect.value === 'enhanced') {
                                setTimeout(() => {
                                    if (confirm('增强搜索暂时不可用，是否切换到基础搜索模式？')) {
                                        searchTypeSelect.value = 'text';
                                        window.performDefaultKnowledgeSearch(query, 'text');
                                    }
                                }, 1000);
                            }
                        } else {
                            const errorMsg = (data && data.error) ? data.error : '未知错误';
                            window.displayKnowledgeSearchError('搜索失败: ' + errorMsg);
                        }
                    }
                })
                .catch(error => {
                    window.hideKnowledgeSearchLoading();
                    console.error('❌ 增强知识库搜索错误:', error);
                    console.error('错误类型:', error.name);
                    console.error('错误消息:', error.message);
                    console.error('错误堆栈:', error.stack);

                    // 检查是否是网络错误还是解析错误
                    if (error.name === 'SyntaxError') {
                        console.error('🔧 JSON解析错误 - 服务器响应格式不正确');
                        window.displayKnowledgeSearchError('服务器响应格式错误，请稍后重试');
                    } else if (error.message.includes('HTTP错误')) {
                        console.error('🔧 HTTP错误 - 服务器返回错误状态');
                        window.displayKnowledgeSearchError(`服务器错误: ${error.message}`);
                    } else {
                        console.error('🔧 网络或其他错误');
                        window.displayKnowledgeSearchError('搜索请求失败，请检查网络连接或稍后重试');
                    }

                    // 网络错误时自动尝试基础搜索
                    setTimeout(() => {
                        console.log('🔄 尝试使用基础搜索作为备选方案...');
                        window.performDefaultKnowledgeSearch(query, 'text');
                    }, 2000);
                });
            }

            // 默认知识库搜索函数 - 设为全局函数
            window.performDefaultKnowledgeSearch = function(query, searchType) {
                console.log(`执行默认知识库搜索: ${query}, 类型: ${searchType}`);

                window.showKnowledgeSearchLoading();

                fetch('/api/v1/knowledge/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        search_type: searchType,
                        limit: 10
                    })
                })
                .then(response => response.json())
                .then(data => {
                    window.hideKnowledgeSearchLoading();

                    if (data.success) {
                        window.displayDefaultKnowledgeResults(data);
                        const searchMode = data.knowledge_base_available ? '基础知识库' : '文件搜索';
                        window.updateKnowledgeSearchStats(`${searchMode}找到 ${data.total || 0} 个结果`);

                        // 如果知识库不可用，显示提示
                        if (!data.knowledge_base_available) {
                            console.warn('基础知识库不可用，仅搜索上传文件');
                        }
                    } else {
                        window.displayKnowledgeSearchError('搜索失败: ' + (data.error || '未知错误'));

                        // 提供用户友好的错误提示
                        if (data.error && data.error.includes('知识库')) {
                            window.displayKnowledgeSearchError('知识库暂时不可用，请稍后重试或联系管理员');
                        }
                    }
                })
                .catch(error => {
                    window.hideKnowledgeSearchLoading();
                    console.error('知识库搜索错误:', error);
                    window.displayKnowledgeSearchError('搜索请求失败，请检查网络连接');
                });
            }

            // 显示增强搜索结果 - 设为全局函数
            window.displayEnhancedKnowledgeResults = function(data) {
                try {
                    console.log('🎯 显示增强搜索结果:', data);

                    // 检查知识库标签页是否存在（不强制要求active类）
                    const knowledgeTab = document.getElementById('knowledge-base');
                    if (!knowledgeTab) {
                        console.error('❌ 知识库标签页不存在');
                        window.displayKnowledgeSearchError('知识库页面未找到，请刷新页面');
                        return;
                    }

                    // 直接获取DOM元素，不等待
                    const enhancedResults = document.getElementById('enhanced-search-results');
                    const defaultResults = document.getElementById('default-search-results');
                    const resultsList = document.getElementById('enhanced-results-list');
                    const resultsCount = document.getElementById('enhanced-results-count');
                    const intentAnalysis = document.getElementById('enhanced-intent-analysis');
                    const intentDetails = document.getElementById('intent-details');

                    // 检查必需的DOM元素
                    console.log('🔍 DOM元素检查:');
                    console.log('enhancedResults:', enhancedResults);
                    console.log('defaultResults:', defaultResults);
                    console.log('resultsList:', resultsList);
                    console.log('resultsCount:', resultsCount);

                    // 如果缺少关键元素，尝试创建它们
                    if (!enhancedResults || !defaultResults) {
                        console.warn('⚠️ 关键DOM元素缺失，尝试重新创建');
                        window.recreateSearchResultsDOM();

                        // 重新获取元素
                        setTimeout(() => {
                            window.displayEnhancedKnowledgeResults(data);
                        }, 100);
                        return;
                    }

                    if (!resultsList || !resultsCount) {
                        console.warn('⚠️ 部分DOM元素缺失，使用降级显示');
                        window.displayFallbackResults(data);
                        return;
                    }

                    // 继续处理搜索结果
                    window.processEnhancedSearchResults(data, enhancedResults, defaultResults, resultsList, resultsCount, intentAnalysis, intentDetails);
                } catch (error) {
                    console.error('❌ 显示增强搜索结果错误:', error);
                    window.displayKnowledgeSearchError('显示搜索结果时发生错误，请重试');
                }
            }

            // 处理增强搜索结果的实际逻辑
            window.processEnhancedSearchResults = function(data, enhancedResults, defaultResults, resultsList, resultsCount, intentAnalysis, intentDetails) {
                try {

                // 显示增强结果区域
                enhancedResults.classList.remove('d-none');
                defaultResults.classList.add('d-none');

                // 更新结果数量
                resultsCount.textContent = data.total || 0;

                // 显示意图分析
                if (data.intent) {
                    const intent = data.intent;
                    let intentText = [];

                    if (intent.equipment_types && intent.equipment_types.length > 0) {
                        intentText.push(`设备类型: ${intent.equipment_types.join(', ')}`);
                    }
                    if (intent.fault_types && intent.fault_types.length > 0) {
                        intentText.push(`故障类型: ${intent.fault_types.join(', ')}`);
                    }
                    if (intent.voltage_levels && intent.voltage_levels.length > 0) {
                        intentText.push(`电压等级: ${intent.voltage_levels.join(', ')}`);
                    }
                    if (intent.locations && intent.locations.length > 0) {
                        intentText.push(`地区: ${intent.locations.join(', ')}`);
                    }

                    if (intentText.length > 0) {
                        intentDetails.textContent = intentText.join(' | ');
                        intentAnalysis.classList.remove('d-none');
                    } else {
                        intentAnalysis.classList.add('d-none');
                    }
                } else {
                    intentAnalysis.classList.add('d-none');
                }

                // 显示搜索结果
                if (!data.results || data.results.length === 0) {
                    resultsList.innerHTML = '<p class="text-muted">没有找到相关结果</p>';
                    return;
                }

                let html = '';
                data.results.forEach((result, index) => {
                    try {
                        const metadata = result.metadata || {};
                        const score = (result.score * 100).toFixed(1);

                        // 安全处理ID和文本内容
                        const safeId = (result.id || '').replace(/'/g, "\\'");
                        const safeTitle = (result.title || '文档 ' + (index + 1)).replace(/'/g, "\\'").replace(/"/g, '\\"');
                        const safeContent = (result.content || '').substring(0, 200).replace(/'/g, "\\'").replace(/"/g, '\\"');

                        html += `
                            <div class="card mb-3 search-result-card" style="cursor: pointer;" onclick="window.showKnowledgeDocumentDetail('${safeId}')"
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h6 class="card-title mb-0">
                                            🔍 ${safeTitle}
                                        </h6>
                                        <span class="badge bg-success">评分: ${score}%</span>
                                    </div>
                                    <p class="card-text text-muted small">
                                        ${safeContent}...
                                    </p>
                                    <div class="d-flex flex-wrap gap-1">
                                        <span class="badge bg-primary">${metadata.equipment_type || '通用'}</span>
                                        <span class="badge bg-info">${metadata.voltage_level || '未知电压'}</span>
                                        <span class="badge bg-secondary">${metadata.fault_type || '一般'}</span>
                                        <span class="badge bg-warning text-dark">${result.type || 'advanced_rag'}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    } catch (itemError) {
                        console.error('处理搜索结果项时出错:', itemError, result);
                        // 跳过有问题的结果项
                    }
                });

                resultsList.innerHTML = html;

                } catch (error) {
                    console.error('❌ 显示增强搜索结果错误:', error);
                    window.displayKnowledgeSearchError('显示搜索结果时发生错误，请重试');
                }
            }

            // 重新创建搜索结果DOM结构
            window.recreateSearchResultsDOM = function() {
                try {
                    const resultsContainer = document.getElementById('knowledge-search-results');
                    if (!resultsContainer) {
                        console.error('搜索结果容器不存在');
                        return;
                    }

                    resultsContainer.innerHTML = `
                        <!-- 增强搜索结果显示区域 -->
                        <div id="enhanced-search-results" class="d-none">
                            <div class="alert alert-success">
                                <strong>🚀 增强搜索结果</strong>
                                <span id="enhanced-results-count" class="badge bg-primary ms-2">0</span>
                            </div>
                            <div id="enhanced-intent-analysis" class="alert alert-info d-none">
                                <strong>🧠 智能分析:</strong>
                                <span id="intent-details"></span>
                            </div>
                            <div id="enhanced-results-list"></div>
                        </div>

                        <!-- 文本搜索结果显示区域 -->
                        <div id="text-search-results" class="d-none">
                            <div class="alert alert-primary">
                                <strong>📝 文本搜索结果</strong>
                                <span id="text-results-count" class="badge bg-secondary ms-2">0</span>
                            </div>
                            <div id="text-results-list"></div>
                        </div>

                        <!-- 多模态搜索结果显示区域 -->
                        <div id="multimodal-search-results" class="d-none">
                            <div class="alert alert-warning">
                                <strong>🎯 多模态搜索结果</strong>
                                <span id="multimodal-results-count" class="badge bg-info ms-2">0</span>
                            </div>
                            <div id="multimodal-analysis" class="alert alert-info d-none">
                                <strong>🔍 多模态分析:</strong>
                                <span id="multimodal-details"></span>
                            </div>
                            <div id="multimodal-results-list"></div>
                        </div>

                        <!-- 语义搜索结果显示区域 -->
                        <div id="semantic-search-results" class="d-none">
                            <div class="alert alert-info">
                                <strong>🧠 语义搜索结果</strong>
                                <span id="semantic-results-count" class="badge bg-success ms-2">0</span>
                            </div>
                            <div id="semantic-analysis" class="alert alert-light d-none">
                                <strong>💡 语义分析:</strong>
                                <span id="semantic-details"></span>
                            </div>
                            <div id="semantic-results-list"></div>
                        </div>

                        <!-- 默认搜索结果显示区域 -->
                        <div id="default-search-results">
                            <div class="text-center text-muted">
                                <i class="bi bi-search" style="font-size: 3rem;"></i>
                                <p class="mt-2">请输入搜索内容</p>
                                <small>💡 建议使用"🚀 增强搜索"获得更好的结果</small>
                            </div>
                        </div>
                    `;

                    console.log('✅ 搜索结果DOM结构已重新创建');
                } catch (error) {
                    console.error('重新创建DOM结构失败:', error);
                }
            }

            // 降级显示搜索结果
            window.displayFallbackResults = function(data) {
                try {
                    const resultsContainer = document.getElementById('knowledge-search-results');
                    if (!resultsContainer) {
                        console.error('搜索结果容器不存在');
                        return;
                    }

                    // 根据搜索类型显示不同的标题
                    let title = '🚀 增强搜索结果';
                    if (data.search_type) {
                        switch(data.search_type) {
                            case 'text':
                                title = '📝 文本搜索结果';
                                break;
                            case 'multimodal':
                                title = '🎯 多模态搜索结果';
                                break;
                            case 'semantic':
                                title = '🧠 语义搜索结果';
                                break;
                            case 'enhanced':
                                title = '🚀 增强搜索结果';
                                break;
                            default:
                                title = '🔍 知识库搜索结果';
                        }
                    }

                    let html = `<div class="alert alert-info">${title}</div>`;

                    if (data.results && data.results.length > 0) {
                        html += '<div class="list-group">';
                        data.results.forEach((result, index) => {
                            // 安全处理ID和文本内容
                            const safeId = (result.id || '').replace(/'/g, "\\'");
                            const safeTitle = (result.title || '无标题').replace(/'/g, "\\'").replace(/"/g, '\\"');
                            const safeContent = (result.content || '无内容').substring(0, 200).replace(/'/g, "\\'").replace(/"/g, '\\"');
                            const safeSource = (result.source || '未知').replace(/'/g, "\\'").replace(/"/g, '\\"');

                            html += `
                                <div class="list-group-item list-group-item-action" onclick="window.showKnowledgeDocumentDetail('${safeId}')" style="cursor: pointer;">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">${safeTitle}</h6>
                                        <small>评分: ${result.score ? (result.score * 100).toFixed(1) : 'N/A'}%</small>
                                    </div>
                                    <p class="mb-1">${safeContent}${result.content && result.content.length > 200 ? '...' : ''}</p>
                                    <small class="text-muted">来源: ${safeSource}</small>
                                </div>
                            `;
                        });
                        html += '</div>';
                    } else {
                        html += '<p class="text-muted">没有找到相关结果</p>';
                    }

                    resultsContainer.innerHTML = html;
                    console.log(`✅ 使用降级模式显示${title}`);
                } catch (error) {
                    console.error('降级显示失败:', error);
                    window.displayKnowledgeSearchError('显示搜索结果失败');
                }
            }

            // 显示默认搜索结果 - 设为全局函数
            window.displayDefaultKnowledgeResults = function(data) {
                try {
                    console.log('📋 显示默认搜索结果:', data);

                    const enhancedResults = document.getElementById('enhanced-search-results');
                    const defaultResults = document.getElementById('default-search-results');

                    console.log('🔍 默认搜索DOM元素检查:');
                    console.log('enhancedResults:', enhancedResults);
                    console.log('defaultResults:', defaultResults);

                    if (!enhancedResults || !defaultResults) {
                        console.error('❌ 缺少必需的DOM元素');
                        console.error('缺少的元素:', {
                            enhancedResults: !enhancedResults,
                            defaultResults: !defaultResults
                        });
                        window.displayKnowledgeSearchError('页面元素加载错误，请刷新页面重试');
                        return;
                    }

                // 显示默认结果区域
                enhancedResults.classList.add('d-none');
                defaultResults.classList.remove('d-none');

                if (!data.results || data.results.length === 0) {
                    defaultResults.innerHTML = '<p class="text-muted">没有找到相关结果</p>';
                    return;
                }

                let html = '<div class="list-group">';
                data.results.forEach((result, index) => {
                    try {
                        // 安全处理ID和文本内容
                        const safeId = (result.id || '').replace(/'/g, "\\'");
                        const safeTitle = (result.title || '文档 ' + (index + 1)).replace(/'/g, "\\'").replace(/"/g, '\\"');
                        const safeContent = (result.content || '').substring(0, 150).replace(/'/g, "\\'").replace(/"/g, '\\"');
                        const safeSource = (result.source || '知识库').replace(/'/g, "\\'").replace(/"/g, '\\"');

                        html += `
                            <div class="list-group-item list-group-item-action" onclick="window.showKnowledgeDocumentDetail('${safeId}')" style="cursor: pointer;">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">${safeTitle}</h6>
                                    <small>评分: ${(result.score * 100).toFixed(1)}%</small>
                                </div>
                                <p class="mb-1">${safeContent}...</p>
                                <small class="text-muted">${safeSource}</small>
                            </div>
                        `;
                    } catch (itemError) {
                        console.error('处理默认搜索结果项时出错:', itemError, result);
                        // 跳过有问题的结果项
                    }
                });
                html += '</div>';

                defaultResults.innerHTML = html;

                } catch (error) {
                    console.error('❌ 显示默认搜索结果错误:', error);
                    window.displayKnowledgeSearchError('显示搜索结果时发生错误，请重试');
                }
            }

            // 显示知识库搜索加载状态 - 设为全局函数
            window.showKnowledgeSearchLoading = function() {
                const resultsDiv = document.getElementById('knowledge-search-results');
                resultsDiv.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">搜索中...</span>
                        </div>
                        <p class="mt-2">正在搜索知识库...</p>
                    </div>
                `;
            }

            // 隐藏知识库搜索加载状态 - 设为全局函数
            window.hideKnowledgeSearchLoading = function() {
                // 加载状态会被结果替换，无需特殊处理
            }

            // 显示知识库搜索错误 - 设为全局函数
            window.displayKnowledgeSearchError = function(message) {
                try {
                    const resultsDiv = document.getElementById('knowledge-search-results');
                    if (resultsDiv) {
                        resultsDiv.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                                ${message}
                            </div>
                        `;
                    } else {
                        console.error('知识库搜索错误:', message);
                        alert('搜索错误: ' + message);
                    }
                } catch (error) {
                    console.error('显示搜索错误时发生异常:', error);
                    alert('搜索错误: ' + message);
                }
            }

            // 更新知识库搜索统计 - 设为全局函数
            window.updateKnowledgeSearchStats = function(stats) {
                const statsDiv = document.getElementById('knowledge-search-stats');
                if (statsDiv) {
                    statsDiv.textContent = stats;
                }
            }

            // 显示知识库文档详情 - 设为全局函数（修复版本）
            window.showKnowledgeDocumentDetail = function(docId) {
                try {
                    console.log('显示知识库文档详情:', docId);

                    if (!docId || docId === 'undefined' || docId === '' || docId === 'null') {
                        console.error('文档ID无效:', docId);
                        alert('文档ID无效，无法显示详情');
                        return;
                    }

                // 创建纯JavaScript模态框（不依赖Bootstrap）
                const modalHtml = `
                    <div id="documentDetailModal" style="
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0,0,0,0.5);
                        z-index: 9999;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">
                        <div style="
                            background: white;
                            border-radius: 8px;
                            max-width: 90%;
                            max-height: 90%;
                            width: 800px;
                            overflow: hidden;
                            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                        ">
                            <div style="
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                padding: 15px 20px;
                                border-bottom: 1px solid #dee2e6;
                                background-color: #f8f9fa;
                            ">
                                <h5 style="margin: 0; color: #495057;">
                                    <span style="margin-right: 8px;">📄</span>文档详情
                                </h5>
                                <div style="display: flex; gap: 10px;">
                                    <button onclick="window.closeKnowledgeDocumentDetail()" style="
                                        background: #6c757d;
                                        color: white;
                                        border: none;
                                        padding: 6px 12px;
                                        border-radius: 4px;
                                        cursor: pointer;
                                        font-size: 14px;
                                    ">返回搜索结果</button>
                                    <button onclick="window.closeKnowledgeDocumentDetail()" style="
                                        background: none;
                                        border: none;
                                        font-size: 20px;
                                        cursor: pointer;
                                        color: #6c757d;
                                        padding: 0;
                                        width: 30px;
                                        height: 30px;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                    ">&times;</button>
                                </div>
                            </div>
                            <div id="documentDetailContent" style="
                                padding: 20px;
                                max-height: 70vh;
                                overflow-y: auto;
                            ">
                                <div style="text-align: center; padding: 40px;">
                                    <div style="
                                        border: 4px solid #f3f3f3;
                                        border-top: 4px solid #007bff;
                                        border-radius: 50%;
                                        width: 40px;
                                        height: 40px;
                                        animation: spin 1s linear infinite;
                                        margin: 0 auto 20px;
                                    "></div>
                                    <p style="color: #6c757d;">正在加载文档详情...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // 移除已存在的模态框
                const existingModal = document.getElementById('documentDetailModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // 添加CSS动画
                if (!document.getElementById('modal-spinner-style')) {
                    const style = document.createElement('style');
                    style.id = 'modal-spinner-style';
                    style.textContent = `
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    `;
                    document.head.appendChild(style);
                }

                // 添加新模态框到页面
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // 添加ESC键关闭功能
                const handleEscKey = function(event) {
                    if (event.key === 'Escape') {
                        window.closeKnowledgeDocumentDetail();
                    }
                };
                document.addEventListener('keydown', handleEscKey);

                // 点击背景关闭模态框
                const modal = document.getElementById('documentDetailModal');
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        window.closeKnowledgeDocumentDetail();
                    }
                });

                // 获取文档详情
                fetch(`/api/v1/knowledge/document/detail/${docId}`)
                    .then(response => response.json())
                    .then(data => {
                        const contentDiv = document.getElementById('documentDetailContent');

                        if (data.success && data.document) {
                            const doc = data.document;
                            const metadata = doc.metadata || {};

                            // 安全处理内容，避免XSS
                            const safeContent = (doc.content || '无内容').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                            const safeTitle = (doc.title || '文档标题').replace(/</g, '&lt;').replace(/>/g, '&gt;');
                            const safeSource = (doc.source || '未知').replace(/</g, '&lt;').replace(/>/g, '&gt;');

                            contentDiv.innerHTML = `
                                <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid #dee2e6;">
                                        <h6 style="margin: 0; color: #007bff; font-size: 18px;">📄 ${safeTitle}</h6>
                                        <span style="
                                            background: #17a2b8;
                                            color: white;
                                            padding: 4px 8px;
                                            border-radius: 4px;
                                            font-size: 12px;
                                        ">${doc.type || 'document'}</span>
                                    </div>

                                    <div style="margin-bottom: 20px;">
                                        <h6 style="color: #495057; margin-bottom: 10px;">📋 文档内容</h6>
                                        <div style="
                                            border: 1px solid #dee2e6;
                                            border-radius: 6px;
                                            padding: 15px;
                                            background-color: #f8f9fa;
                                            max-height: 400px;
                                            overflow-y: auto;
                                            font-family: 'Courier New', monospace;
                                            font-size: 14px;
                                            line-height: 1.5;
                                        ">
                                            <pre style="white-space: pre-wrap; margin: 0; font-family: inherit;">${safeContent}</pre>
                                        </div>
                                    </div>

                                    <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                                        <div style="flex: 1;">
                                            <h6 style="color: #495057; margin-bottom: 10px;">🏷️ 元数据</h6>
                                            <div style="display: flex; flex-wrap: wrap; gap: 6px;">
                                                ${metadata.equipment_type ? `<span style="background: #007bff; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">${metadata.equipment_type}</span>` : ''}
                                                ${metadata.fault_type ? `<span style="background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">${metadata.fault_type}</span>` : ''}
                                                ${metadata.location ? `<span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">${metadata.location}</span>` : ''}
                                                ${metadata.voltage_level ? `<span style="background: #ffc107; color: #212529; padding: 2px 6px; border-radius: 3px; font-size: 12px;">${metadata.voltage_level}</span>` : ''}
                                                ${metadata.data_type ? `<span style="background: #6c757d; color: white; padding: 2px 6px; border-radius: 3px; font-size: 12px;">${metadata.data_type}</span>` : ''}
                                            </div>
                                        </div>
                                        <div style="flex: 1;">
                                            <h6 style="color: #495057; margin-bottom: 10px;">📊 文档信息</h6>
                                            <div style="font-size: 13px; color: #6c757d; line-height: 1.4;">
                                                <div style="margin-bottom: 4px;">来源: ${safeSource}</div>
                                                <div style="margin-bottom: 4px;">ID: ${doc.id}</div>
                                                ${metadata.added_at ? `<div>添加时间: ${new Date(metadata.added_at).toLocaleString()}</div>` : ''}
                                            </div>
                                        </div>
                                    </div>

                                    <div style="text-align: center; padding-top: 15px; border-top: 1px solid #dee2e6;">
                                        <button onclick="navigator.clipboard.writeText('${safeContent.replace(/'/g, '\\\'')}')" style="
                                            background: #28a745;
                                            color: white;
                                            border: none;
                                            padding: 8px 16px;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            margin-right: 10px;
                                            font-size: 14px;
                                        ">📋 复制内容</button>
                                        <button onclick="window.closeKnowledgeDocumentDetail()" style="
                                            background: #007bff;
                                            color: white;
                                            border: none;
                                            padding: 8px 16px;
                                            border-radius: 4px;
                                            cursor: pointer;
                                            font-size: 14px;
                                        ">🔙 返回搜索</button>
                                    </div>
                                </div>
                            `;
                        } else {
                            contentDiv.innerHTML = `
                                <div style="
                                    background: #fff3cd;
                                    border: 1px solid #ffeaa7;
                                    color: #856404;
                                    padding: 15px;
                                    border-radius: 6px;
                                    text-align: center;
                                ">
                                    <span style="font-size: 20px; margin-right: 8px;">⚠️</span>
                                    ${data.error || '无法加载文档详情'}
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('获取文档详情失败:', error);
                        const contentDiv = document.getElementById('documentDetailContent');
                        contentDiv.innerHTML = `
                            <div style="
                                background: #f8d7da;
                                border: 1px solid #f5c6cb;
                                color: #721c24;
                                padding: 15px;
                                border-radius: 6px;
                                text-align: center;
                            ">
                                <span style="font-size: 20px; margin-right: 8px;">❌</span>
                                加载文档详情失败，请稍后重试
                            </div>
                        `;
                    });
                } catch (error) {
                    console.error('显示文档详情时发生错误:', error);
                    alert('显示文档详情时发生错误，请重试');
                }
            }

            // 关闭知识库文档详情模态框 - 设为全局函数
            window.closeKnowledgeDocumentDetail = function() {
                try {
                    const modal = document.getElementById('documentDetailModal');
                    if (modal) {
                        modal.remove();
                    }

                    // 移除ESC键监听器
                    document.removeEventListener('keydown', function(event) {
                        if (event.key === 'Escape') {
                            window.closeKnowledgeDocumentDetail();
                        }
                    });

                    console.log('✅ 文档详情模态框已关闭，返回知识库搜索页面');
                } catch (error) {
                    console.error('关闭文档详情模态框时发生错误:', error);
                }
            }


            // 确保所有链接和按钮都不会导致页面跳转
            const allLinks = document.querySelectorAll('a[href="#"], a[href=""]');
            allLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('链接点击被阻止，保持在主页面');
                });
            });

            // 全局页面跳转拦截 - 确保DeepSeek-V3始终在主页面执行
            window.addEventListener('beforeunload', function(e) {
                console.log('页面即将跳转，但DeepSeek-V3应该在主页面执行');
            });

            // 拦截所有可能的页面跳转
            const originalOpen = window.open;
            window.open = function(...args) {
                console.log('window.open被拦截，保持在主页面');
                return null;
            };

            // 拦截location变更
            const originalAssign = window.location.assign;
            window.location.assign = function(url) {
                console.log('location.assign被拦截，保持在主页面:', url);
            };

            const originalReplace = window.location.replace;
            window.location.replace = function(url) {
                console.log('location.replace被拦截，保持在主页面:', url);
            };

            // 显示系统就绪通知
            setTimeout(() => {
                if (window.EnhancedFeatures && window.EnhancedFeatures.showNotification) {
                    window.EnhancedFeatures.showNotification(
                        '系统就绪',
                        '故障分析智能助手已启动，所有功能正常运行',
                        'success',
                        3000
                    );
                }
            }, 1000);

            // 文本搜索函数
            window.performTextKnowledgeSearch = function(query) {
                console.log('执行文本搜索:', query);
                window.showKnowledgeSearchLoading();

                fetch('/api/v1/knowledge/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        search_type: 'text',
                        limit: 10
                    })
                })
                .then(response => response.json())
                .then(data => {
                    window.hideKnowledgeSearchLoading();
                    if (data.success) {
                        // 保存搜索结果到全局变量
                        window.currentSearchResults = data.results || [];
                        window.displayTextSearchResults(data);
                        window.updateKnowledgeSearchStats(`文本搜索找到 ${data.total || 0} 个结果`);
                    } else {
                        window.displayKnowledgeSearchError('文本搜索失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    window.hideKnowledgeSearchLoading();
                    console.error('文本搜索错误:', error);
                    window.displayKnowledgeSearchError('文本搜索请求失败，请检查网络连接');
                });
            }

            // 多模态搜索函数
            window.performMultimodalKnowledgeSearch = function(query) {
                console.log('执行多模态搜索:', query);
                window.showKnowledgeSearchLoading();

                fetch('/api/v1/knowledge/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        search_type: 'multimodal',
                        limit: 10
                    })
                })
                .then(response => response.json())
                .then(data => {
                    window.hideKnowledgeSearchLoading();
                    if (data.success) {
                        // 保存搜索结果到全局变量
                        window.currentSearchResults = data.results || [];
                        window.displayMultimodalSearchResults(data);
                        const searchMode = data.enhanced ? '多模态增强搜索' : '多模态基础搜索';
                        window.updateKnowledgeSearchStats(`${searchMode}找到 ${data.total || 0} 个结果`);
                    } else {
                        window.displayKnowledgeSearchError('多模态搜索失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    window.hideKnowledgeSearchLoading();
                    console.error('多模态搜索错误:', error);
                    window.displayKnowledgeSearchError('多模态搜索请求失败，请检查网络连接');
                });
            }

            // 语义搜索函数
            window.performSemanticKnowledgeSearch = function(query) {
                console.log('执行语义搜索:', query);
                window.showKnowledgeSearchLoading();

                fetch('/api/v1/knowledge/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        search_type: 'semantic',
                        limit: 10
                    })
                })
                .then(response => response.json())
                .then(data => {
                    window.hideKnowledgeSearchLoading();
                    if (data.success) {
                        // 保存搜索结果到全局变量
                        window.currentSearchResults = data.results || [];
                        window.displaySemanticSearchResults(data);
                        const searchMode = data.enhanced ? '语义增强搜索' : '语义基础搜索';
                        window.updateKnowledgeSearchStats(`${searchMode}找到 ${data.total || 0} 个结果`);
                    } else {
                        window.displayKnowledgeSearchError('语义搜索失败: ' + (data.error || '未知错误'));
                    }
                })
                .catch(error => {
                    window.hideKnowledgeSearchLoading();
                    console.error('语义搜索错误:', error);
                    window.displayKnowledgeSearchError('语义搜索请求失败，请检查网络连接');
                });
            }

            // 显示文本搜索结果
            window.displayTextSearchResults = function(data) {
                try {
                    console.log('📝 显示文本搜索结果:', data);

                    // 隐藏其他搜索结果区域
                    window.hideAllSearchResults();

                    const textResults = document.getElementById('text-search-results');
                    const textResultsList = document.getElementById('text-results-list');
                    const textResultsCount = document.getElementById('text-results-count');

                    if (!textResults || !textResultsList || !textResultsCount) {
                        console.warn('文本搜索DOM元素缺失，使用降级显示');
                        window.displayFallbackResults(data);
                        return;
                    }

                    // 显示文本搜索结果区域
                    textResults.classList.remove('d-none');
                    textResultsCount.textContent = data.total || 0;

                    if (!data.results || data.results.length === 0) {
                        textResultsList.innerHTML = '<p class="text-muted">没有找到相关结果</p>';
                        return;
                    }

                    let html = '';
                    data.results.forEach((result, index) => {
                        try {
                            const metadata = result.metadata || {};
                            const score = result.score ? (result.score * 100).toFixed(1) : 'N/A';

                            // 安全处理ID和文本内容
                            const safeId = (result.id || '').replace(/'/g, "\\'");
                            const safeTitle = (result.title || '文档 ' + (index + 1)).replace(/'/g, "\\'").replace(/"/g, '\\"');
                            const safeContent = (result.content || '').substring(0, 200).replace(/'/g, "\\'").replace(/"/g, '\\"');

                            html += `
                                <div class="card mb-3 search-result-card" style="cursor: pointer;" onclick="window.showKnowledgeDocumentDetail('${safeId}')">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">
                                                📝 ${safeTitle}
                                            </h6>
                                            <span class="badge bg-primary">评分: ${score}%</span>
                                        </div>
                                        <p class="card-text text-muted small">
                                            ${safeContent}...
                                        </p>
                                        <div class="d-flex flex-wrap gap-1">
                                            <span class="badge bg-primary">${metadata.equipment_type || '通用'}</span>
                                            <span class="badge bg-info">${metadata.fault_type || '一般'}</span>
                                            <span class="badge bg-secondary">${result.source || '文本搜索'}</span>
                                            <span class="badge bg-warning text-dark">${result.type || 'text'}</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } catch (itemError) {
                            console.error('处理文本搜索结果项时出错:', itemError, result);
                        }
                    });

                    textResultsList.innerHTML = html;
                } catch (error) {
                    console.error('显示文本搜索结果错误:', error);
                    window.displayKnowledgeSearchError('显示文本搜索结果时发生错误');
                }
            }

            // 显示多模态搜索结果
            window.displayMultimodalSearchResults = function(data) {
                try {
                    console.log('🎯 显示多模态搜索结果:', data);

                    // 隐藏其他搜索结果区域
                    window.hideAllSearchResults();

                    const multimodalResults = document.getElementById('multimodal-search-results');
                    const multimodalResultsList = document.getElementById('multimodal-results-list');
                    const multimodalResultsCount = document.getElementById('multimodal-results-count');
                    const multimodalAnalysis = document.getElementById('multimodal-analysis');
                    const multimodalDetails = document.getElementById('multimodal-details');

                    if (!multimodalResults || !multimodalResultsList || !multimodalResultsCount) {
                        console.warn('多模态搜索DOM元素缺失，使用降级显示');
                        window.displayFallbackResults(data);
                        return;
                    }

                    // 显示多模态搜索结果区域
                    multimodalResults.classList.remove('d-none');
                    multimodalResultsCount.textContent = data.total || 0;

                    // 显示多模态分析信息
                    if (multimodalAnalysis && multimodalDetails && data.features) {
                        multimodalDetails.textContent = `支持功能: ${data.features.join(', ')}`;
                        multimodalAnalysis.classList.remove('d-none');
                    }

                    if (!data.results || data.results.length === 0) {
                        multimodalResultsList.innerHTML = '<p class="text-muted">没有找到相关结果</p>';
                        return;
                    }

                    let html = '';
                    data.results.forEach((result, index) => {
                        try {
                            const metadata = result.metadata || {};
                            const score = result.score ? (result.score * 100).toFixed(1) : 'N/A';
                            const isImageResult = result.type === 'image' || result.source === '图像数据库';

                            // 安全处理ID和文本内容
                            const safeId = (result.id || '').replace(/'/g, "\\'");
                            const safeTitle = (result.title || '文档 ' + (index + 1)).replace(/'/g, "\\'").replace(/"/g, '\\"');
                            const safeContent = (result.content || '').substring(0, 200).replace(/'/g, "\\'").replace(/"/g, '\\"');

                            html += `
                                <div class="card mb-3 search-result-card" style="cursor: pointer;" onclick="window.showKnowledgeDocumentDetail('${safeId}')">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">
                                                ${isImageResult ? '🖼️ ' : '🎯 '}${safeTitle}
                                            </h6>
                                            <span class="badge bg-warning">评分: ${score}%</span>
                                        </div>
                                        <p class="card-text text-muted small">
                                            ${safeContent}...
                                        </p>
                                        <div class="d-flex flex-wrap gap-1">
                                            <span class="badge bg-primary">${metadata.equipment_type || '通用'}</span>
                                            <span class="badge bg-info">${metadata.fault_type || '一般'}</span>
                                            <span class="badge bg-secondary">${isImageResult ? '图像+文本' : '文本'}</span>
                                            <span class="badge bg-warning text-dark">${result.type || 'multimodal'}</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } catch (itemError) {
                            console.error('处理多模态搜索结果项时出错:', itemError, result);
                        }
                    });

                    multimodalResultsList.innerHTML = html;
                } catch (error) {
                    console.error('显示多模态搜索结果错误:', error);
                    window.displayKnowledgeSearchError('显示多模态搜索结果时发生错误');
                }
            }

            // 显示语义搜索结果
            window.displaySemanticSearchResults = function(data) {
                try {
                    console.log(' 显示语义搜索结果:', data);

                    // 隐藏其他搜索结果区域
                    window.hideAllSearchResults();

                    const semanticResults = document.getElementById('semantic-search-results');
                    const semanticResultsList = document.getElementById('semantic-results-list');
                    const semanticResultsCount = document.getElementById('semantic-results-count');
                    const semanticAnalysis = document.getElementById('semantic-analysis');
                    const semanticDetails = document.getElementById('semantic-details');

                    if (!semanticResults || !semanticResultsList || !semanticResultsCount) {
                        console.warn('语义搜索DOM元素缺失，使用降级显示');
                        window.displayFallbackResults(data);
                        return;
                    }

                    // 显示语义搜索结果区域
                    semanticResults.classList.remove('d-none');
                    semanticResultsCount.textContent = data.total || 0;

                    // 显示语义分析信息
                    if (semanticAnalysis && semanticDetails && data.features) {
                        semanticDetails.textContent = `语义特征: ${data.features.join(', ')}`;
                        semanticAnalysis.classList.remove('d-none');
                    }

                    if (!data.results || data.results.length === 0) {
                        semanticResultsList.innerHTML = '<p class="text-muted">没有找到相关结果</p>';
                        return;
                    }

                    let html = '';
                    data.results.forEach((result, index) => {
                        try {
                            const metadata = result.metadata || {};
                            const score = result.score ? (result.score * 100).toFixed(1) : 'N/A';

                            // 安全处理ID和文本内容
                            const safeId = (result.id || '').replace(/'/g, "\\'");
                            const safeTitle = (result.title || '文档 ' + (index + 1)).replace(/'/g, "\\'").replace(/"/g, '\\"');
                            const safeContent = (result.content || '').substring(0, 200).replace(/'/g, "\\'").replace(/"/g, '\\"');

                            html += `
                                <div class="card mb-3 search-result-card" style="cursor: pointer;" onclick="window.showKnowledgeDocumentDetail('${safeId}')">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0">
                                                🧠 ${safeTitle}
                                            </h6>
                                            <span class="badge bg-info">评分: ${score}%</span>
                                        </div>
                                        <p class="card-text text-muted small">
                                            ${safeContent}...
                                        </p>
                                        <div class="d-flex flex-wrap gap-1">
                                            <span class="badge bg-primary">${metadata.equipment_type || '通用'}</span>
                                            <span class="badge bg-info">${metadata.fault_type || '一般'}</span>
                                            <span class="badge bg-secondary">${result.source || '语义搜索'}</span>
                                            <span class="badge bg-success text-white">${result.type || 'semantic'}</span>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } catch (itemError) {
                            console.error('处理语义搜索结果项时出错:', itemError, result);
                        }
                    });

                    semanticResultsList.innerHTML = html;
                } catch (error) {
                    console.error('显示语义搜索结果错误:', error);
                    window.displayKnowledgeSearchError('显示语义搜索结果时发生错误');
                }
            }

            // 隐藏所有搜索结果区域
            window.hideAllSearchResults = function() {
                const resultAreas = [
                    'enhanced-search-results',
                    'text-search-results',
                    'multimodal-search-results',
                    'semantic-search-results',
                    'default-search-results'
                ];

                resultAreas.forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.classList.add('d-none');
                    }
                });
            }
        });

        // ==================== 知识库文档管理函数 ====================

        // 处理文档文件选择
        window.handleDocFileSelect = function(input) {
            // 首先调用modal版本的函数来设置全局变量
            if (typeof handleDocFileSelect_modal !== 'undefined') {
                handleDocFileSelect_modal(input);
            }

            const file = input.files[0];
            const fileInfo = document.getElementById('docFileInfo');
            const fileName = document.getElementById('docFileName');
            const fileSize = document.getElementById('docFileSize');
            const uploadArea = document.getElementById('uploadArea');

            if (file) {
                // 显示文件信息
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.classList.remove('d-none');

                // 更新上传区域样式
                uploadArea.style.borderColor = '#28a745';
                uploadArea.style.backgroundColor = '#f8f9fa';

                // 自动填充标题（如果为空）
                const titleInput = document.getElementById('doc-title');
                if (!titleInput.value) {
                    titleInput.value = file.name.replace(/\.[^/.]+$/, "");
                }

                // 如果是文本文件，尝试读取内容
                if (file.type.includes('text') || file.name.endsWith('.txt') || file.name.endsWith('.md')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const contentTextarea = document.getElementById('doc-content');
                        if (!contentTextarea.value) {
                            contentTextarea.value = e.target.result.substring(0, 2000);
                            updateContentLength(contentTextarea);
                        }
                    };
                    reader.readAsText(file);
                }
            } else {
                fileInfo.classList.add('d-none');
                uploadArea.style.borderColor = '';
                uploadArea.style.backgroundColor = '';
            }
        };

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 更新内容长度显示
        window.updateContentLength = function(textarea) {
            const lengthDisplay = document.getElementById('docContentLength');
            if (lengthDisplay) {
                lengthDisplay.textContent = textarea.value.length;
            }
        };

        // 更新图片进度条
        window.updateImgProgress = function(percentage) {
            const progressBar = document.getElementById('img-progress-bar');
            if (progressBar) {
                progressBar.style.width = percentage + '%';
                progressBar.setAttribute('aria-valuenow', percentage);
            }
        };

        // 处理图片文件选择
        window.handleImgFileSelect = function(input) {
            // 首先调用modal版本的函数来设置全局变量
            if (typeof handleImgFileSelect_modal !== 'undefined') {
                handleImgFileSelect_modal(input);
            }

            const previewContainer = document.getElementById('image-preview-container');
            const previewList = document.getElementById('image-preview-list');

            if (input.files && input.files.length > 0) {
                previewList.innerHTML = '';

                Array.from(input.files).forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewItem = document.createElement('div');
                            previewItem.className = 'col-6 col-md-4';
                            previewItem.innerHTML = `
                                <div class="card border-0 shadow-sm">
                                    <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;">
                                    <div class="card-body p-2">
                                        <small class="text-muted">${file.name}</small>
                                        <div class="small text-success">${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                                    </div>
                                </div>
                            `;
                            previewList.appendChild(previewItem);
                        };
                        reader.readAsDataURL(file);
                    }
                });

                previewContainer.classList.remove('d-none');

                // 更新上传区域样式
                const dropZone = document.getElementById('image-drop-zone');
                if (dropZone) {
                    dropZone.style.borderColor = '#28a745';
                    dropZone.style.backgroundColor = '#f8fff9';
                }

                // 自动填充标题（如果为空）
                const titleInput = document.getElementById('img-title');
                if (!titleInput.value && input.files[0]) {
                    titleInput.value = input.files[0].name.replace(/\.[^/.]+$/, "");
                }
            }
        };

        // 提取文档关键词功能已合并到main.js中的extractKeywords函数

        // 更新文档进度条
        window.updateDocProgress = function(percentage) {
            const progressBar = document.getElementById('doc-progress-bar');
            if (progressBar) {
                progressBar.style.width = percentage + '%';
                progressBar.setAttribute('aria-valuenow', percentage);
            }
        };

        // 清除文档文件
        window.clearDocFile = function() {
            const fileInput = document.getElementById('doc-file');
            const fileInfo = document.getElementById('docFileInfo');
            if (fileInput) fileInput.value = '';
            if (fileInfo) fileInfo.classList.add('d-none');
        };

        // 处理文档文件选择
        window.handleDocFileSelect = function(input) {
            const fileInfo = document.getElementById('docFileInfo');
            const fileName = document.getElementById('docFileName');
            const fileSize = document.getElementById('docFileSize');

            if (input.files && input.files[0]) {
                const file = input.files[0];
                const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);

                if (fileName) fileName.textContent = file.name;
                if (fileSize) fileSize.textContent = sizeInMB + ' MB';
                if (fileInfo) fileInfo.classList.remove('d-none');

                // 添加拖拽效果
                const uploadArea = document.getElementById('uploadArea');
                if (uploadArea) {
                    uploadArea.style.borderColor = '#28a745';
                    uploadArea.style.backgroundColor = '#f8fff9';
                }
            }
        };

        // 重复的函数定义已删除

        // 添加标签到输入框
        window.addTagToInput = function(tag) {
            const tagsInput = document.getElementById('doc-tags');
            const currentTags = tagsInput.value.split(',').map(t => t.trim()).filter(t => t);

            if (!currentTags.includes(tag)) {
                currentTags.push(tag);
                tagsInput.value = currentTags.join(', ');
            }
        };

        // 重置文档表单
        window.resetDocForm = function() {
            if (confirm('确定要重置表单吗？所有输入的内容将被清除。')) {
                document.getElementById('add-document-form').reset();
                document.getElementById('docFileInfo').classList.add('d-none');
                document.getElementById('suggestedTags').classList.add('d-none');
                document.getElementById('docContentLength').textContent = '0';

                // 重置上传区域样式
                const uploadArea = document.getElementById('uploadArea');
                uploadArea.style.borderColor = '';
                uploadArea.style.backgroundColor = '';

                showToast('表单已重置', 'info');
            }
        };

        // 保存文档功能已移至 modal-functions.js

        // 通用提示函数和标签页切换功能已移至 modal-functions.js

        // 🔥 页面加载完成后初始化标签页
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 DOM加载完成，初始化标签页系统...');
            initializeTabs();

            // 添加标签页点击事件监听器
            addTabEventListeners();

            // 延迟验证
            setTimeout(() => {
                console.log('🔍 验证标签页初始化结果:');
                validateTabFunctionality();
            }, 500);
        });

        // 🔥 添加标签页事件监听器
        function addTabEventListeners() {
            // 文档标签页事件
            document.querySelectorAll('#documentTabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetTab = this.id.replace('-tab', '');
                    console.log('📌 点击文档标签页:', targetTab);
                    switchDocTab(targetTab);
                });
            });

            // 图片标签页事件
            document.querySelectorAll('#imageTabs .nav-link').forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetTab = this.id.replace('-tab', '');
                    console.log('📌 点击图片标签页:', targetTab);
                    switchImgTab(targetTab);
                });
            });

            console.log('✅ 标签页事件监听器添加完成');
        }

        // 初始化标签页状态
        function initializeTabs() {
            console.log('初始化标签页状态...');

            // 确保文档模态框的第一个标签页是激活状态
            const docFirstTab = document.getElementById('doc-upload-tab');
            const docFirstPane = document.getElementById('doc-upload');
            if (docFirstTab && docFirstPane) {
                docFirstTab.classList.add('active');
                docFirstPane.classList.add('show', 'active');
            }

            // 确保图片模态框的第一个标签页是激活状态
            const imgFirstTab = document.getElementById('img-upload-tab');
            const imgFirstPane = document.getElementById('img-upload');
            if (imgFirstTab && imgFirstPane) {
                imgFirstTab.classList.add('active');
                imgFirstPane.classList.add('show', 'active');
            }
        }

        // 标签页重新初始化
        function reinitializeTabs() {
            console.log('重新初始化所有标签页...');

            // 重置所有模态框标签页状态
            document.querySelectorAll('.modal .tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            document.querySelectorAll('.modal .nav-link').forEach(tab => {
                tab.classList.remove('active');
            });

            // 激活默认标签页
            setTimeout(() => {
                // 文档模态框
                const docTab = document.getElementById('doc-upload-tab');
                const docPane = document.getElementById('doc-upload');
                if (docTab && docPane) {
                    docTab.classList.add('active');
                    docPane.classList.add('show', 'active');
                }

                // 图片模态框
                const imgTab = document.getElementById('img-upload-tab');
                const imgPane = document.getElementById('img-upload');
                if (imgTab && imgPane) {
                    imgTab.classList.add('active');
                    imgPane.classList.add('show', 'active');
                }
            }, 50);
        }







        // 暴露关键函数到全局作用域 - 保持原有功能
        window.switchDocTab = switchDocTab;
        window.switchImgTab = switchImgTab;
        window.reinitializeTabs = reinitializeTabs;
        window.forceRefreshTabs = forceRefreshTabs;

        // closeModal函数已在页面顶部定义

    </script>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

